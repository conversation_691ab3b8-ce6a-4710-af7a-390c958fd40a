using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Game.Battle;
using Game.Common;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.View
{
    public partial class battle_pve_fail_info : ViewBase
    {
        public override void OnAwake(int id, string name)
        {
            base.OnAwake(id, name);
            BindBtnsClick();
            InitList();

            if (uiObjs.selfRootObj != null)
            {
                SetTimelineUnit(uiObjs.selfRootObj.GetComponentInParent<TimelineUnit>(), "show");
            }
        }

        public override void OnShow(object parameters)
        {
            base.OnShow(parameters);

            // MODULE.UIBgCounter.ShowGaussBg(Id, 0.98f, true, false);
            InitEnhanceItemConfigData();
            RefreshList();
        }

        public override void OnClose()
        {
            // MODULE.UIBgCounter.HideGaussBg(Id);
            base.OnClose();
        }

        public override void Dispose()
        {
            _enhanceItemList?.Dispose();
        }
        

        private void BindBtnsClick()
        {
            if (this.uiObjs.backImgBtn != null)
            {
                this.uiObjs.backImgBtn.AddListener(OnBtnClickBackImg);
            }
        }

        private void OnBtnClickBackImg()
        {
            UnicornPveControl.GetInstance().QuitBattle();
            this.Close();
        }


        #region 跳转逻辑处理

        #region 数据结构

        public class enhancement_item : ViewNode
        {
            #region UI 控件定义

            private int _btn; // 整体按钮
            private int _icon; // 图标
            private int _text; // 描述内容

            private UIButtonEx _btnEx;

            /// <summary>
            /// UI控件名称常量
            /// </summary>
            static class ItemName
            {
                public const string Btn = "bg";
                public const string Icon = "icon";
                public const string Text = "text";
            }

            #endregion

            #region 初始化

            private ViewNode _hoster;

            public enhancement_item(ViewNode hoster, int id, string name) : base(id, name)
            {
                _hoster = hoster;
                Init();
                InitComp();
            }

            private void Init()
            {
                OnAttach(_hoster);
            }

            private void InitComp()
            {
                _btn = GetCompFromContainerKey(ItemName.Btn, UIControlType.UIButtonEx);
                _icon = GetCompFromContainerKey(ItemName.Icon, UIControlType.DynRawImage);
                _text = GetCompFromContainerKey(ItemName.Text, UIControlType.UI_TMP);

                _btnEx = GetUnityCompFromContainerByKey<UIButtonEx>(ItemName.Btn, UIControlType.UIButtonEx);
            }

            #endregion

            #region 表现设定

            private EnhancementItemData _enhancementItemData;

            public void SetItemData(EnhancementItemData enhancementItemData)
            {
                _enhancementItemData = enhancementItemData;
                RefreshItemUI();
            }

            private void RefreshItemUI()
            {
                SetDynTex(_icon, _enhancementItemData.Icon);
                SetTextValueTMP(_text, _enhancementItemData.Text);

                if (_btnEx != null)
                {
                    _btnEx.RemoveAllListener();
                    _btnEx.AddListener(_enhancementItemData.OnClickAction);
                }
            }

            #endregion
        }

        public class EnhancementItemData
        {
            public string Icon;
            public string Text;
            public UnityAction OnClickAction;
        }

        #endregion

        private SuperListViewContainer<enhancement_item, EnhancementItemData> _enhanceItemList;
        private List<EnhancementItemData> _allEnhancementItemData;

        private void InitList()
        {
            _enhanceItemList =
                new SuperListViewContainer<enhancement_item, EnhancementItemData>(this, ui.content, InitEnhanceItem);
        }

        private enhancement_item InitEnhanceItem(int pid, int itemIndex)
        {
            return new enhancement_item(this, pid, itemIndex.ToString());
        }

        private void RefreshList()
        {
            if (_enhanceItemList == null)
            {
                return;
            }

            _enhanceItemList.SetData(_allEnhancementItemData);
            _enhanceItemList.RgOnSetItemData(OnSetEnhanceItemData);
            _enhanceItemList.RefreshUI();
        }
        
        private void OnSetEnhanceItemData(enhancement_item item, EnhancementItemData data, int _id, int _index)
        {
            item.SetItemData(data);
        }

        private void InitEnhanceItemConfigData()
        {
            // 添加虚拟数据 - todo: 要读表
            _allEnhancementItemData = new List<EnhancementItemData>()
            {
                new EnhancementItemData()
                {
                    Icon = "ui_icon_tongyongjiasu",
                    Text = "测试数据1",
                    OnClickAction = delegate { Log.UI.Debug("click 1"); },
                },
                new EnhancementItemData()
                {
                    Icon = "ui_icon_tengman",
                    Text = "测试数据2",
                    OnClickAction = delegate { Log.UI.Debug("click 2"); },
                },
                new EnhancementItemData()
                {
                    Icon = "ui_icon_xinhaoqiang_chouka_5",
                    Text = "测试数据3",
                    OnClickAction = delegate { Log.UI.Debug("click 3"); },
                },
            };
        }

        #endregion
    }
}
