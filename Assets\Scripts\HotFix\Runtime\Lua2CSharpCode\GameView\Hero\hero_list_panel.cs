using Game.Common;
using Game.Data;
using Game.Module;
using System.Collections.Generic;
using System;
using UnityEngine;
using Game.Common.Logic.Event;
using Event = Game.Common.Logic.Event.Event;
using RedPointSystem;
using Game.Common.Check;

namespace Game.View
{
    public partial class hero_list_panel : ViewBase
    {
        public class FilterItem
        {
            public string name;
            public bool isChoose;
            public bool isFirst;
            public bool isLast;
        }
        private Components Comp;
        private HeroItemData __recruit_hero;
        private bool __reset_list_ui;
        private List<HeroItemData> __data;
        private List<FilterItem> _filterList;
        private bool _isFilterShow = false;
        private int _filterChooseIndex = 0;
        private Animator _filterAnim;

        private RedPointNode _recruitRedPointNode;
        
        private class Components
        {
            public CommonContainer<hero_list_head_item, HeroItemData> container;
            public CommonContainer<hero_list_filter_item, FilterItem> filterContainer;
        }

        public override void OnAwake(int id, string name)
        {
            base.OnAwake(id, name);

            Comp = new Components();
            Comp.container = new CommonContainer<hero_list_head_item, HeroItemData>(this, ui.container, GetHeroListHeadItem);
            // Comp.container.RgListener(new Dictionary<string, Delegate>
            // {
            //     { "OnSortItemData", DATA.Hero.Fn_HeroListSort }
            // });
            Comp.container.RgOnSetItemData((item, data, index) => { item.OnSetItemData(data, index); });
            
            Comp.filterContainer = new CommonContainer<hero_list_filter_item, FilterItem>(this, ui.filterChooseGroup, GetHeroListFilterItem);
            Comp.filterContainer.RgOnSetItemData((item, data, index) => { item.OnSetItemData(data, index); });
            Comp.filterContainer.RgOnSelectItem(OnSelectedFilterItem);

            _filterAnim = uiObjs.filterObj.GetComponent<Animator>();
            
            _filterList = new List<FilterItem>()
            {
                new FilterItem() {name="hero_level", isChoose = true, isFirst = true, isLast = false},
                new FilterItem() {name="hero_power2", isChoose = false, isFirst = false, isLast = false},
                new FilterItem() {name="hero_quality", isChoose = false, isFirst = false, isLast = false},
                new FilterItem() {name="hero_star_rating", isChoose = false, isFirst = false, isLast = true},
            };
            
            ButtonAddClick(ui.close_btn, ()=>Close());
            ButtonAddClick(ui.filterChooseBtn, OnFilterChooseBtnClicked);
            uiObjs.recruit_btn.onClick.AddListener(OnRecruitBtnClicked);
            uiObjs.eventTrigger.onClick = _ => OnClickedPanel();
            InitRedPoint();

            Event.Instance.AddListener<HeroItemData>(this, EventDefine.UI_HERO_ITEM_CLICKED, __OnItemClicked);
            Event.Instance.AddListener<HeroItemData>(this, EventDefine.HERO_ITEM_DATA_NEW, __OnGetNewHero);
        }

        public override void OnShow(object parameters)
        {
            base.OnShow(parameters);
            __data = DATA.Hero.GetAllHeroItemList();
            __recruit_hero = null;
            __SelectTab();
            SetRecruitButton();
            RefreshFilterContent();
            OnRedPointEvent(_recruitRedPointNode);
        }
        
        private void SetRecruitButton()
        {
            uiObjs.recruit_btn.gameObject.SetActive(MainPanelItemKey.IsOpen(MainPanelItemKey.recruit, false));
        }

        private void RefreshFilterContent()
        {
            SetScale(ui.filterChooseIcon, 1f, _isFilterShow ? -1f : 1f);
            SetTextValueTMP(uiObjs.filterChooseText, Localization.GetFormat(_filterList[_filterChooseIndex].name));
            for (int i = 0; i< _filterList.Count; i++)
            {
                _filterList[i].isChoose = _filterChooseIndex == i;
            }
            Comp.filterContainer.SetData(_filterList);
            Comp.filterContainer.RefreshUI();
            SortHeroListByType();
        }

        private void SortHeroListByType()
        {
            __data.Sort((x, y) => {
                if (x.IsCanRecruit() != y.IsCanRecruit())
                    return y.IsCanRecruit().CompareTo(x.IsCanRecruit()); // true comes first
                
                if (x.IsCanRecruit() && y.IsCanRecruit()) {
                    if (x.Quality != y.Quality) {
                         return y.Quality.CompareTo(x.Quality); // Higher quality comes first
                    }
                }
                
                if (x.IsHave != y.IsHave)
                    return y.IsHave.CompareTo(x.IsHave); 
                
                switch (_filterChooseIndex)
                {
                    case 0: 
                        return y.Level.CompareTo(x.Level);
                    case 1: 
                        return y.Power.CompareTo(x.Power); 
                    case 2: 
                        return y.Quality.CompareTo(x.Quality); 
                    case 3: 
                        return y.StarLv.CompareTo(x.StarLv);
                    default:
                        return 0;
                }
            });
            
            Comp.container.SetData(__data);
            Comp.container.RefreshUI();
        }
        
        private void __RefreshCurList()
        {
            var data = Comp.container.GetData();
            if (data != null)
            {
                SortHeroListByType();
                Comp.container.SetData(data);
                Comp.container.RefreshUI();
            }
        }

        private void OnClickedPanel()
        {
            if (_isFilterShow)
            {
                OnFilterChooseBtnClicked();
            }
        }

        private void InitRedPoint()
        {
            _recruitRedPointNode = GLOABAL.RED_CHECK.GetRedTree<CheckHero>(RedTreeType.Hero).GetRecruitRootNode();
            _recruitRedPointNode.AddListener(OnRedPointEvent);
            OnRedPointEvent(_recruitRedPointNode);
        }

        private void OnRedPointEvent(RedPointNode redPoint)
        {
            int unreadCount = 0;
            
            if (redPoint != null)
            {
                unreadCount = redPoint.PointNum;
            }   
            SetActive(ui.recruit_btn_red, unreadCount > 0);
        }

        public override void OnEnable()
        {
            if (__reset_list_ui)
            {
                __reset_list_ui = false;
                __RefreshCurList();
            }
        }

        public override void OnDisable()
        {
            __reset_list_ui = true;
            if (_filterAnim != null)
            {
                _filterAnim.enabled = false;
            }
        }

        public override void OnClose()
        {
            base.OnClose();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            RemoveEvents();
        }

        private void RemoveEvents()
        {
            Event.Instance.RemoveListener(this, EventDefine.UI_HERO_ITEM_CLICKED);
            Event.Instance.RemoveListener(this, EventDefine.HERO_ITEM_DATA_NEW);
        }
        

        private void OnRecruitBtnClicked()
        {
            MODULE.Recruit.UIOpenRecruitPanel();
        }
        
        private void OnFilterChooseBtnClicked()
        {
            _isFilterShow = !_isFilterShow;
            SetScale(ui.filterChooseIcon, 1f, _isFilterShow ? -1f : 1f);
            SetActive(ui.filterChooseGroup, _isFilterShow);
            SetActive(ui.eventTriggerObj, _isFilterShow);
            if  (_filterAnim != null)
            {
                _filterAnim.enabled = true;
                var animName = _isFilterShow ? "ani_list_panel_zhankai" : "ani_list_panel_shousuo";
                _filterAnim.Play(animName);
            }
        }

        private void OnSelectedFilterItem(hero_list_filter_item item, FilterItem data, int index)
        {
            if (_filterChooseIndex == index)
            {
                return;
            }
            _filterChooseIndex = index;
            RefreshFilterContent();
            OnFilterChooseBtnClicked();
        }

        private void __SelectTab()
        {
            Comp.container.SetData(__data);
            Comp.container.RefreshUI();
            UIMisc.PlayUISound("Play_sfx_ui_panel_hero_open");
        }

        private void __OnItemClicked(HeroItemData data)
        {
            Debug.Log("hero_list_panel __OnItemClicked");
            if (data == null)
            {
                return;
            }
            // if (data == null || ui_select_tab == 0)
            //     return;

            // if (MODULE.Rescue.IsFuncOpenAndShow())
            // {
            //     var heroId = int.Parse(ELEX.Config.ConfHelper.GetConfDataConstant("saveHeroId").value);
            //     if (heroId == data.MetaId)
            //     {
            //         VIEW.OpenUI("rescue_main_panel");
            //         return;
            //     }
            // }

            // if (data.IsCanRecruit())
            // {
            //     __recruit_hero = data;
            //     data.NetSendHeroRecruit();
            //     return;
            // }
            
            var idx = __data.IndexOf(data);
            if (idx >= 0)
            {
                VIEW.OpenUICover(UIDefine.HeroInfoPanel, new InfoPanelData(){ list = __data, index = idx });
            }
        }

        private void __OnGetNewHero(HeroItemData data)
        {
            if (data == null)
                return;

            // if (__recruit_hero != null && __recruit_hero == data)
            // {
            //     MODULE.Hero.UIOpenHeroObtainPanel(data, true);
            // }
        }
        
        public hero_list_head_item GetHeroListHeadItem(int pid)
        {
            return new hero_list_head_item(this, pid, pid.ToString());
        }
        
        public hero_list_filter_item GetHeroListFilterItem(int pid)
        {
            return new hero_list_filter_item(this, pid, pid.ToString());
        }
    }
    
    public partial class hero_list_filter_item : ViewNode
    {
        public hero_list_filter_item(ViewNode hoster, int id, string name) : base(id, name)
        {
            OnAttach(hoster);
            __AutoInitUI();
        }
        
        public void OnSetItemData(hero_list_panel.FilterItem data, int index)
        {
            if (data == null)
            {
                return;
            }
            SetTextValueTMP(uiObjs.type_text, Localization.Get(data.name));
            SetActive(ui.choose, data.isChoose);
            SetActive(ui.bg_top, data.isFirst && data.isChoose);
            SetActive(ui.bg_bottom, data.isLast && data.isChoose);
            SetActive(ui.bg, !data.isLast && !data.isFirst && data.isChoose);
        }
    }


    #region 引导的特殊处理

    public partial class hero_list_panel
    {
        public override void ScrollContainer_ForGuide(string scrollKey, string scrollTag = "")
        {
            base.ScrollContainer_ForGuide(scrollKey, scrollTag);
            
            if (string.IsNullOrEmpty(scrollKey))
            {
                return;
            }
            
            var index = Comp.container.GetData().FindIndex(x => x.MetaId.ToString() == scrollKey);
            Comp.container.MoveToPositionByIndex(index);
        }

        public override void SwitchTab_ForGuide(string tabKey)
        {
            base.SwitchTab_ForGuide(tabKey);
            
            // 该页面没有tab，暂时不用处理
        }
    }

    #endregion
}
