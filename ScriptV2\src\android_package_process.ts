﻿import { JavaCaller } from 'java-caller';
import * as path from "path";
import AdmZip from "adm-zip"
import * as fs from "fs";
import {isValidStr, logFunctionEnd, logFunctionStart, logWithKey} from "./utility.ts";
import {brotliCompress} from "node:zlib";
// bundletool
// https://github.com/google/bundletool
// https://developer.android.com/tools/bundletool?hl=zh-cn
// 您不能使用 apksigner 为 app bundle 签名。
async function buildApks(aabPath: string, outputApksPath: string): Promise<void> {
    logFunctionStart("buildApks",`aabPath:${aabPath} outputApksPath:${outputApksPath}`);
    try {
        const jarpath = path.join(__dirname,'../lib/bundletool-all-1.18.1.jar')
        logWithKey('buildApks',`jarpath:${jarpath}`)
        const java = new JavaCaller({
            jar:jarpath,
            classPath: jarpath,
        });

        const kspath = path.join(__dirname,'../../unicorn.keystore')
        logWithKey('buildApks',`kspath:${kspath}`)
        // 设置签名参数
        const args = [
            'build-apks',
            '--bundle', aabPath,
            '--output', outputApksPath,
            '--mode','universal',
            '--overwrite',
            '--ks',kspath,
            '--ks-pass','pass:A7f!kP2#xQ9zLw4@tS6v',
            '--ks-key-alias','unicorn',
            '--key-pass','pass:A7f!kP2#xQ9zLw4@tS6v'
        ];

        const { status, stdout, stderr } = await java.run(args);

        if (status === 0) {
            logFunctionEnd("buildApks",`success,outputApksPath:${outputApksPath}`);
        } else {

            logFunctionEnd("buildApks",`fail,status:${status} ${stderr}`);
        }
    } catch (error) {
        if (error instanceof Error) {
            logFunctionEnd("buildApks",`error:${error.message}`);
        }else{
            logFunctionEnd("buildApks",`error`);
        }
    }
}

// 解压 .apks 文件并提取 universal.apk
function extractUniversalApk(apksPath: string, outputDirectory: string): string {
    logFunctionStart('extractUniversalApk',`apksPath:${apksPath},outputDirectory:${outputDirectory}`);
    // 检查输入的 .apks 文件是否存在
    if (!fs.existsSync(apksPath)) {
        throw new Error(`.apks file not found at path: ${apksPath}`);
    }

    // 检查输出目录是否存在，如果不存在则创建
    if (!fs.existsSync(outputDirectory)) {
        fs.mkdirSync(outputDirectory, { recursive: true });
    }
    const universalApk ="universal.apk";
    const outputPath = path.join(outputDirectory, "universal.apk");
    if(fs.existsSync(outputPath)) {
        fs.rmSync(outputPath);
    }
    // 使用 AdmZip 加载 .apks 文件
    const zip = new AdmZip(apksPath);
    // 遍历 .apks 文件中的所有条目，检索并提取 universal.apk
    zip.getEntries().forEach((entry) => {
        const entryName = entry.entryName;
        if (entryName === universalApk) {
            zip.extractEntryTo(entry, outputDirectory, false, true);
        }
    });

    if(fs.existsSync(outputPath)) {
        logFunctionEnd('extractUniversalApk',`success,outputPath:${outputPath}`);
        return outputPath
    }else{
        logFunctionEnd('extractUniversalApk',`fail,not find ${universalApk} in ${apksPath}`);
        return '';
    }
}

export async function  extractApkFromaab(aabPath: string, apkPath: string):Promise<string>  {
    logFunctionStart('extractApkFromaab', `aabPath:${aabPath}, apkPath:${apkPath}`);
    // console.log(apkPath);
    let apkName =path.basename(apkPath,path.extname(apkPath))+'.apks';
    let apksPath =path.join(path.dirname(apkPath),apkName);
    // console.log(apksPath);
    await buildApks(aabPath, apksPath);

    let outputApk = extractUniversalApk(apksPath,path.dirname(apksPath));

    if(fs.existsSync(apksPath)) {
        fs.rmSync(apksPath);
    }

    if(isValidStr(outputApk)){
        fs.renameSync(outputApk, apkPath);
        logFunctionEnd('extractApkFromaab', `success,apkPath:${apkPath}`);
        return apkPath;
    }else{
        logFunctionEnd('extractApkFromaab', 'fail');
        return ''
    }
}

async function signApk(inputApkPath: string, outputPath: string): Promise<void> {
    try {
        const java = new JavaCaller({
            jar: './uber-apk-signer-1.3.0.jar',
            classPath: './uber-apk-signer-1.3.0.jar'
        });

        // 设置签名参数
        const args = [
            '--apks', inputApkPath,
            '--out', outputPath
        ];

        const { status, stdout, stderr } = await java.run(args);

        if (status === 0) {
            console.log('APK签名成功！');
            console.log(stdout);
        } else {
            console.error('APK签名失败：', stderr);
        }
    } catch (error) {
        console.error('执行过程中出错：', error);
        if (error instanceof Error) {
            console.error('错误详情：', error.message);
        }
    }
}




// 使用示例 - 使用实际的文件路径
// extractApkFromaab('C:\\Users\\<USER>\\Downloads\\AAB\\0.0.1670124_202505120924.aab', 'C:\\Users\\<USER>\\Downloads\\AAB\\0.0.1670124_202505120924.apk');
