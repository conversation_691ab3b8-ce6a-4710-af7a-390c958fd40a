import {$} from 'zx'
import fs from 'fs-extra'
import * as path from 'path'
import {format} from 'date-fns'
import {
    getFormatTime,
    getLocalIPAddress,
    isInvalidStr,
    logFunctionStart,
    logWithKey,
    stringToNumber
} from "./utility.ts";
import {BUILD_TYPE, JENKIN_BUILD_TIMER, PLATFORM, JENKINS_TO_FEISHU_USER_MAPPING} from "./const.ts";
import {Timer} from "./timer.ts";
import {GitCdnManager} from "./git_cdn_manager.ts";


/**
 * Jenkins参数接口 - 仅包含初始参数
 */
export interface IJenkinsUnityBuildParams {
    projectPath: string
    platform: string
    debug: boolean
    scriptDebug: boolean
    branchName: string
    webServerUrl: string
    buildType: string
    channel: string
    testSDKPay: boolean
    defineSymbols: string
    gameVersion: string,
    packName: string
    clearLibrary: boolean
    isGoogleAAB: boolean
    uploadHybridCLR: boolean
    serverId: string
    gateway: string
    gateWayTcpPort: string,
    gateWayWebSocketPort: string,
    keepGuest: boolean
    unityPath: string  // 例如:  /Applications/Unity/Hub/Editor/2022.3.38t1/Tuanjie/Tuanjie.app/Contents/MacOS/Tuanjie
    cdnRootDirectory: string
    gitResDirectory: string
    copyBuildinFileOption: string
    exportProject: boolean
    createSymbols:boolean
    uploadMiniGameToOfficial: boolean
    //使用git保存版本数据
    useGitSave: boolean
    //资源是否上传外网cdn
    uploadOuterCDN: boolean
    //更新总控
    autoUpdateRemoteVersion: boolean
    //是否使用env，开发阶段需要使用
    useEnvName: boolean
    cdnRootUrl: string,
    //app的名字
    productName: string,
    //bundle identifier
    applicationIdentifier: string,
    netType: number,
    //epsdk中android的一个配置字段
    envAndroid: string,
    //(a.b.c)是否自动提升buildVersion，对应提升c的版本
    autoIncreaseBuildNumber: boolean,
    appDownloadAddress:string,
    confDownloadAddress:string,
    resDownloadAddress:string,

    //当前job的user
    buildUser:string,
}


/**
 * 构建状态接口 - 包含所有运行时状态
 */
export interface IUnityBuildState {
    //只有开发阶段有效
    envName: string
    platform: string
    userId: string
    projectPath: string
    unityVersion: string
    systemTag: string
    localMachineAddress: string
    formatBranchName: string
    shortBranchName: string

    resVersion: string
    cfgVersion: string
    protoVersion:string
    appVersion: string
    versionCode: string//app内部版本号

    buildFileSuffix: string
    buildTime: string
}

/**
 * 存储一些打包过程中的context
 */
export interface IBuildContext {
    gitCdnManager? :GitCdnManager
}
/**
 * BuildParam 类
 * 处理Unity项目的构建参数和状态
 */
export class BuildParam {
    private params: IJenkinsUnityBuildParams
    private state: IUnityBuildState
    private context: Partial<IBuildContext>


    constructor() {
        this.state = this.getDefaultState()
        this.params = this.getDefaultParams()
        this.context = {}
    }

    /**
     * 获取当前参数
     */
    public getParams(): IJenkinsUnityBuildParams {
        return this.params
    }

    /**
     * 获取当前状态
     */
    public getState(): IUnityBuildState {
        return this.state
    }

    public getContext(): Partial<IBuildContext> {
        return this.context;
    }


    /**
     * 初始化构建参数 - 仅保留Jenkins传入的初始参数
     */
    private getDefaultParams(): IJenkinsUnityBuildParams {
        return {
            platform: '',
            projectPath: '',
            debug: false,
            scriptDebug: false,
            branchName: '',
            webServerUrl: '',
            buildType: '',
            channel: '',
            testSDKPay: false,
            defineSymbols: '',
            gameVersion: "",
            packName: '',
            clearLibrary: false,
            isGoogleAAB: false,
            uploadHybridCLR: false,
            serverId: '',
            gateway: '',
            gateWayWebSocketPort: '',
            gateWayTcpPort: '',
            keepGuest: false,
            unityPath: '',
            cdnRootDirectory: '',
            gitResDirectory: '',
            uploadMiniGameToOfficial: false,
            uploadOuterCDN: false,
            useGitSave: false,
            autoUpdateRemoteVersion: false,
            useEnvName: false,
            cdnRootUrl: '',
            productName: '',
            applicationIdentifier: '',
            netType: 0,
            envAndroid: '',
            autoIncreaseBuildNumber: false,
            appDownloadAddress:'',
            confDownloadAddress:'',
            resDownloadAddress:'',
            buildUser:'',
            copyBuildinFileOption:'',
            exportProject:false,
            createSymbols : false,
        }
    }

    /**
     * 初始化构建状态 - 包含所有运行时状态
     */
    private getDefaultState(): IUnityBuildState {
        return {
            envName: '',
            platform: '',
            userId: '',
            projectPath: process.cwd() + "/../",
            unityVersion: '',
            systemTag: '',
            localMachineAddress: '',
            formatBranchName: '',
            shortBranchName: '',
            resVersion: "",
            cfgVersion: '',
            appVersion: '',
            buildFileSuffix: '.exe',
            versionCode: '',
            protoVersion:'',
            buildTime: getFormatTime(),
        }
    }

    public async initState() {
        try {
            this.state.projectPath = this.params.projectPath;
            //初始化平台
            this.state.platform = this.params.platform;

            //  读取 Unity 版本
            console.log('开始读取Unity版本...')
            try {
                const firstLine = (await fs.readFile(path.join(this.state.projectPath, 'ProjectSettings/ProjectVersion.txt'), 'utf8')).split('\n')[0]
                this.state.unityVersion = firstLine.split(':')[1].trim()
                console.log('Unity版本:', this.state.unityVersion)
            } catch (error) {
                console.error('读取Unity版本失败:', error)
                throw new Error(`Unity版本读取失败: ${(error as Error).message}`)
            }

            // 获取IP地址
            this.state.localMachineAddress = getLocalIPAddress() ?? '';
            console.log(`获取本地IP地址:${this.state.localMachineAddress}`)

            // 处理分支名称
            try {
                let formatBranchName = this.params.branchName;

                if (formatBranchName.startsWith("origin/")) {
                    formatBranchName = formatBranchName.substring("origin/".length);
                }
                this.state.formatBranchName = formatBranchName.replace(/\//g, '_')
                this.state.shortBranchName = this.params.branchName.split('/').pop() || ''
                // console.log('分支名称:', this.state.formatBranchName)
            } catch (error) {
                console.error('处理分支名称失败:', error)
                throw new Error(`分支名称处理失败: ${(error as Error).message}`)
            }

            // 初始化系统和构建设置
            try {
                //初始化环境变量,开发 阶段有用,这里直接映射分支名
                if (this.params.useEnvName) {
                    this.state.envName = `${this.params.platform}_${this.state.formatBranchName}`;
                }
                // 计算版本
                await this.initVersions()
                // 初始化系统和路径
                await this.initSystemTag()
                // await this.initLocalOutputPath()
                // 设置构建文件信息
                // this.initBuildFilePath()
                console.log('初始化完成')
            } catch (error) {
                console.error('初始化系统和构建设置失败:', error)
                throw new Error(`系统和构建设置初始化失败: ${(error as Error).message}`)
            }

            return {params: this.params, state: this.state}

        } catch (error) {
            console.error('初始化过程失败:', error)
            // 可以在这里添加一些清理代码
            throw error
        }
    }

    /**
     * 是否提交到外网cdn
     */
    public isUseOuterCDN(): boolean {
        return this.params.uploadOuterCDN;
    }

    public isNullOrBlank(str: string | null | undefined): boolean {
        return !str || str.trim() === "";
    }

    public ShowInfo() {
        console.log('ShowInfo Begin ')
        console.log(JSON.stringify({params: this.params, state: this.state}, null, 2))
        console.log('ShowInfo End ')
    }


    /**
     * 更新构建参数
     */
    public updateParam(key: keyof IJenkinsUnityBuildParams, value: string | boolean | number) {
        if (typeof this.params[key] === typeof value) {
            this.params[key] = value as never;
            console.log(`更新参数 ${key}: ${value}`);
        }else if(typeof this.params[key] === 'number' && typeof value === 'string') {
            //那么进行强转
            this.params[key] = stringToNumber(value) as never;
            console.log(`更新参数 ${key}: ${value}`);
        }
        else {
            console.error(`更新参数,类型不匹配：无法将 ${key} value:${value} type:${typeof value} 类型赋给 ${this.params[key]} type:${typeof this.params[key]} 类型`);
        }
    }

    /**
     * 批量更新构建参数
     */
    public updateParams(params: Partial<IJenkinsUnityBuildParams>) {
        Object.entries(params).forEach(([key, value]) => {
            this.updateParam(key as keyof IJenkinsUnityBuildParams, value)
        })
    }

    /**
     * 更新构建状态
     */
    public updateState<K extends keyof IUnityBuildState>(key: K, value: IUnityBuildState[K]) {
        this.state[key] = value
        console.log(`更新状态 ${key}: ${value}`)
    }

    /**
     * 获取系统标签和Unity路径
     */
    private async initSystemTag() {
        const sysType = await $`uname`

        if (sysType.stdout.includes('Darwin')) {
            this.state.systemTag = 'mac'
            //  this.state.unityPath = `/Applications/Unity/Hub/Editor/${this.state.unityVersion}/Unity.app/Contents/MacOS/Unity`
        } else if (sysType.stdout.includes('MINGW') || sysType.stdout.includes('CYGWIN')) {
            this.state.systemTag = 'win'
            //this.state.unityPath = `C:\\Program Files\\Unity\\Hub\\Editor\\${this.state.unityVersion}\\Editor\\Unity.exe`
        } else {
            this.state.systemTag = 'mac'
            // this.state.unityPath = `/Applications/Unity/Hub/Editor/${this.state.unityVersion}/Unity.app/Contents/MacOS/Unity`
        }

        console.log(`系统类型: ${this.state.systemTag}`)
        console.log(`Unity路径: ${this.params.unityPath}`)
    }

    /**
     * 计算版本号
     */
    public async initVersions() {
        /**
         * 对应app外部版本号
         * 1，首先分支名中获取
         * 2. 分支名中不存在版本号，那么从用户手动填的版本号获取。
         */
        let gameVersion = this.extractAndNormalizeVersion(this.params.branchName)
        if (isInvalidStr(gameVersion)) {
            gameVersion = `${this.params.gameVersion}`
        }
        if (isInvalidStr(gameVersion)) {
            gameVersion = `0.0.1`
        }

        if (this.params.autoIncreaseBuildNumber) {
            const parts = gameVersion!.split('.');
            while (parts.length < 3) {
                parts.push('0'); // 补充缺失的部分为 "0"
            }
            parts[2] = `${this.getVersionString()}`;
            gameVersion = parts.join('.'); // 重新拼接格式化为 "x.y.z"
            logWithKey('autoIncreaseBuildNumber', gameVersion)
        }

        this.state.appVersion = gameVersion!;

        /**
         * 对应app内部版本号
         * Android有最大int32限制{2,147,483,647}
         */
        this.state.versionCode = `${this.getVersionCode()}`;

        //这里将配置和资源的版本号同步app版本号
        this.state.resVersion = this.state.appVersion
        //todo 配置额版本号读表，不需要在这里配置
        this.state.cfgVersion = this.getConfVersionFromJson()
        if(isInvalidStr(this.state.cfgVersion)){
        this.state.cfgVersion = this.state.appVersion
            logWithKey('initVersions','从配置中读取cfgVersion失败，使用appVersion')
        }
        //从配置中读取proto版本号
        this.state.protoVersion = this.getProtoVersionFromJson();
    }

    /**
     * 从配置文件中读取配置版本号
     * @private
     */
    private  getConfVersionFromJson(): string {
        logFunctionStart('getConfVersionFromJson')
        // 指定文件路径
        const filePath = path.resolve(this.params.projectPath, 'conf/json/ConfConfigVersion_configVersion/1.json');
        logWithKey('getConfVersionFromJson',`filePath:${filePath}`);
        try {
            // 判断文件是否存在
            if (fs.existsSync(filePath)) {
                // 读取文件内容
                const fileContent = fs.readFileSync(filePath, 'utf-8');
                logWithKey('getConfVersionFromJson',`fileContent:${fileContent}`);
                // 使用正则表达式提取版本号（如: "version": "xxx"）
                const pattern = /"version"\s*:\s*"(.*?)"/;
                const match = fileContent.match(pattern);

                if (match) {
                    // 提取版本号
                    const version = match[1];
                    logWithKey('getConfVersionFromJson',`提取到的版本号为: ${version}`);
                    return version;
                } else {
                    logWithKey('getConfVersionFromJson',"版本号未找到");
                }
            } else {
                logWithKey('getConfVersionFromJson',`ConfVersion 文件不存在: ${filePath}`);
            }
        } catch (err) {
            logWithKey('getConfVersionFromJson',`读取文件出现错误: ${(err as Error).message}`);
        }
        return '';
    }


    /**
     * 从配置文件中读取 Proto 版本号
     * @private
     */
    private getProtoVersionFromJson(): string {
        logFunctionStart('getProtoVersion');

        // 指定文件路径
        const filePath = path.resolve(this.params.projectPath, 'conf/net_proto/version.proto');
        logWithKey('getProtoVersion', `filePath:${filePath}`);

        try {
            // 判断文件是否存在
            if (fs.existsSync(filePath)) {
                // 读取文件内容
                const fileContent = fs.readFileSync(filePath, 'utf-8');
                logWithKey('getProtoVersion', `fileContent:${fileContent}`);

                // 使用正则表达式提取版本号（如: default="xxx"）
                const pattern = /default\s*=\s*"(.*?)"/;
                const match = fileContent.match(pattern);
                if (match) {
                    // 提取版本号
                    const version = match[1];
                    logWithKey('getProtoVersion', `提取到的 ProtoVersion 为: ${version}`);
                    return version;
                } else {
                    logWithKey('getProtoVersion', 'ProtoVersion 未找到');
                }
            } else {
                logWithKey('getProtoVersion', `ProtoVersion 文件不存在: ${filePath}`);
            }
        } catch (err) {
            logWithKey('getProtoVersion', `读取文件出现错误: ${(err as Error).message}`);
        }

        return '';
    }

    /**
     * 返回打包的产物名字
     * @private
     */
    private getBuildPlayerFileName(): string {
        let buildOutputName = `${this.state.appVersion}_${this.state.buildTime}`
        if (this.state.platform == PLATFORM.ANDROID) {
            if(this.params.isGoogleAAB)
                buildOutputName = `${buildOutputName}.aab`;
            else
            buildOutputName = `${buildOutputName}.apk`;
        }
        else if(this.state.platform === PLATFORM.WeixinMiniGame) {
            buildOutputName = `${buildOutputName}/minigame`;
        }
        return buildOutputName;
    }

    /**
     * 返回打包的产物
     */
    public getBuildPlayerFullPath(): string {
        let output = `${this.state.projectPath}/build/${this.state.platform}/${this.state.appVersion}_${this.state.buildTime}/${this.getBuildPlayerFileName()}`
        logWithKey("getBuildOutputPlayer", output);
        return output;
    }

    /**
     * 生成8位数字的 versionCode：2位月份，2位天数，2位小时，2位分钟
     * 例如：2024年3月15日14时30分 -> "03151430"
     * @returns {string} 生成的 versionCode
     */
    public getVersionString(): string {
        // 使用本地时间
        const date = new Date();
        
        // 获取月份、日期、小时、分钟
        const month = date.getMonth() + 1; // 本地月份（从 0 开始，因此需要 +1）
        const day = date.getDate(); // 本地日期
        const hour = date.getHours(); // 本地小时
        const minute = date.getMinutes(); // 本地分钟

        // 拼接成8位字符串：MMDDHHMM
        const formattedDate = `${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}`;

        return formattedDate;
    }

      /**
     * 以时间戳计算出一个不会重复的 versionCode。采用紧凑格式。
     * 以 2025 年为元年，不超过 int32 最大值，使用 UTC 时间。
     * 大概可以表示200多年
     * @returns {number} 生成的 versionCode
     */
      public getVersionCode(): number {
        const baseYear = 2025;
        const int32Max = 2147483647; // int32 最大值限制

        // 使用 UTC 时间
        const date = new Date();
        // (1) 年份偏移
        const year = Math.max(date.getUTCFullYear() - baseYear, 0); // 相对于 2025 年的偏移量

        // (2) 月份和日期相加 (UTC)
        const month = date.getUTCMonth() + 1; // UTC 月份（从 0 开始，因此需要 +1）
        const day = date.getUTCDate(); // UTC 日期
        const monthDayPart = (month * 31) + day; // 月份和日期相加
        // (3) 小时和分钟相加 (UTC)
        const hour = date.getUTCHours(); // UTC 小时
        const minute = date.getUTCMinutes(); // UTC 分钟

        // (4) 拼接结果
        const formattedDate = parseInt(
            `${year}${monthDayPart.toString().padStart(3, '0')}${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}`,
            10
        );

        // (5) 检查是否超出 int32 范围
        if (formattedDate > int32Max) {
            throw new Error("VersionCode exceeds int32 range");
        }

        return formattedDate;
    }


    /**
     * 从分支名中提取3位版本号
     * @param branchName
     * 支持的格式：
     * 1. v1.0.0
     * 2. release-v1.0.0
     * 3. feature/v1.0.0
     * 4. any-prefix-v1.0.0
     * 5. V1.0（会被转换为 1.0.0）
     * 6. release-V1（会被转换为 1.0.0）
     */
    public extractAndNormalizeVersion(branchName: string): string | null {
        
        const versionMatch = branchName.match(/.*?(?:v)(\d+(?:\.\d+)?(?:\.\d+)?)/i);

        if (versionMatch) {
            // 提取到的原始版本号
            let version = versionMatch[1];
            // 按 "." 分割版本号并补全至三位
            const parts = version.split('.');
            while (parts.length < 3) {
                parts.push('0'); // 补充缺失的部分为 "0"
            }
            return parts.join('.'); // 重新拼接格式化为 "x.y.z"
        }
        return null; // 未匹配到版本号时返回 null
    }


    /**
     * 修改JSON配置并重新编译Unity
     */
    public async changeJsonAttr(jsonPath: string, attrName: string, attrValue: string, attrType: string) {
        const configPath = `jsonPath=${process.cwd()}/${jsonPath}`
        await $`sh Tools/BuildTools/Release/run.sh "-JsonChange" ${configPath} attrName=${attrName} attrValue=${attrValue} attrType=${attrType}`
        //await $`source shell/recompile_unity.sh`
    }

    public parseParamsStr(paramsStr: string) {
        console.log(`parseParamsStr,${paramsStr}`)
        // 参数空值检查
        if (!paramsStr || paramsStr.trim() === '') {
            console.error('Error: paramsStr is empty or undefined,skip');
        }

        const lines = paramsStr.split('\n')

        for (const line of lines) {
            const [key, value] = line.trim().split('=')
            if (key && value) {
                const trimmedKey = key.trim();
                let trimmedValue: string | boolean = value.trim();

                if (trimmedValue.toLowerCase() === 'true') {
                    trimmedValue = true;
                } else if (trimmedValue.toLowerCase() === 'false') {
                    trimmedValue = false;
                }
                this.updateParam(trimmedKey as keyof IJenkinsUnityBuildParams, trimmedValue);
            }
        }
    }

    /**
     * 获取cdn的相对路径
     */
    public getCdnRelativePath(): string {
        let _path = path.join(this.params.platform, isInvalidStr(this.state.envName) ? '' : `${this.state.envName}`);
        return _path;
    }

    /**
     * 获取打包的apk,ipa,exe等下载地址
     */
    public getBuildPlayerDownloadUrl(): string {
        let url = `${this.params.cdnRootUrl}/${this.getCdnRelativePath()}/${this.getBuildPlayerFileName()}`;
        logWithKey('getBuildPlayerDownloadUrl', url);
        return url
    }


}
