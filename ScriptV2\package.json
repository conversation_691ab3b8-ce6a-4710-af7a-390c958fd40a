{"name": "scripts", "version": "2.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "start": "tsx src/index.ts", "build-minigame": "tsx src/build_minigame_to_local_cdn.ts", "build-android": "tsx src/build_android.ts", "build-player": "tsx src/build_player.ts", "backup_jenkins": "tsx src/backup_jen<PERSON>_configs.ts", "warm_up_cos": "tsx src/warm_up_cos.ts", "manual_upload_to_cos": "tsx src/manual_upload_to_cos.ts", "warm_up_cos_test": "tsx src/warm_up_cos.ts --platform weixinminigame --env weixinminigame_lishengjie_optimize-config-v2 --baseUrl https://xiuxian-cdn.elex-tech.net --cdnRootDirectory F:/cdn --cfgVersion 2.0.1241040 --resVersion 2.0.1241040 --area mainland"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/adm-zip": "^0.5.7", "@types/jest": "^29.5.14", "@types/node": "^22.10.8", "@types/qrcode": "^1.5.5", "commander": "^13.1.0", "jest": "^29.7.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "dependencies": {"@types/ali-oss": "^6.16.11", "@types/tail": "^2.2.3", "adm-zip": "^0.5.16", "ali-oss": "^6.23.0", "cos-nodejs-sdk-v5": "^2.14.6", "crc-32": "^1.2.2", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "flatted": "^3.3.3", "fs-extra": "^11.2.0", "java-caller": "^4.2.0", "qrcode": "^1.5.4", "simple-git": "^3.27.0", "tail": "^2.2.6", "tencentcloud-sdk-nodejs-cdn": "^4.1.10", "zx": "^8.2.4"}}