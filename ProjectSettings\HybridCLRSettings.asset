%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e189374413a3f00468e49d51d8b27a09, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 0
  useGlobalIl2cpp: 0
  hybridclrRepoURL: https://gitee.com/focus-creative-games/hybridclr
  il2cppPlusRepoURL: https://gitee.com/focus-creative-games/il2cpp_plus
  hotUpdateAssemblyDefinitions:
  - {fileID: 5897886265953266890, guid: 7a080837a80cff0498f00efce177a854, type: 3}
  - {fileID: 5897886265953266890, guid: c2cce9ba36c5afc4e9eb8d44fa79c95c, type: 3}
  hotUpdateAssemblies: []
  preserveHotUpdateAssemblies: []
  hotUpdateDllCompileOutputRootDir: HybridCLRData/HotUpdateDlls
  externalHotUpdateAssembliyDirs: []
  strippedAOTDllOutputRootDir: HybridCLRData/AOTDllOutput
  patchAOTAssemblies:
  - DOTween
  - GameCreator.Runtime.Core
  - LitJson
  - LuaCoreBase
  - MobControl
  - Newtonsoft.Json
  - OSA.Core
  - ParadoxNotion
  - Sirenix.OdinInspector.Attributes
  - System.Core
  - System
  - Unity.RenderPipelines.Core.Runtime
  - UnityEngine.AssetBundleModule
  - UnityEngine.CoreModule
  - UnityEngine.JSONSerializeModule
  - UnityEngine.UI
  - com.elex.client.common
  - mscorlib
  - spine-unity
  outputLinkFile: HybridCLRGenerate/link.xml
  outputAOTGenericReferenceFile: HybridCLRGenerate/AOTGenericReferences.cs
  maxGenericReferenceIteration: 10
  maxMethodBridgeGenericIteration: 10
