%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13981b66f67204744891820795ef4230, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gameplayTagTreeElements:
  - _id: 1
    _name: Root
    _depth: -1
  - _id: 3
    _name: Fraction
    _depth: 0
  - _id: 4
    _name: Player
    _depth: 1
  - _id: 5
    _name: Enemy
    _depth: 1
  - _id: 69
    _name: Effect_Type
    _depth: 0
  - _id: 60
    _name: Damage
    _depth: 1
  - _id: 63
    _name: Attack
    _depth: 2
  - _id: 62
    _name: Skill
    _depth: 2
  - _id: 66
    _name: Heal
    _depth: 1
  - _id: 109
    _name: Shield
    _depth: 1
  - _id: 6
    _name: Buff
    _depth: 1
  - _id: 2
    _name: Ability
    _depth: 0
  - _id: 70
    _name: Count
    _depth: 1
  - _id: 120
    _name: Count_1
    _depth: 2
  - _id: 77
    _name: Count_2
    _depth: 2
  - _id: 95
    _name: Count
    _depth: 3
  - _id: 90
    _name: Ready
    _depth: 3
  - _id: 78
    _name: Count_3
    _depth: 2
  - _id: 96
    _name: Count
    _depth: 3
  - _id: 92
    _name: Ready
    _depth: 3
  - _id: 79
    _name: Count_4
    _depth: 2
  - _id: 97
    _name: Count
    _depth: 3
  - _id: 93
    _name: Ready
    _depth: 3
  - _id: 80
    _name: Count_5
    _depth: 2
  - _id: 98
    _name: Count
    _depth: 3
  - _id: 94
    _name: Ready
    _depth: 3
  - _id: 102
    _name: Spawn
    _depth: 1
  - _id: 106
    _name: SpawnSkill_1
    _depth: 2
  - _id: 107
    _name: SpawnSkill_2
    _depth: 2
  - _id: 108
    _name: SpawnSkill_3
    _depth: 2
  - _id: 15
    _name: Die
    _depth: 1
  - _id: 9
    _name: Attack
    _depth: 1
  - _id: 71
    _name: Special
    _depth: 2
  - _id: 34
    _name: running
    _depth: 2
  - _id: 32
    _name: CD
    _depth: 2
  - _id: 68
    _name: Skill_1
    _depth: 1
  - _id: 35
    _name: running
    _depth: 2
  - _id: 36
    _name: CD
    _depth: 2
  - _id: 38
    _name: Skill_2
    _depth: 1
  - _id: 40
    _name: running
    _depth: 2
  - _id: 41
    _name: CD
    _depth: 2
  - _id: 39
    _name: Skill_3
    _depth: 1
  - _id: 42
    _name: running
    _depth: 2
  - _id: 43
    _name: CD
    _depth: 2
  - _id: 44
    _name: Skill_4
    _depth: 1
  - _id: 45
    _name: running
    _depth: 2
  - _id: 46
    _name: CD
    _depth: 2
  - _id: 16
    _name: State
    _depth: 0
  - _id: 113
    _name: Transform
    _depth: 1
  - _id: 112
    _name: Charge
    _depth: 1
  - _id: 110
    _name: Armor
    _depth: 1
  - _id: 64
    _name: Immunity
    _depth: 1
  - _id: 65
    _name: Damage_Immunity
    _depth: 2
  - _id: 67
    _name: Heal_Immunity
    _depth: 2
  - _id: 53
    _name: Control_Immunity
    _depth: 2
  - _id: 52
    _name: Control
    _depth: 1
  - _id: 115
    _name: Dizzy
    _depth: 2
  - _id: 114
    _name: Hit
    _depth: 2
  - _id: 111
    _name: Disabled
    _depth: 2
  - _id: 55
    _name: Cant_Move
    _depth: 2
  - _id: 56
    _name: Cant_Attack
    _depth: 2
  - _id: 57
    _name: Cant_Skill
    _depth: 2
  - _id: 48
    _name: IsAlive
    _depth: 1
  - _id: 117
    _name: Mark
    _depth: 0
  - _id: 128
    _name: HP_State
    _depth: 1
  - _id: 130
    _name: Percent_25
    _depth: 2
  - _id: 127
    _name: Percent_50
    _depth: 2
  - _id: 129
    _name: Percent_75
    _depth: 2
  - _id: 122
    _name: SpecialMark
    _depth: 1
  - _id: 123
    _name: Mark1
    _depth: 2
  - _id: 124
    _name: Mark2
    _depth: 2
  - _id: 125
    _name: Mark3
    _depth: 2
  - _id: 121
    _name: MissionFinish
    _depth: 1
  - _id: 116
    _name: AfterKill
    _depth: 1
  - _id: 119
    _name: Keanu_SkillMark
    _depth: 1
  Tags:
  - _name: Fraction
    _hashCode: -1199216040
    _shortName: Fraction
    _ancestorHashCodes: 
    _ancestorNames: []
  - _name: Fraction.Player
    _hashCode: -2024449421
    _shortName: Player
    _ancestorHashCodes: 586a85b8
    _ancestorNames:
    - Fraction
  - _name: Fraction.Enemy
    _hashCode: -1488994106
    _shortName: Enemy
    _ancestorHashCodes: 586a85b8
    _ancestorNames:
    - Fraction
  - _name: Effect_Type
    _hashCode: -371351852
    _shortName: Effect_Type
    _ancestorHashCodes: 
    _ancestorNames: []
  - _name: Effect_Type.Damage
    _hashCode: 980954777
    _shortName: Damage
    _ancestorHashCodes: d49edde9
    _ancestorNames:
    - Effect_Type
  - _name: Effect_Type.Damage.Attack
    _hashCode: 2034632271
    _shortName: Attack
    _ancestorHashCodes: d49edde9992e783a
    _ancestorNames:
    - Effect_Type
    - Effect_Type.Damage
  - _name: Effect_Type.Damage.Skill
    _hashCode: -1115329928
    _shortName: Skill
    _ancestorHashCodes: d49edde9992e783a
    _ancestorNames:
    - Effect_Type
    - Effect_Type.Damage
  - _name: Effect_Type.Heal
    _hashCode: -1382532348
    _shortName: Heal
    _ancestorHashCodes: d49edde9
    _ancestorNames:
    - Effect_Type
  - _name: Effect_Type.Shield
    _hashCode: -803274523
    _shortName: Shield
    _ancestorHashCodes: d49edde9
    _ancestorNames:
    - Effect_Type
  - _name: Effect_Type.Buff
    _hashCode: -865195303
    _shortName: Buff
    _ancestorHashCodes: d49edde9
    _ancestorNames:
    - Effect_Type
  - _name: Ability
    _hashCode: -523153736
    _shortName: Ability
    _ancestorHashCodes: 
    _ancestorNames: []
  - _name: Ability.Count
    _hashCode: 586871599
    _shortName: Count
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Count.Count_1
    _hashCode: -36695508
    _shortName: Count_1
    _ancestorHashCodes: b84ed1e02ff3fa22
    _ancestorNames:
    - Ability
    - Ability.Count
  - _name: Ability.Count.Count_2
    _hashCode: -36695505
    _shortName: Count_2
    _ancestorHashCodes: b84ed1e02ff3fa22
    _ancestorNames:
    - Ability
    - Ability.Count
  - _name: Ability.Count.Count_2.Count
    _hashCode: 183547992
    _shortName: Count
    _ancestorHashCodes: b84ed1e02ff3fa222f12d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_2
  - _name: Ability.Count.Count_2.Ready
    _hashCode: 223970000
    _shortName: Ready
    _ancestorHashCodes: b84ed1e02ff3fa222f12d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_2
  - _name: Ability.Count.Count_3
    _hashCode: -36695506
    _shortName: Count_3
    _ancestorHashCodes: b84ed1e02ff3fa22
    _ancestorNames:
    - Ability
    - Ability.Count
  - _name: Ability.Count.Count_3.Count
    _hashCode: 183374839
    _shortName: Count
    _ancestorHashCodes: b84ed1e02ff3fa222e12d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_3
  - _name: Ability.Count.Count_3.Ready
    _hashCode: 223798767
    _shortName: Ready
    _ancestorHashCodes: b84ed1e02ff3fa222e12d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_3
  - _name: Ability.Count.Count_4
    _hashCode: -36695511
    _shortName: Count_4
    _ancestorHashCodes: b84ed1e02ff3fa22
    _ancestorNames:
    - Ability
    - Ability.Count
  - _name: Ability.Count.Count_4.Count
    _hashCode: 183197718
    _shortName: Count
    _ancestorHashCodes: b84ed1e02ff3fa222912d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_4
  - _name: Ability.Count.Count_4.Ready
    _hashCode: 223619214
    _shortName: Ready
    _ancestorHashCodes: b84ed1e02ff3fa222912d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_4
  - _name: Ability.Count.Count_5
    _hashCode: -36695512
    _shortName: Count_5
    _ancestorHashCodes: b84ed1e02ff3fa22
    _ancestorNames:
    - Ability
    - Ability.Count
  - _name: Ability.Count.Count_5.Count
    _hashCode: 183303093
    _shortName: Count
    _ancestorHashCodes: b84ed1e02ff3fa222812d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_5
  - _name: Ability.Count.Count_5.Ready
    _hashCode: 223727021
    _shortName: Ready
    _ancestorHashCodes: b84ed1e02ff3fa222812d0fd
    _ancestorNames:
    - Ability
    - Ability.Count
    - Ability.Count.Count_5
  - _name: Ability.Spawn
    _hashCode: -273933297
    _shortName: Spawn
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Spawn.SpawnSkill_1
    _hashCode: -1180799459
    _shortName: SpawnSkill_1
    _ancestorHashCodes: b84ed1e00f1cacef
    _ancestorNames:
    - Ability
    - Ability.Spawn
  - _name: Ability.Spawn.SpawnSkill_2
    _hashCode: -777514932
    _shortName: SpawnSkill_2
    _ancestorHashCodes: b84ed1e00f1cacef
    _ancestorNames:
    - Ability
    - Ability.Spawn
  - _name: Ability.Spawn.SpawnSkill_3
    _hashCode: 1951368423
    _shortName: SpawnSkill_3
    _ancestorHashCodes: b84ed1e00f1cacef
    _ancestorNames:
    - Ability
    - Ability.Spawn
  - _name: Ability.Die
    _hashCode: 265453460
    _shortName: Die
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Attack
    _hashCode: 115429334
    _shortName: Attack
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Attack.Special
    _hashCode: -411541373
    _shortName: Special
    _ancestorHashCodes: b84ed1e0d64fe106
    _ancestorNames:
    - Ability
    - Ability.Attack
  - _name: Ability.Attack.running
    _hashCode: -1980811263
    _shortName: running
    _ancestorHashCodes: b84ed1e0d64fe106
    _ancestorNames:
    - Ability
    - Ability.Attack
  - _name: Ability.Attack.CD
    _hashCode: -250513571
    _shortName: CD
    _ancestorHashCodes: b84ed1e0d64fe106
    _ancestorNames:
    - Ability
    - Ability.Attack
  - _name: Ability.Skill_1
    _hashCode: 410277607
    _shortName: Skill_1
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Skill_1.running
    _hashCode: 955764930
    _shortName: running
    _ancestorHashCodes: b84ed1e0e7567418
    _ancestorNames:
    - Ability
    - Ability.Skill_1
  - _name: Ability.Skill_1.CD
    _hashCode: 145654248
    _shortName: CD
    _ancestorHashCodes: b84ed1e0e7567418
    _ancestorNames:
    - Ability
    - Ability.Skill_1
  - _name: Ability.Skill_2
    _hashCode: 410277606
    _shortName: Skill_2
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Skill_2.running
    _hashCode: 957020549
    _shortName: running
    _ancestorHashCodes: b84ed1e0e6567418
    _ancestorNames:
    - Ability
    - Ability.Skill_2
  - _name: Ability.Skill_2.CD
    _hashCode: 145654217
    _shortName: CD
    _ancestorHashCodes: b84ed1e0e6567418
    _ancestorNames:
    - Ability
    - Ability.Skill_2
  - _name: Ability.Skill_3
    _hashCode: 410277605
    _shortName: Skill_3
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Skill_3.running
    _hashCode: 948936772
    _shortName: running
    _ancestorHashCodes: b84ed1e0e5567418
    _ancestorNames:
    - Ability
    - Ability.Skill_3
  - _name: Ability.Skill_3.CD
    _hashCode: 145654058
    _shortName: CD
    _ancestorHashCodes: b84ed1e0e5567418
    _ancestorNames:
    - Ability
    - Ability.Skill_3
  - _name: Ability.Skill_4
    _hashCode: 410277612
    _shortName: Skill_4
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  - _name: Ability.Skill_4.running
    _hashCode: 959392263
    _shortName: running
    _ancestorHashCodes: b84ed1e0ec567418
    _ancestorNames:
    - Ability
    - Ability.Skill_4
  - _name: Ability.Skill_4.CD
    _hashCode: 145654283
    _shortName: CD
    _ancestorHashCodes: b84ed1e0ec567418
    _ancestorNames:
    - Ability
    - Ability.Skill_4
  - _name: State
    _hashCode: 1695958603
    _shortName: State
    _ancestorHashCodes: 
    _ancestorNames: []
  - _name: State.Transform
    _hashCode: -1329954427
    _shortName: Transform
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: State.Charge
    _hashCode: -1112546661
    _shortName: Charge
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: State.Armor
    _hashCode: 701487322
    _shortName: Armor
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: State.Immunity
    _hashCode: -1394352005
    _shortName: Immunity
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: State.Immunity.Damage_Immunity
    _hashCode: -1301577957
    _shortName: Damage_Immunity
    _ancestorHashCodes: 4b4616657be0e3ac
    _ancestorNames:
    - State
    - State.Immunity
  - _name: State.Immunity.Heal_Immunity
    _hashCode: -806998362
    _shortName: Heal_Immunity
    _ancestorHashCodes: 4b4616657be0e3ac
    _ancestorNames:
    - State
    - State.Immunity
  - _name: State.Immunity.Control_Immunity
    _hashCode: -1098777657
    _shortName: Control_Immunity
    _ancestorHashCodes: 4b4616657be0e3ac
    _ancestorNames:
    - State
    - State.Immunity
  - _name: State.Control
    _hashCode: -1399146500
    _shortName: Control
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: State.Control.Dizzy
    _hashCode: 765520528
    _shortName: Dizzy
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.Control.Hit
    _hashCode: 1196791989
    _shortName: Hit
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.Control.Disabled
    _hashCode: -1458974970
    _shortName: Disabled
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.Control.Cant_Move
    _hashCode: 94067988
    _shortName: Cant_Move
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.Control.Cant_Attack
    _hashCode: -1064944857
    _shortName: Cant_Attack
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.Control.Cant_Skill
    _hashCode: 1870552846
    _shortName: Cant_Skill
    _ancestorHashCodes: 4b461665fcb79aac
    _ancestorNames:
    - State
    - State.Control
  - _name: State.IsAlive
    _hashCode: -825449696
    _shortName: IsAlive
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  - _name: Mark
    _hashCode: -1516408795
    _shortName: Mark
    _ancestorHashCodes: 
    _ancestorNames: []
  - _name: Mark.HP_State
    _hashCode: -89872149
    _shortName: HP_State
    _ancestorHashCodes: 25709da5
    _ancestorNames:
    - Mark
  - _name: Mark.HP_State.Percent_25
    _hashCode: 780861142
    _shortName: Percent_25
    _ancestorHashCodes: 25709da5eba8a4fa
    _ancestorNames:
    - Mark
    - Mark.HP_State
  - _name: Mark.HP_State.Percent_50
    _hashCode: 377576618
    _shortName: Percent_50
    _ancestorHashCodes: 25709da5eba8a4fa
    _ancestorNames:
    - Mark
    - Mark.HP_State
  - _name: Mark.HP_State.Percent_75
    _hashCode: 780861147
    _shortName: Percent_75
    _ancestorHashCodes: 25709da5eba8a4fa
    _ancestorNames:
    - Mark
    - Mark.HP_State
  - _name: Mark.SpecialMark
    _hashCode: -850771769
    _shortName: SpecialMark
    _ancestorHashCodes: 25709da5
    _ancestorNames:
    - Mark
  - _name: Mark.SpecialMark.Mark1
    _hashCode: 438945397
    _shortName: Mark1
    _ancestorHashCodes: 25709da5c7404acd
    _ancestorNames:
    - Mark
    - Mark.SpecialMark
  - _name: Mark.SpecialMark.Mark2
    _hashCode: 842229924
    _shortName: Mark2
    _ancestorHashCodes: 25709da5c7404acd
    _ancestorNames:
    - Mark
    - Mark.SpecialMark
  - _name: Mark.SpecialMark.Mark3
    _hashCode: -723854017
    _shortName: Mark3
    _ancestorHashCodes: 25709da5c7404acd
    _ancestorNames:
    - Mark
    - Mark.SpecialMark
  - _name: Mark.MissionFinish
    _hashCode: 266289328
    _shortName: MissionFinish
    _ancestorHashCodes: 25709da5
    _ancestorNames:
    - Mark
  - _name: Mark.AfterKill
    _hashCode: 1084878321
    _shortName: AfterKill
    _ancestorHashCodes: 25709da5
    _ancestorNames:
    - Mark
  - _name: Mark.Keanu_SkillMark
    _hashCode: 1848445236
    _shortName: Keanu_SkillMark
    _ancestorHashCodes: 25709da5
    _ancestorNames:
    - Mark
