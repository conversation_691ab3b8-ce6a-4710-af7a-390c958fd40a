/**
 * Upload CDN Tool
 * 用于将Unity构建的资源和配置文件上传到腾讯云oss
 * <AUTHOR>
 * @date 2025-01-14
 */

import {$} from 'zx';
import {BuildParam} from './build_params.js';
import {GitCdnManager} from "./git_cdn_manager.ts";
import COS, {ProgressInfo} from 'cos-nodejs-sdk-v5';
import {Timer} from "./timer.js";
import {delay, isInvalidStr, logFunctionEnd, logFunctionStart, logWithKey} from "./utility.ts";
import {Client} from "tencentcloud-sdk-nodejs-cdn/tencentcloud/services/cdn/v20180606/cdn_client";
import * as TencentCloudCommon from "tencentcloud-sdk-nodejs-common";
import {CDN_VERSION_PREFIX} from "./const.ts";
import {stringify} from 'flatted';

//敏感信息从环境变量中获取
const SecretId = process.env.COS_SECRET_ID ?? '';
const SecretKey = process.env.COS_SECRET_KEY?? '';
const Bucket = process.env.COS_BUCKET?? '';
const Region = process.env.COS_REGION?? '';
// logWithKey('COS_PARAMS',`SecretId:${SecretId} SecretKey:${SecretKey} Bucket:${Bucket} Region:${Region}`)
logWithKey('COS_PARAMS', `SecretId:******* SecretKey:****** Bucket:${Bucket} Region:${Region}`)
export default async function test_upload_to_cos() {
    console.log(`upload_to_cos`);
    // await uploadByCoscli("F:/teskcos","cos://xiuxian-1251001614/");
    // let ret = await uploadByCosSdK([""],"");
    // console.log(`uploadByCosSdK success:${ret}`);
    const cos = new COS({
        SecretId: SecretId, // 推荐使用环境变量获取；用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
        SecretKey: SecretKey, // 推荐使用环境变量获取；用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
    });

    let getServiceResult = await cos.getService().catch(console.error);
    // console.log(getServiceResult);
    // let param = new BuildParam();
    //
    // await uploadToCosWithParam(param);
}

/**
 * 上传资源到腾讯云
 * @param buildParam
 */
export async function uploadToCosWithParam(buildParam: BuildParam): Promise<void> {
    logFunctionStart(`uploadToCosWithParam:`,stringify(buildParam));

    if (buildParam.isUseOuterCDN() === false) {
        logFunctionEnd(`uploadToCosWithParam:`,'skip upload to cos,isUseOuterCDN === false');
        return;
    }

    let state = buildParam.getState();
    let params = buildParam.getParams();
    let version = state.appVersion;
    const manager = new GitCdnManager(params.cdnRootDirectory,params.gitResDirectory, {
        env: state.envName,
        platform: state.platform,
        safeModel: true,
    });
    let cdnDirectory = manager.CdnRootDirectory;
    //这里只自动上传配置和资源，暂时咩有考虑字体
    let confs = await manager.getAllFilesInCurrentVersionWithPrefix(state.cfgVersion, [`${params.platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`]);
    let ress = await manager.getAllFilesInCurrentVersionWithPrefix(state.resVersion, [`${params.platform}_${CDN_VERSION_PREFIX.BUILD_RES}`]);
    let allfiles = [ ...(confs || []),...(ress||[])];
    logWithKey('uploadToCosWithParam',`needUpdateFilesCount:${allfiles.length}`);

    let existkeys = await checkAlreadyExistCosfiles();
    logWithKey('uploadToCosWithParam', `exist keys cnt:${existkeys.length}`);

    let uploadResult: boolean;
    try {
        uploadResult = await uploadByCosSdK(allfiles, cdnDirectory, existkeys);
    } catch (err) {
        logWithKey('uploadToCosWithParam', `uploadByCosSdK exception: ${err}`);
        throw new Error(`上传cos失败，uploadByCosSdK异常，详情请查看日志: ${err}`);
    }
    if (uploadResult === false) {
        logWithKey('uploadToCosWithParam', `upload failed`);
        throw new Error(`上传cos失败,详情求查看日志`)
    } else {
        logWithKey('uploadToCosWithParam', `upload success`);
    }
    logFunctionEnd('uploadToCosWithParam')
}


/**
 * 命令行将本地的文件上传的cos
 * @param localfiles
 */
async function uploadByCoscli(source_path: string, destination_path: string): Promise<boolean> {
    // await $`coscli cp .cos.yaml cos://xiuxian-1251001614/test.yaml`;
    try {
        let output = await $`coscli -v`;
        console.log(`${output.stdout.trim()}`);
        // ./coscli cp <source_path> <destination_path> [flags]
        output = await $`coscli cp ${source_path} ${destination_path} -r`;
        var retInfo = output.stdout.trim();
        if (retInfo.startsWith("succeed")) {
            return true;
        }
    } catch (e) {
        console.error(e);
    }
    return false;
}

/**
 * sdk 上传文件到cos
 * 上传可能会失败，如果失败，那么多次上传
 */
export async function uploadByCosSdK(localfiles: string[], baseDir: string,existKeys:string[]): Promise<boolean> {
    logFunctionStart('uploadByCosSdK',`baseDir:${baseDir}`);
    const UPLOAD_TO_COS_TIMER = 'UPLOAD_TO_COS_TIMER';
    Timer.start(UPLOAD_TO_COS_TIMER);
    const cos = new COS({
        SecretId: SecretId, // 推荐使用环境变量获取；用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
        SecretKey: SecretKey, // 推荐使用环境变量获取；用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
    });

    //定义需要上传的文件列表
    let uploadFiles = localfiles;
    let retry: boolean = false;
    let uploadIndex = 0;
    let MAX_UPLOAD_CNT = 10;
    let uploadSuccess: boolean = false;
    do {
        uploadIndex++;
    const cosfiles:{"Bucket":string,"Region":string,"FilePath":string,"Key":string}[] = [];
        for (var localfile of uploadFiles) {
        let key = localfile.substring(baseDir.length+1).replace(/\\/g,'/') ;
            if(existKeys.indexOf(key) === -1) {
        logWithKey('uploadByCosSdK',`key:${key},localfile:${localfile}`);
        cosfiles.push({
            Bucket:Bucket,Region:Region,FilePath:localfile,Key:key
        });
    }
        }
        logWithKey('uploadByCosSdK', `Upload Start,uploadIndex:${uploadIndex},upload total cosfiles count:${cosfiles.length}`);

        if(cosfiles.length > 0){
            let uploadFilesResult = await cos.uploadFiles({
                files: cosfiles, onProgress: (params: ProgressInfo) => {
            logWithKey('uploadByCosSdK',`progress:${JSON.stringify(params)}`);
                }
            }).catch(console.error);

        let allvalid = true;
        let fails:string[] = []
            if (uploadFilesResult != null && uploadFilesResult.files != null) {
        for(let file of uploadFilesResult.files){
            if(file?.data?.statusCode != 200){
                allvalid = false;
                fails.push(file.options.FilePath!);
                logWithKey('uploadByCosSdK',`upload error:${JSON.stringify(file)}`);
            }
        }
                logWithKey('uploadByCosSdK', `Upload Result,uploadIndex:${uploadIndex},success:${allvalid}`);
    }
            retry = !allvalid
            if (retry)
                uploadFiles = fails
            else
                uploadSuccess = true;
        }else{
            uploadSuccess = true;
        }
        await delay(1000);
    } while (retry && uploadIndex < MAX_UPLOAD_CNT)

    logWithKey('uploadByCosSdK', `Upload Finish,success:${uploadSuccess},duration:${Timer.formatDuration(Timer.end(UPLOAD_TO_COS_TIMER))}`);
    return uploadSuccess;
}

/**
 * 检测cos中已经存在的文件列表
 */
export async function checkAlreadyExistCosfiles(): Promise<string[] > {
    logFunctionStart('checkAlreadyExistCosfiles');
    const cos = new COS({
        SecretId: SecretId, // 推荐使用环境变量获取；用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
        SecretKey: SecretKey, // 推荐使用环境变量获取；用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参考https://cloud.tencent.com/document/product/598/37140
    });
    let existKeys:string[] = [];
    let marker: COS.Key | undefined;
    do {
        let ret = await cos.getBucket({
            Bucket: Bucket, // 填入您自己的存储桶，必须字段
            Region: Region,  // 存储桶所在地域，例如ap-beijing，必须字段
            Prefix: '',
            Marker: marker,
        });
        marker = ret.NextMarker;

        if (ret.Contents && ret.Contents.length > 0) {
            ret.Contents.forEach((item: { Key: string }) => {
                if (item.Key) {
                    existKeys.push(item.Key); // 将 Key 添加到 existKeys 列表
                }
            });
        }
        logWithKey('checkAlreadyExistCosfiles', `NextMarker:${marker}`);
        await delay(500);
    }
    while (isInvalidStr(marker) === false)
    logWithKey(`checkAlreadyExistCosfiles`,`cos total count:${existKeys.length}`);
    logFunctionEnd(`checkAlreadyExistCosfiles`);
    return existKeys;
}

