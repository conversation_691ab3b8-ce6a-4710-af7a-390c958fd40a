%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 22400010}
  - component: {fileID: 22300000}
  - component: {fileID: 11400012}
  - component: {fileID: 5715800781779736296}
  - component: {fileID: 1549548531243780997}
  m_Layer: 5
  m_Name: battle_panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &22400010
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100010}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7345722372248988896}
  - {fileID: 7628462148353335855}
  - {fileID: 8731823509253027201}
  - {fileID: 3420336458574444445}
  - {fileID: 6528520090664588152}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &22300000
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100010}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &11400012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 46792503
--- !u!114 &5715800781779736296
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 34df9afaeb0a5984ca44afa94a91216d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  objectArray: []
  objectKeyArray:
  - key: speedBtn
    uiType: 54
    gameObject: {fileID: 3010475700095681038}
  - key: automatic_opening
    uiType: 54
    gameObject: {fileID: 6278037247263661324}
  - key: backBtn
    uiType: 54
    gameObject: {fileID: 3938624929717014786}
  - key: container
    uiType: 18
    gameObject: {fileID: 1083509979150086630}
  - key: speedTxt
    uiType: 30
    gameObject: {fileID: 1093287817720204355}
  - key: automaticTxt
    uiType: 30
    gameObject: {fileID: 4526953751519700165}
  - key: group
    uiType: 18
    gameObject: {fileID: 1083509979150086630}
  - key: automatic_closing
    uiType: 9
    gameObject: {fileID: 4362691378125528284}
--- !u!114 &1549548531243780997
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59c3a2db266eedb478649049579f7c42, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  isHide3dCamera: 0
  isConsiderSafeArea: 1
  isHideHudUI: 0
  rect: {fileID: 22400010}
--- !u!1 &166627391909516180
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3950972076835341814}
  - component: {fileID: 5198741451338602541}
  - component: {fileID: 7346986710912388947}
  m_Layer: 5
  m_Name: hero_battling_item
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3950972076835341814
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166627391909516180}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2094935589738116334}
  - {fileID: 3235548454295133508}
  - {fileID: 6905982433906858907}
  m_Father: {fileID: 419020617892108307}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 730, y: -110}
  m_SizeDelta: {x: 194, y: 220}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &5198741451338602541
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166627391909516180}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 194
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!114 &7346986710912388947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166627391909516180}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 34df9afaeb0a5984ca44afa94a91216d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  objectArray: []
  objectKeyArray:
  - key: heroHpSlider
    uiType: 13
    gameObject: {fileID: 4402251429307741259}
  - key: battlepve_heard_new
    uiType: 7
    gameObject: {fileID: 6908815361091427021}
  - key: support
    uiType: 2
    gameObject: {fileID: 7695876148486829027}
  - key: itemBtn
    uiType: 54
    gameObject: {fileID: 4562682755113059689}
  - key: cdLeftTxt
    uiType: 30
    gameObject: {fileID: 535098487460063400}
  - key: root
    uiType: 8
    gameObject: {fileID: 166627391909516180}
--- !u!1 &360396850388122243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3420336458574444445}
  - component: {fileID: 3439617605032871722}
  m_Layer: 5
  m_Name: scroll_view
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3420336458574444445
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 360396850388122243}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 549414867308363111}
  m_Father: {fileID: 22400010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 120}
  m_SizeDelta: {x: 0, y: 238}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &3439617605032871722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 360396850388122243}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 419020617892108307}
  m_Horizontal: 0
  m_Vertical: 0
  m_MovementType: 1
  m_Elasticity: 0.1
  m_Inertia: 1
  m_DecelerationRate: 0.135
  m_ScrollSensitivity: 1
  m_Viewport: {fileID: 549414867308363111}
  m_HorizontalScrollbar: {fileID: 0}
  m_VerticalScrollbar: {fileID: 0}
  m_HorizontalScrollbarVisibility: 0
  m_VerticalScrollbarVisibility: 0
  m_HorizontalScrollbarSpacing: 0
  m_VerticalScrollbarSpacing: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1083509979150086630
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 419020617892108307}
  - component: {fileID: 8070430915284330992}
  - component: {fileID: 6121682142095122674}
  m_Layer: 5
  m_Name: group
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &419020617892108307
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1083509979150086630}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3950972076835341814}
  m_Father: {fileID: 549414867308363111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 118.99998}
  m_SizeDelta: {x: 0, y: 225}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &8070430915284330992
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1083509979150086630}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 20
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 1
  m_Spacing: 18
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &6121682142095122674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1083509979150086630}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6dbce389173ed764392cbd28ee6ace45, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_templete: {fileID: 166627391909516180}
  m_scroll_rect: {fileID: 3439617605032871722}
  isVertical: 0
  isJumpWithAnim: 0
  content: {fileID: 419020617892108307}
  itemWidth: 0
  itemHeight: 0
  spacing: 0
  default_coroutine_num: 0
  m_can_selected: 1
  m_refresh_interval: 0
--- !u!1 &1093287817720204355
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5945613765535365329}
  - component: {fileID: 2031583118479736388}
  - component: {fileID: 6260732797153821619}
  - component: {fileID: 8560767107172183248}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5945613765535365329
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093287817720204355}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8018205928206834040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 3.6}
  m_SizeDelta: {x: 108, y: 90}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2031583118479736388
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093287817720204355}
  m_CullTransparentMesh: 1
--- !u!114 &6260732797153821619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093287817720204355}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: x2
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5823828e9571933449d8a0bf9535ecc5, type: 2}
  m_sharedMaterial: {fileID: 0}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &8560767107172183248
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093287817720204355}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56537187ae161044e841aa6406888571, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  presetMaterial: {fileID: 1561051330912826401, guid: 5823828e9571933449d8a0bf9535ecc5,
    type: 2}
  shouldOverrideFaceParameters: 1
  faceParameters:
    color: {r: 1, g: 1, b: 1, a: 1}
    softness: 0
    dilate: 0.2
  shouldOverrideOutlineParameters: 1
  outlineParameters:
    color: {r: 0.64705884, g: 0.4431373, b: 0, a: 1}
    thickness: 0.2
  shouldOverrideUnderlayParameters: 1
  underlayParameters:
    enable: 1
    color: {r: 0.64705884, g: 0.44313726, b: 0, a: 1}
    offsetX: 0
    offsetY: -1
    dilate: 1
    softness: 0
    offset1X: 0
    offset1Y: 0
    dilate1: 0
    softness1: 0
  shouldOverrideGlowParameters: 0
  glowParameters:
    enable: 0
    color: {r: 1, g: 1, b: 1, a: 1}
    offset: 0
    inner: 0
    outer: 0
    power: 0
--- !u!1 &1794912087297298394
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8849053700766191311}
  - component: {fileID: 4487922070646264468}
  - component: {fileID: 7746169883055695841}
  - component: {fileID: 5009022166763221001}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8849053700766191311
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794912087297298394}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4858170816569024829}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0.5}
  m_SizeDelta: {x: 108, y: 90}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4487922070646264468
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794912087297298394}
  m_CullTransparentMesh: 1
--- !u!114 &7746169883055695841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794912087297298394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Auto
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5823828e9571933449d8a0bf9535ecc5, type: 2}
  m_sharedMaterial: {fileID: 0}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 34
  m_fontSizeBase: 34
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &5009022166763221001
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794912087297298394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56537187ae161044e841aa6406888571, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  presetMaterial: {fileID: 1561051330912826401, guid: 5823828e9571933449d8a0bf9535ecc5,
    type: 2}
  shouldOverrideFaceParameters: 1
  faceParameters:
    color: {r: 1, g: 1, b: 1, a: 1}
    softness: 0
    dilate: 0.2
  shouldOverrideOutlineParameters: 1
  outlineParameters:
    color: {r: 0.64705884, g: 0.4431373, b: 0, a: 1}
    thickness: 0.2
  shouldOverrideUnderlayParameters: 1
  underlayParameters:
    enable: 1
    color: {r: 0.64705884, g: 0.44313726, b: 0, a: 1}
    offsetX: 0
    offsetY: -1
    dilate: 1
    softness: 0
    offset1X: 0
    offset1Y: 0
    dilate1: 0
    softness1: 0
  shouldOverrideGlowParameters: 0
  glowParameters:
    enable: 0
    color: {r: 1, g: 1, b: 1, a: 1}
    offset: 0
    inner: 0
    outer: 0
    power: 0
--- !u!1 &3010475700095681038
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7345722372248988896}
  - component: {fileID: 1982635557006670724}
  m_Layer: 5
  m_Name: speed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7345722372248988896
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3010475700095681038}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8018205928206834040}
  m_Father: {fileID: 22400010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -84, y: 395}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1982635557006670724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3010475700095681038}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 2
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 21300000, guid: c7624871df4c74d49b4a18c5a0f6ff9e,
      type: 3}
    m_PressedSprite: {fileID: 21300000, guid: c7624871df4c74d49b4a18c5a0f6ff9e, type: 3}
    m_SelectedSprite: {fileID: 21300000, guid: c7624871df4c74d49b4a18c5a0f6ff9e, type: 3}
    m_DisabledSprite: {fileID: 21300000, guid: b9049f12ff424404db5b0bb5444dd9da, type: 3}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  soundName: Play_sfx_ui_button_common
  ButtonText: {fileID: 0}
  textGrayColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  outlineGrayColor: {r: 0.44313726, g: 0.44313726, b: 0.44313726, a: 1}
  UIAtlasImage: {fileID: 0}
  clickCd: 0.5
--- !u!1 &3643819198782109087
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8018205928206834040}
  - component: {fileID: 1622634236953624061}
  - component: {fileID: 3181700395237218404}
  - component: {fileID: 6910810507453125023}
  m_Layer: 5
  m_Name: icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8018205928206834040
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3643819198782109087}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5945613765535365329}
  - {fileID: 7607953377639547276}
  m_Father: {fileID: 7345722372248988896}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1622634236953624061
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3643819198782109087}
  m_CullTransparentMesh: 1
--- !u!114 &3181700395237218404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3643819198782109087}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c7624871df4c74d49b4a18c5a0f6ff9e, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &6910810507453125023
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3643819198782109087}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5882353}
  m_EffectDistance: {x: 1, y: -7}
  m_UseGraphicAlpha: 1
--- !u!1 &3938624929717014786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6528520090664588152}
  - component: {fileID: 2394880487495719417}
  m_Layer: 5
  m_Name: back
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6528520090664588152
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3938624929717014786}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7328188037513155823}
  m_Father: {fileID: 22400010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 50, y: -50}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2394880487495719417
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3938624929717014786}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  soundName: Play_sfx_ui_button_common
  ButtonText: {fileID: 0}
  textGrayColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  outlineGrayColor: {r: 0.44313726, g: 0.44313726, b: 0.44313726, a: 1}
  UIAtlasImage: {fileID: 0}
  clickCd: 0.5
--- !u!1 &4362691378125528284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4858170816569024829}
  - component: {fileID: 6056608229033009529}
  - component: {fileID: 5605227541747386765}
  - component: {fileID: 6920980201110230019}
  - component: {fileID: 8305729605678924886}
  m_Layer: 5
  m_Name: icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4858170816569024829
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4362691378125528284}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8849053700766191311}
  - {fileID: 3914717807992762596}
  m_Father: {fileID: 8731823509253027201}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6056608229033009529
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4362691378125528284}
  m_CullTransparentMesh: 1
--- !u!114 &5605227541747386765
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4362691378125528284}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: b9049f12ff424404db5b0bb5444dd9da, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &6920980201110230019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4362691378125528284}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5882353}
  m_EffectDistance: {x: 1, y: -7}
  m_UseGraphicAlpha: 1
--- !u!114 &8305729605678924886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4362691378125528284}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 2
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 21300000, guid: b9049f12ff424404db5b0bb5444dd9da, type: 3}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 5605227541747386765}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  soundName: Play_sfx_ui_button_common
  ButtonText: {fileID: 7746169883055695841}
  textGrayColor: {r: 1, g: 1, b: 1, a: 1}
  outlineGrayColor: {r: 0.44313726, g: 0.44313726, b: 0.44313726, a: 1}
  UIAtlasImage: {fileID: 0}
  clickCd: 0.5
--- !u!1 &4526953751519700165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2564645827059437636}
  - component: {fileID: 83514803189607081}
  - component: {fileID: 8722149163748798950}
  - component: {fileID: 1439390296189452845}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2564645827059437636
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526953751519700165}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7006192453534271283}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0.5}
  m_SizeDelta: {x: 108, y: 90}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &83514803189607081
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526953751519700165}
  m_CullTransparentMesh: 1
--- !u!114 &8722149163748798950
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526953751519700165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Auto
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5823828e9571933449d8a0bf9535ecc5, type: 2}
  m_sharedMaterial: {fileID: 0}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 34
  m_fontSizeBase: 34
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &1439390296189452845
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526953751519700165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56537187ae161044e841aa6406888571, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  presetMaterial: {fileID: 1561051330912826401, guid: 5823828e9571933449d8a0bf9535ecc5,
    type: 2}
  shouldOverrideFaceParameters: 1
  faceParameters:
    color: {r: 1, g: 1, b: 1, a: 1}
    softness: 0
    dilate: 0.2
  shouldOverrideOutlineParameters: 1
  outlineParameters:
    color: {r: 0.64705884, g: 0.4431373, b: 0, a: 1}
    thickness: 0.2
  shouldOverrideUnderlayParameters: 1
  underlayParameters:
    enable: 1
    color: {r: 0.64705884, g: 0.44313726, b: 0, a: 1}
    offsetX: 0
    offsetY: -1
    dilate: 1
    softness: 0
    offset1X: 0
    offset1Y: 0
    dilate1: 0
    softness1: 0
  shouldOverrideGlowParameters: 0
  glowParameters:
    enable: 0
    color: {r: 1, g: 1, b: 1, a: 1}
    offset: 0
    inner: 0
    outer: 0
    power: 0
--- !u!1 &5222261222517187737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3914717807992762596}
  - component: {fileID: 8368386154895299375}
  - component: {fileID: 7014195892995975198}
  - component: {fileID: 8506964615533067830}
  m_Layer: 5
  m_Name: lock_icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3914717807992762596
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5222261222517187737}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.79999995, y: 0.79999995, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4858170816569024829}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -48}
  m_SizeDelta: {x: 34, y: 42}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8368386154895299375
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5222261222517187737}
  m_CullTransparentMesh: 1
--- !u!114 &7014195892995975198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5222261222517187737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 5ea96c8ce21632543ad7f8aeb7316610, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &8506964615533067830
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5222261222517187737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 68d90a36b9dbfb845b2306cfdf7a8811, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _strength: 1
  _renderSpace: 0
  _expand: 1
  _method: 0
  _size: 5
  _distanceShape: 2
  _blur: 0
  _softness: 2
  _sourceAlpha: 1
  _color: {r: 0, g: 0, b: 0, a: 1}
  _texture: {fileID: 0}
  _textureOffset: {x: 0, y: 0}
  _textureScale: {x: 1, y: 1}
  _direction: 2
--- !u!1 &6278037247263661324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7006192453534271283}
  - component: {fileID: 6022863884666572681}
  - component: {fileID: 6804923436231193198}
  - component: {fileID: 5996040423256265687}
  - component: {fileID: 4081471800267695799}
  m_Layer: 5
  m_Name: icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7006192453534271283
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278037247263661324}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2564645827059437636}
  - {fileID: 9116927598406283401}
  m_Father: {fileID: 7628462148353335855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6022863884666572681
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278037247263661324}
  m_CullTransparentMesh: 1
--- !u!114 &6804923436231193198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278037247263661324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c7624871df4c74d49b4a18c5a0f6ff9e, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &5996040423256265687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278037247263661324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfabb0440166ab443bba8876756fdfa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5882353}
  m_EffectDistance: {x: 1, y: -7}
  m_UseGraphicAlpha: 1
--- !u!114 &4081471800267695799
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278037247263661324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 2
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 21300000, guid: b9049f12ff424404db5b0bb5444dd9da, type: 3}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 6804923436231193198}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  soundName: Play_sfx_ui_button_common
  ButtonText: {fileID: 8722149163748798950}
  textGrayColor: {r: 1, g: 1, b: 1, a: 1}
  outlineGrayColor: {r: 0.44313726, g: 0.44313726, b: 0.44313726, a: 1}
  UIAtlasImage: {fileID: 0}
  clickCd: 0.5
--- !u!1 &6738669508221486021
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2094935589738116334}
  - component: {fileID: 4383631145504204665}
  - component: {fileID: 3360084821155374313}
  m_Layer: 5
  m_Name: hero_bg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2094935589738116334
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6738669508221486021}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3950972076835341814}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 97, y: -193}
  m_SizeDelta: {x: 187, y: 54}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4383631145504204665
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6738669508221486021}
  m_CullTransparentMesh: 1
--- !u!114 &3360084821155374313
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6738669508221486021}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.10196079, g: 0.050980397, b: 0.078431375, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: e96685db0139e7946ad19e0cd34acd5c, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &7000030064251713083
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9116927598406283401}
  - component: {fileID: 2938820739659420712}
  - component: {fileID: 3822538880313097867}
  - component: {fileID: 3246688828859916249}
  m_Layer: 5
  m_Name: lock_icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &9116927598406283401
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7000030064251713083}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.79999995, y: 0.79999995, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7006192453534271283}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -48}
  m_SizeDelta: {x: 34, y: 42}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2938820739659420712
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7000030064251713083}
  m_CullTransparentMesh: 1
--- !u!114 &3822538880313097867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7000030064251713083}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 5ea96c8ce21632543ad7f8aeb7316610, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &3246688828859916249
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7000030064251713083}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 68d90a36b9dbfb845b2306cfdf7a8811, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _strength: 1
  _renderSpace: 0
  _expand: 1
  _method: 0
  _size: 5
  _distanceShape: 2
  _blur: 0
  _softness: 2
  _sourceAlpha: 1
  _color: {r: 0, g: 0, b: 0, a: 1}
  _texture: {fileID: 0}
  _textureOffset: {x: 0, y: 0}
  _textureScale: {x: 1, y: 1}
  _direction: 2
--- !u!1 &7825248900689282187
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7607953377639547276}
  - component: {fileID: 7435933190428987431}
  - component: {fileID: 3161170039054103472}
  - component: {fileID: 3320147375201021133}
  m_Layer: 5
  m_Name: lock_icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7607953377639547276
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7825248900689282187}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.79999995, y: 0.79999995, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8018205928206834040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -48}
  m_SizeDelta: {x: 34, y: 42}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7435933190428987431
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7825248900689282187}
  m_CullTransparentMesh: 1
--- !u!114 &3161170039054103472
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7825248900689282187}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 5ea96c8ce21632543ad7f8aeb7316610, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &3320147375201021133
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7825248900689282187}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 68d90a36b9dbfb845b2306cfdf7a8811, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _strength: 1
  _renderSpace: 0
  _expand: 1
  _method: 0
  _size: 5
  _distanceShape: 2
  _blur: 0
  _softness: 2
  _sourceAlpha: 1
  _color: {r: 0, g: 0, b: 0, a: 1}
  _texture: {fileID: 0}
  _textureOffset: {x: 0, y: 0}
  _textureScale: {x: 1, y: 1}
  _direction: 2
--- !u!1 &8288525802188540655
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 549414867308363111}
  - component: {fileID: 8460580931081450807}
  m_Layer: 5
  m_Name: viewport
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &549414867308363111
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8288525802188540655}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 419020617892108307}
  m_Father: {fileID: 3420336458574444445}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &8460580931081450807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8288525802188540655}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding: {x: 0, y: 0, z: 0, w: 0}
  m_Softness: {x: 0, y: 0}
--- !u!1 &8434671488393936717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7328188037513155823}
  - component: {fileID: 964336354068839352}
  - component: {fileID: 8549473103271443498}
  m_Layer: 5
  m_Name: icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7328188037513155823
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8434671488393936717}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6528520090664588152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 76, y: 76}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &964336354068839352
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8434671488393936717}
  m_CullTransparentMesh: 1
--- !u!114 &8549473103271443498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8434671488393936717}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 024cb38b12eccf44ab5ae8a29486a0a7, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &8773579434736984274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7628462148353335855}
  m_Layer: 5
  m_Name: automatic
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7628462148353335855
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8773579434736984274}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7006192453534271283}
  m_Father: {fileID: 22400010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -84, y: 576}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &9176566235841278011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8731823509253027201}
  m_Layer: 5
  m_Name: automatic_closing
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8731823509253027201
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9176566235841278011}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4858170816569024829}
  m_Father: {fileID: 22400010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -84, y: 576}
  m_SizeDelta: {x: 108, y: 108}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &4105083308922682052
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3950972076835341814}
    m_Modifications:
    - target: {fileID: 291440038017285106, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 185.99995
      objectReference: {fileID: 0}
    - target: {fileID: 291440038017285106, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.999996
      objectReference: {fileID: 0}
    - target: {fileID: 291440038017285106, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -106.99998
      objectReference: {fileID: 0}
    - target: {fileID: 291440038017285106, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 73
      objectReference: {fileID: 0}
    - target: {fileID: 427806629341122191, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 204
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -277
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2638994607663120853, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2638994607663120853, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Type
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: 21300000, guid: 9ad705176e2d17a4c929d64efdd98292,
        type: 3}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.b
      value: 0.18039216
      objectReference: {fileID: 0}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.g
      value: 0.9450981
      objectReference: {fileID: 0}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.r
      value: 0.34901962
      objectReference: {fileID: 0}
    - target: {fileID: 3417289524645235360, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_FillAmount
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4487749601157277283, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Name
      value: slider
      objectReference: {fileID: 0}
    - target: {fileID: 4487749601157277283, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5746923972989741343, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: 21300000, guid: e96685db0139e7946ad19e0cd34acd5c,
        type: 3}
    - target: {fileID: 5746923972989741343, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.b
      value: 0.18039216
      objectReference: {fileID: 0}
    - target: {fileID: 5746923972989741343, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.g
      value: 0.9450981
      objectReference: {fileID: 0}
    - target: {fileID: 5746923972989741343, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Color.r
      value: 0.34901962
      objectReference: {fileID: 0}
    - target: {fileID: 6130831237546063232, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_Value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6283534646705691049, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6346807661687948745, guid: 6e272cd09e985b04090e5b60b28dbb41,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6e272cd09e985b04090e5b60b28dbb41, type: 3}
--- !u!224 &3235548454295133508 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1449831048638359424, guid: 6e272cd09e985b04090e5b60b28dbb41,
    type: 3}
  m_PrefabInstance: {fileID: 4105083308922682052}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4402251429307741259 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 427806629341122191, guid: 6e272cd09e985b04090e5b60b28dbb41,
    type: 3}
  m_PrefabInstance: {fileID: 4105083308922682052}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6153833303272003234
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3950972076835341814}
    m_Modifications:
    - target: {fileID: 155478037289962852, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 1561051330912826401, guid: 5823828e9571933449d8a0bf9535ecc5,
        type: 2}
    - target: {fileID: 764158159100057637, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 178
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 186
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 97
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -95
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1267375638767104811, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1393023114883445990, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1393023114883445990, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1464459208005130809, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1505605047107297113, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1521501326935985927, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1568519479533599635, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_Name
      value: battle_pve_main_selectItem
      objectReference: {fileID: 0}
    - target: {fileID: 1568519479533599635, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1960426517978720502, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2019273850665774166, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2216664219711689911, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2246818226562633482, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2518591925434104498, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2518591925434104498, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2518591925434104498, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2518591925434104498, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3104608948770217532, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3941690137953466595, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3970869943571693444, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4144101817336523111, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4588024931145114945, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4668801651024334390, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4803985878080828447, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4945965456436551127, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_Texture
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5076431870472128197, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5116969341297629629, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: objectKeyArray.Array.size
      value: 13
      objectReference: {fileID: 0}
    - target: {fileID: 5116969341297629629, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: objectKeyArray.Array.data[12].key
      value: support_bg
      objectReference: {fileID: 0}
    - target: {fileID: 5116969341297629629, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: objectKeyArray.Array.data[12].uiType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5116969341297629629, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: objectKeyArray.Array.data[12].gameObject
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5178001048279400866, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6003830744117970031, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024568132780262387, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024568132780262387, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7317431436401092120, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7623625293582399000, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 7623625293582399000, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 7623625293582399000, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7704855369676349361, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7704855369676349361, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7704855369676349361, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 85
      objectReference: {fileID: 0}
    - target: {fileID: 7704855369676349361, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -37
      objectReference: {fileID: 0}
    - target: {fileID: 8147962125823844773, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8202561906246051176, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8557197227148748356, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8810133501394806976, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8810133501394806976, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8810133501394806976, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8810133501394806976, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8810133501394806976, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.3
      objectReference: {fileID: 0}
    - target: {fileID: 8932094356252495022, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_FillAmount
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9077675252560651291, guid: f3c1e8362262a9648912d8b6690e66dc,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f3c1e8362262a9648912d8b6690e66dc, type: 3}
--- !u!1 &535098487460063400 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5912034395851128330, guid: f3c1e8362262a9648912d8b6690e66dc,
    type: 3}
  m_PrefabInstance: {fileID: 6153833303272003234}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4562682755113059689 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7653631181802410955, guid: f3c1e8362262a9648912d8b6690e66dc,
    type: 3}
  m_PrefabInstance: {fileID: 6153833303272003234}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &6905982433906858907 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 770173431741418809, guid: f3c1e8362262a9648912d8b6690e66dc,
    type: 3}
  m_PrefabInstance: {fileID: 6153833303272003234}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &6908815361091427021 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 758790839662201967, guid: f3c1e8362262a9648912d8b6690e66dc,
    type: 3}
  m_PrefabInstance: {fileID: 6153833303272003234}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &7695876148486829027 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4588024931145114945, guid: f3c1e8362262a9648912d8b6690e66dc,
    type: 3}
  m_PrefabInstance: {fileID: 6153833303272003234}
  m_PrefabAsset: {fileID: 0}
