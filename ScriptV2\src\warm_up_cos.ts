/**
 * 预热cos的资源
 * https://cloud.tencent.com/document/product/228/37872
 * <AUTHOR>
 * @date 2025-01-14
 */
import {promises as fs} from 'fs';
import {delay, getFormatTimeShow, logFunctionEnd, logFunctionStart, logWithKey, urlJoin} from "./utility.ts";
import {Client} from "tencentcloud-sdk-nodejs-cdn/tencentcloud/services/cdn/v20180606/cdn_client";
import {
    DescribePushQuotaResponse,
    DescribePushTasksResponse,
    PushTask,
    PushUrlsCacheResponse,
    Quota
} from "tencentcloud-sdk-nodejs-cdn/tencentcloud/services/cdn/v20180606/cdn_models";
import {GitCdnManager} from "./git_cdn_manager.ts";
import {CDN_VERSION_PREFIX} from "./const.ts";
import {Command} from 'commander';
import path from "path";
import {format, subDays} from "date-fns";

//敏感信息从环境变量中获取
const SecretId = process.env.COS_SECRET_ID ?? '';
const SecretKey = process.env.COS_SECRET_KEY ?? '';
console.log(`SecretId:${SecretId},SecretKey:${SecretKey}`)

interface InputParms {
    platform: string
    env: string
    baseUrl: string
    cdnRootDirectory: string,
    gitResDirectory: string,
    //资源版本
    resVersion: string,
    //配置版本，可能跟资源不一致
    cfgVersion: string,
    area: 'mainland' | 'overseas',
}

/**
 * cdn预热记录文件
 */
interface TotalCosCDNWarmRecordData {
    refreshTime?: string,
    //根域名
    baseURL?: string,
    //平台
    platform?: string,
    //环境
    env?: string,
    resVersion?: string,
    cfgVersion?: string,
    //预热失败的数量
    warmupFailCnt?: number,
    //预热中的数量
    warmupProcessingCnt?: number,
    //预热成功的数量
    warmUpSuccessCnt?: number,
    warmUpInvalidCnt?: number,
    //总的预热过数量
    warmUpTotalCnt?: number,
    //还没有预热的数量
    notWarmupCnt?: number,
    //用量
    pushQuota?: DescribePushQuotaResponse,
    nowPushQuota?: DescribePushQuotaResponse,
    //预热返回的taskid
    warmUpTaskIds?: Array<string>,
    //记录历史文件
    cosCDNWarmItems?: Array<Partial<CosCDNWarmItem>>,
}

/**
 * 单词预热文件
 */
interface CosCDNWarmItem {
    key: string;
    localPath: string;
    fullURL: string,
    pushTask: PushTask
}

async function loadLogFile(filePath: string): Promise<TotalCosCDNWarmRecordData | null> {

    try {
        // 异步读取文件内容
        const fileContent = await fs.readFile(filePath, 'utf-8');
        // 解析 JSON 数据
        const data = JSON.parse(fileContent) as TotalCosCDNWarmRecordData;
        return data;
    } catch (error) {
        // 捕获错误
        console.error(`Error reading or parsing file at ${filePath}:`, error);
    }
    return null;
}

async function saveLogFile(data: TotalCosCDNWarmRecordData, filePath: string) {
    try {
        const jsonString = JSON.stringify(data, null, 2); // 格式化 JSON：缩进 2 个空格
        await fs.writeFile(filePath, jsonString, 'utf-8');
        console.log(`File successfully written to ${filePath}`);
    } catch (error) {
        console.error(`Error writing file to ${filePath}:`, error);
        throw error;
    }
}


/**
 * warmup
 * @param inputParms
 */
export async function buildCosCDNWarmup(inputParms: InputParms): Promise<void> {
    logFunctionStart(`buildCosCDNWarmup`, JSON.stringify(inputParms));
    try {
        //首先读取本地存储的预热的日志文件
        const logpath = path.join(inputParms.cdnRootDirectory, inputParms.platform, inputParms.env, `cos_warm_up_${inputParms.cfgVersion}_${inputParms.resVersion}.log`)
        let logData = await loadLogFile(logpath);
        if (logData == null) {
            logData = {}
        }
        logData.refreshTime = getFormatTimeShow()
        logData.platform = inputParms.platform
        logData.env = inputParms.env
        logData.baseURL = inputParms.baseUrl
        logData.resVersion = inputParms.resVersion;
        logData.cfgVersion = inputParms.cfgVersion;

        //1.-------------------------------------------------- 查询用量--------------------------------------
        let pushQuota = await checkWarmupQuota();
        logData.pushQuota = pushQuota;
        //这里获取当前中国大旭需要预热的数量
        let nowQuota: Quota | undefined
        if (pushQuota.UrlPush.length > 0) {
            for (const quota of pushQuota.UrlPush) {
                if (quota.Area == inputParms.area) {
                    nowQuota = quota
                }
            }
        }
        if (nowQuota == undefined) {
            throw new Error(`${inputParms.area} is invalid `)
        }

        //3. ------------------------------------加载本地的版本所有文件--------------------------------
        const manager = new GitCdnManager(inputParms.cdnRootDirectory, inputParms.gitResDirectory, {
            env: inputParms.env,
            platform: inputParms.platform,
            safeModel: true,
        });
        let cdnDirectory = manager.CdnRootDirectory;
        //当前所有的文件
        let cfgs = await manager.getAllFilesInCurrentVersionWithPrefix(inputParms.cfgVersion,
            [`${inputParms.platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`]);
        let ress = await manager.getAllFilesInCurrentVersionWithPrefix(inputParms.resVersion,
            [`${inputParms.platform}_${CDN_VERSION_PREFIX.BUILD_RES}`]);
        let allfiles = [...(cfgs||[]),...(ress||[])];
        //所有的文件
        logData.warmUpTotalCnt = allfiles.length;
        //获取素有的keys
        const keysInVersion = new Set<string>();
        for (const file of allfiles) {
            let key = file.substring(cdnDirectory.length + 1).replace(/\\/g, '/');
            keysInVersion.add(key);
        }

        //2. ---------------------------------------------刷新一下历史-----------------------------
        if (logData.cosCDNWarmItems == null)
            logData.cosCDNWarmItems = [];
        if (logData.warmUpTaskIds == null)
            logData.warmUpTaskIds = [];
        //预存的预热的数据
        let warmUpFileData: Map<string, Partial<CosCDNWarmItem>> = new Map(logData.cosCDNWarmItems.map(item => [item.key!, item]));
        //记录状态
        let pushSuccessCnt = 0;
        let pushFailCnt = 0;
        let pushProcessCnt = 0;
        let pushInvalidCnt = 0;
        //所有的预热历史查询
        let describePushTasksResponses = await checkWarmupHistory(logData.warmUpTaskIds);
        for (const pushTask of describePushTasksResponses) {
            //将记录的结果存储到log中
            if (pushTask.PushLogs != null && pushTask.PushLogs.length > 0) {
                for (const pushLog of pushTask.PushLogs) {
                    let url = pushLog.Url;
                    if (url != null) {
                        let urlPrefix = urlJoin(inputParms.baseUrl, inputParms.platform, inputParms.env);
                        if (url.startsWith(urlPrefix)) {
                            let key = url.substring(inputParms.baseUrl.length + 1);
                            //如果不是当前版本的key,那么过滤掉
                            if (keysInVersion.has(key) === false)
                                continue;

                            let item: Partial<CosCDNWarmItem>
                            if (warmUpFileData.has(key) === false) {
                                item = {}
                                warmUpFileData.set(key, item)
                            } else {
                                item = warmUpFileData.get(key)!;
                            }
                            item.key = key
                            item.fullURL = url
                            item.pushTask = pushLog
                            let taskId = pushLog.TaskId;
                            if (taskId != null) {
                                logData.warmUpTaskIds.push(taskId);
                            }
                        }
                    }
                }
            }
        }
        //4.----------------------过滤掉已经预热过的文件---------------------------
        let need2warmup = []
        for (const key of keysInVersion) {
            if (warmUpFileData.has(key) === false) {
                //说明没有预热过，
                let warmurl = urlJoin(inputParms.baseUrl, key);
                need2warmup.push(warmurl)
            }
        }

        //开始预热
        let ret = await PushUrlsCache(need2warmup, nowQuota)
        for (const warmbatch of ret) {
            let taskId = warmbatch.pushUrlsCacheResponse.TaskId
            let urls = warmbatch.urls
            if (taskId != null) {
                logData.warmUpTaskIds?.push(taskId)
            }

            for (const url of urls) {
                let key = url.substring(inputParms.baseUrl.length + 1);
                let item: Partial<CosCDNWarmItem>
                if (warmUpFileData.has(key) === false) {
                    item = {}
                    warmUpFileData.set(key, item)
                } else {
                    item = warmUpFileData.get(key)!;
                }
                item.key = key
                item.fullURL = url
                item.pushTask = {
                    TaskId: taskId,
                    Url: url,
                    Status: 'process',
                }
            }
        }

        //这里统计数据
        for (const item of warmUpFileData.values()) {
            let fail = item.pushTask!.Status === "fail";
            if (fail)
                pushFailCnt++;
            let success = item.pushTask!.Status === "done";
            if (success)
                pushSuccessCnt++;
            let invalid = item.pushTask!.Status === "invalid";
            if (invalid)
                pushInvalidCnt++;
            let process = item.pushTask!.Status === "process";
            if (process)
                pushProcessCnt++;
        }
        //记录数据
        logData.warmupFailCnt = pushFailCnt;
        logData.warmupProcessingCnt = pushProcessCnt;
        logData.warmUpSuccessCnt = pushSuccessCnt;
        logData.warmUpInvalidCnt = pushInvalidCnt;
        logData.notWarmupCnt = logData.warmUpTotalCnt - pushFailCnt - pushProcessCnt - pushSuccessCnt - pushInvalidCnt;
        //存储预热的数据
        logData.cosCDNWarmItems = Array.from(warmUpFileData.values())
        //再次查询余量
        pushQuota = await checkWarmupQuota();
        logData.nowPushQuota = pushQuota;
        //过滤taskids，去除重复的taskid
        logData.warmUpTaskIds = Array.from(new Set<string>(logData.warmUpTaskIds));

        await saveLogFile(logData, logpath)
    } catch (e) {
        logWithKey('buildCosCDNWarmup', `exception:${e}`)
    } finally {
        logFunctionEnd('buildCosCDNWarmup')
    }
}

/**
 * 资源预热 预热 URL
 * 每天1000次
 * 每次调用最大500条
 * https://cloud.tencent.com/document/product/228/37869
 * @constructor
 */
async function PushUrlsCache(urls: string[], quota: Quota): Promise<{
    urls: string[],
    pushUrlsCacheResponse: PushUrlsCacheResponse
}[]> {
    logFunctionStart('PushUrlsCache')
    const cdn = new Client({credential: {secretId: SecretId, secretKey: SecretKey}});
    if (urls.length > quota.Available) {
        logWithKey('PushUrlsCache', `urls cnt:${urls.length}> ${quota.Available},so only warm ${quota.Available} in ${urls.length}`);
    }
    urls = urls.slice(0, Math.min(urls.length, quota.Available));

    let allResults: { urls: string[], pushUrlsCacheResponse: PushUrlsCacheResponse }[] = []; // 存储所有批次的结果

    for (let i = 0; i < urls.length; i += quota.Batch) {
        // 切分当前批次的 URL
        const toWarmUrls = urls.slice(i, i + quota.Batch);
        // 调用预热接口
        let ret: PushUrlsCacheResponse = await cdn.PushUrlsCache({Urls: toWarmUrls});
        // 日志记录
        logWithKey('PushUrlsCache', `Batch ${Math.floor(i / quota.Batch) + 1}: ${JSON.stringify(toWarmUrls)} => Response: ${JSON.stringify(ret)}`);
        // 将当前批次结果保存
        allResults.push({
            urls: toWarmUrls,
            pushUrlsCacheResponse: ret
        });
        await delay(1000); // 延迟1秒
    }
    logFunctionEnd('PushUrlsCache');
    return allResults;
}

/**
 * 查询预热的用量
 * DescribePushQuota 用于查询预热配额和每日可用量。
 * @param urls
 * @constructor
 */
async function checkWarmupQuota(): Promise<DescribePushQuotaResponse> {
    logFunctionStart('CheckWarmupQuota')
    const cdn = new Client({credential: {secretId: SecretId, secretKey: SecretKey}});
    //用量查询
    const ret = await cdn.DescribePushQuota();
    logWithKey('CheckWarmupQuota', `${JSON.stringify(ret)}`);
    // 默认接口请求频率限制：20次/秒。
    logFunctionEnd('CheckWarmupQuota')
    return ret;
}

/**
 * 预热历史查询
 * https://cloud.tencent.com/document/product/228/37872
 * 默认接口请求频率限制：20次/秒。
 * {TaskId: '560446800863995581'}
 * @param urls
 * @constructor
 */
async function checkWarmupHistory(taskIds: Array<string>): Promise<DescribePushTasksResponse[]> {
    logFunctionStart('CheckWarmupHistory')
    const cdn = new Client({credential: {secretId: SecretId, secretKey: SecretKey}});
    let counter = 0;
    let pushTasksResponses: DescribePushTasksResponse[] = []
    if (taskIds.length > 0) {
        for (const taskId of taskIds) {
            //用量查询
            const ret = await cdn.DescribePushTasks({TaskId: taskId, Limit: 10000});
            pushTasksResponses.push(ret)
            logWithKey('CheckWarmupHistory', `${JSON.stringify(ret)}`);
            counter++;
            // 默认接口请求频率限制：20次/秒。
            if (counter % 20 === 0) {
                console.log('Reached 20 tasks, waiting for 1 second...');
                await delay(1500); // 延迟1秒
            }
        }
    } else {
        //已时间来作为查询条件
        const now = new Date();
        const startTime = format(subDays(now, 7), 'yyyy-MM-dd HH:mm:ss');
        const nowTime = format(now, 'yyyy-MM-dd HH:mm:ss');

        logWithKey('CheckWarmupHistory', `startTime:${startTime},nowTime:${nowTime}`);
        const ret = await cdn.DescribePushTasks({StartTime: startTime, EndTime: nowTime, Limit: 10000});
        pushTasksResponses.push(ret)
        logWithKey('CheckWarmupHistory', `${JSON.stringify(ret)}`);
    }
    logFunctionEnd('CheckWarmupHistory')
    return pushTasksResponses;
}


logFunctionStart('warm_up_cos.ts');

const program = new Command();
program
    .option('--platform <type>', '平台')
    .option('--env <type>', '开发阶段环境变量')
    .option('--baseUrl <type>', '域名')
    .option('--cdnRootDirectory <type>', 'cdnroot')
    .option('--cfgVersion <type>', '配置版本号')
    .option('--resVersion <type>', '资源版本号')
    .option('--area <type>', '大陆或者海外')
console.log(process.argv);
program.parse(process.argv);
const options = program.opts();
let platform = options.platform;
let env = options.env;
let baseUrl = options.baseUrl;
let cdnRootDirectory = options.cdnRootDirectory;
let cfgVersion = options.cfgVersion;
let resVersion = options.resVersion;
let area = options.area;
const inputParam: InputParms = {
    platform: platform,
    env: env,
    baseUrl: baseUrl,
    cdnRootDirectory: cdnRootDirectory,
    gitResDirectory: '',
    cfgVersion: cfgVersion,
    resVersion: resVersion,
    area: area,
}
// const inputParam: InputParms = {
//     platform: PLATFORM.WeixinMiniGame,
//     env: 'weixinminigame_lishengjie_optimize-config-v2',
//     baseUrl: 'https://xiuxian-cdn.elex-tech.net',
//     cdnRootDirectory:'F:/cdn',
//     gitResDirectory:'',
//     cfgVersion:'2.0.1241040',
//     resVersion:'2.0.1241040',
//     area:'mainland',
// }
logWithKey('warm_up_cos.ts', `inputParam:${JSON.stringify(inputParam)}`);
buildCosCDNWarmup(inputParam).then(() => {
    logFunctionEnd('warm_up_cos.ts');
});

