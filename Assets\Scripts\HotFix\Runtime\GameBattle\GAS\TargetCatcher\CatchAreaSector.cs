using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Game.Battle;
namespace GAS.Runtime
{
    public class CatchAreaSector : CatchArea
    {
        public float radius = 3.5f;          // 半径
        public ValueOrTableIdSelector radiusSelector;  // 半径选择器
        public float angle = 120f;           // 扇形角度
        public float offsetAngle = 0f;       // 偏转角度 
        public OffsetType offsetType;        //偏移类型
        public Vector2 centerOffset;         // 中心点偏移
        private static readonly Collider[] colliders = new Collider[32];

        protected override void CatchTargetsNonAlloc(AbilitySystemComponent mainTarget, List<AbilitySystemComponent> results)
        {
        
            InitializeCache();
            
            try
            {
                radius = GetRadius(this.Owner);
                Vector3 pos = (offsetType == OffsetType.Self?this.Owner.transform.position:mainTarget.transform.position)+new Vector3(0,radius/2,0);
                pos += (offsetType == OffsetType.Self ? this.Owner.transform.rotation : mainTarget.transform.rotation)*new Vector3(centerOffset.x,0,centerOffset.y);
                Vector3 dir = offsetType == OffsetType.Self?this.Owner.transform.forward:mainTarget.transform.forward;

                UnicornCharacter character = this.Owner.GetComponent<UnicornCharacter>();
                int count = Physics.OverlapSphereNonAlloc(pos, radius, colliders);
                //Debug.Log("count:"+count);
                for (int i = 0; i < count; i++)
                {
                    Collider c = colliders[i];
                    AbilitySystemComponent ac = c.GetComponent<AbilitySystemComponent>();
                    if (ac != null && IsValidTarget(ac) && IsInFanShape(ac.transform.position))
                    {
                        results.Add(ac);
                    }
                }

                AddSelfIfNeeded(results);
                PostProcessResults(results);
               
#if UNITY_EDITOR
                OnEditorPreview(this.Owner.gameObject);
#endif
            }
            finally
            {
              
                ClearCache();
            }
        }

        private bool IsInFanShape(Vector3 targetPosition)
        {
            // 1. 计算实际的中心点（考虑偏移）
            Transform ownerTransform = this.Owner.transform;
            Vector3 center = ownerTransform.position + ownerTransform.rotation * new Vector3(centerOffset.x, 0, centerOffset.y);

            // 2. 计算到目标的方向向量
            Vector3 directionToTarget = targetPosition - center;
            float distanceToTarget = directionToTarget.magnitude;

            // 3. 检查距离
            //if (distanceToTarget > radius) return false;

            // 4. 计算前向基准方向（考虑偏转角度）
            Vector3 baseForward = ownerTransform.rotation * Quaternion.Euler(0, offsetAngle, 0) * Vector3.forward;

            // 5. 计算目标方向与基准方向的夹角
            float angleToTarget = Vector3.SignedAngle(baseForward, directionToTarget, Vector3.up);
            
            // 6. 将角度转换到 [-180, 180] 范围
            angleToTarget = angleToTarget < -180 ? angleToTarget + 360 : angleToTarget;
            angleToTarget = angleToTarget > 180 ? angleToTarget - 360 : angleToTarget;

            // 7. 检查是否在扇形角度范围内
            return Mathf.Abs(angleToTarget) <= angle / 2f;
        }
        float GetRadius(AbilitySystemComponent mainTarget)
        {
            if (radiusSelector.dataSourceType == DataSourceType.Value && radiusSelector.fixedValue == 0)
            {
                return radius;
            }else
            {
                return radiusSelector.GetFloatValue(mainTarget);
            }
        }
#if UNITY_EDITOR
        public override void OnEditorPreview(GameObject obj)
        {
            base.OnEditorPreview(obj);
            // 考虑物体的当前旋转
            float objectYRotation = obj.transform.eulerAngles.y;
            Vector3 center = obj.transform.position + obj.transform.rotation * new Vector3(centerOffset.x,0,centerOffset.y);

            float startAngle = -angle / 2f + offsetAngle + objectYRotation;
            float endAngle = angle / 2f + offsetAngle + objectYRotation;
            int segments = 36;

            Vector3 previousPoint = center + Quaternion.Euler(0, startAngle, 0) * Vector3.forward * radius;

            for (int i = 0; i <= segments; i++)
            {
                float currentAngle = Mathf.Lerp(startAngle, endAngle, (float)i / segments);
                Vector3 currentPoint = center + Quaternion.Euler(0, currentAngle, 0) * Vector3.forward * radius;

                Debug.DrawLine(center, currentPoint, Color.yellow, 2);
                if (i > 0) Debug.DrawLine(previousPoint, currentPoint, Color.yellow, 2);

                previousPoint = currentPoint;
            }
        }
#endif
    }
}
