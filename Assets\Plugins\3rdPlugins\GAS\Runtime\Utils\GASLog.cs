﻿using System.Diagnostics;

namespace GAS.Runtime
{
    public abstract class GASLog
    {
        /// <summary>
        /// 调试日志，正式版上线后不编译
        /// </summary>
        /// <param name="message"></param>
        static string _tag = $"<color=#0BFFC5>[GAS]</color>";
        public static bool IsLog = false;

        [Conditional("ENABLE_LOG")]
        public static void Info(string msg)
        {
            if (!IsLog) return;
            var showMsg = _tag + msg;
            UnityEngine.Debug.Log(showMsg);
        }

        [Conditional("ENABLE_LOG")]
        public static void Warning(string msg)
        {
            if (!IsLog) return;
            var showMsg = _tag + msg;
            UnityEngine.Debug.LogWarning(showMsg);
        }

        [Conditional("ENABLE_LOG")]
        public static void Error(string msg)
        {
            if (!IsLog) return;
            var showMsg = _tag + msg;
            UnityEngine.Debug.LogError(showMsg);
        }
    }
}