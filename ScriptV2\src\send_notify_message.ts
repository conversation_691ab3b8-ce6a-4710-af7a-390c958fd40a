import {$} from 'zx';
import fs from 'fs-extra';
import * as path from 'path';
import {sendEnhancedCard} from "./feishu_webhook.ts";
import {BuildParam} from "./build_params.ts";
import {BUILD_TYPE, JENKIN_BUILD_TIMER, JENKINS_TO_FEISHU_USER_MAPPING, PLATFORM} from "./const.ts";
import {isValidStr, logFunctionEnd, logFunctionStart} from "./utility.ts";
import QRCode from "qrcode";
import {format} from "date-fns";
import {Timer} from "./timer.ts";

export {sendNotifyMessage, sendErrorMessage}

/**
 * 获取打包的通知消息
 */
async function getBasicMessage(buildParam: BuildParam,success:boolean):Promise<string[]> {
    // 输出所有的环境变量内容
    // console.log('所有环境变量:', JSON.stringify(process.env, null, 2));

    const buildUrl = process.env.BUILD_URL?.replace("127.0.0.1", buildParam.getState().localMachineAddress)
        .replace("localhost", buildParam.getState().localMachineAddress);

    // 获取构建用户名
    const buildUser = buildParam.getParams().buildUser || '';

    // 查找对应的飞书用户ID
    const feishuUserId = JENKINS_TO_FEISHU_USER_MAPPING[buildUser];

    // 构建用户显示和@标记
    let userDisplay = buildUser;
    let atUserContent =` <at email=${process.env.BUILD_USER_EMAIL}></at>`;
    let contents = <string[]>[];
    contents.push('### 基本信息');
    contents.push(`
- **构建No**: ${process.env.BUILD_NUMBER}
- **构建User**: ${userDisplay}
- **构建平台**: ${buildParam.getParams().platform}
- **构建环境**: ${buildParam.getState().envName}
- **构建分支**: ${buildParam.getParams().branchName} 
- **构建类型**: ${buildParam.getParams().buildType}
- **内网/外网**: ${buildParam.getParams().netType === 0 ? '内网' : '外网'}
- **Debug包**: ${buildParam.getParams().debug ? '是' : '否'}
- **C#脚本调试**: ${buildParam.getParams().scriptDebug ? '是' : '否'}
- **App版本号**: ${(buildParam.getParams().buildType === BUILD_TYPE.BUILD_ALL || buildParam.getParams().buildType === BUILD_TYPE.BUILD_APP) ? buildParam.getState().appVersion : '-'}
- **配置版本号**: ${(buildParam.getParams().buildType === BUILD_TYPE.BUILD_ALL || buildParam.getParams().buildType === BUILD_TYPE.BUILD_CONFIG) ? buildParam.getState().cfgVersion : '-'}
- **Res版本号**: ${(buildParam.getParams().buildType === BUILD_TYPE.BUILD_ALL || buildParam.getParams().buildType === BUILD_TYPE.BUILD_RES) ? buildParam.getState().resVersion : '-'}
- **Proto版本号**: ${(buildParam.getParams().buildType === BUILD_TYPE.BUILD_ALL || buildParam.getParams().buildType === BUILD_TYPE.BUILD_RES) ? buildParam.getState().protoVersion : '-'}
- **自动更新总控**: ${buildParam.getParams().autoUpdateRemoteVersion}
- **当前时间**: ${format(new Date(), 'yyyy年MM月dd日HH时mm分')}
- **构建耗时**：${Timer.formatDuration(Timer.end(JENKIN_BUILD_TIMER))}
`);
    if(success && buildParam.getState().platform === PLATFORM.WeixinMiniGame){
        contents.push(`- **自动上传到微信后台**：${buildParam.getParams().uploadMiniGameToOfficial ? '是' : '否'}`);
    };

contents.push('### 重要链接🔗');
    //链接部分
    contents.push( `
- [构建日志链接](${buildUrl}consoleText)
- [构建参数链接](${buildUrl}parameters)`);

    if(success && buildParam.getParams().buildType === BUILD_TYPE.BUILD_ALL || buildParam.getParams().buildType === BUILD_TYPE.BUILD_APP){

                let version = buildParam.getState().appVersion;
                let buildInfo = await buildParam.getContext().gitCdnManager?.getBuildPlayerDownloadInfo(version);
                if(buildInfo !== undefined) {
                    for (let file of buildInfo!) {
                        contents.push(`- [[${file.fileName}] [${file.size}] 点击下载](${file.url})`);
                    }
                }
            };
    // 在消息最后添加@用户
    contents.push(`### 关注人`);
    contents.push(`- ${atUserContent}请关注此次构建`);
    return contents;
}

/**
 * 发送飞书构建消息
 * @param buildParam
 */
async function sendNotifyMessage(buildParam: BuildParam) {
    logFunctionStart("sendNotifyMessage")
    const params = buildParam.getParams();
    const state = buildParam.getState();
    let contents = await getBasicMessage(buildParam,true)
    if (state.platform === PLATFORM.WeixinMiniGame) {
        if (params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_APP) {
            let weixinOutputDirectory = `${buildParam.getBuildPlayerFullPath()}`
            weixinOutputDirectory = path.resolve(weixinOutputDirectory)
            if (params.uploadMiniGameToOfficial === true) {
                var desc = state.appVersion
                var temp = await $.sync`sh ./shell/upload_minigame.sh ${weixinOutputDirectory} ${state.appVersion} ${desc}`
                console.log(temp.stdout);
            }

            let infoJsonPath = `${weixinOutputDirectory}_info.json`
            infoJsonPath = path.resolve(infoJsonPath);
            let qrImagePath = `${weixinOutputDirectory}_qr.png`
            qrImagePath = path.resolve(qrImagePath);
            if (fs.existsSync(infoJsonPath)) {
                fs.removeSync(infoJsonPath);
            }
            if (fs.existsSync(qrImagePath)) {
                fs.removeSync(qrImagePath);
            }
            var dd = await $.sync`sh ./shell/send_minigame_preview.sh ${weixinOutputDirectory} ${qrImagePath} ${infoJsonPath}`
            console.log(dd.stdout);
            if (!fs.existsSync(qrImagePath)) {
                const message = `微信小游戏生成preview二维码失败`
                throw new Error(message)
            } else {
                console.log(`微信小游戏构建成功`)
                await sendEnhancedCard({
                    title: "微信小游戏构建成功",
                    contents: contents,
                    imagePath: qrImagePath,
                    template: "green"
                });
            }
        }
    } else {
        if (params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_APP) {
            let version = buildParam.getState().appVersion;
            let buildInfo = await buildParam.getContext().gitCdnManager?.getBuildPlayerDownloadInfo(version);
            let url = ''
            if(buildInfo !== undefined) {
                for (let file of buildInfo!) {
                    if(file.fileName.endsWith('apk')){
                        url = file.url
                        break
                    }
                }
            }

            let imagePath = ''
            if (isValidStr(url)) {
                const qrpath = path.join(process.cwd(), 'url_qr.png')
                if (fs.existsSync(qrpath)) {
                    fs.removeSync(qrpath);
                }
                await QRCode.toFile(qrpath, url, {type: "png"});
                console.log(`QR Code saved to ${qrpath}`);
                if (fs.existsSync(qrpath)) {
                    imagePath = qrpath;
                }
            }

            if (isValidStr(imagePath)) {
                await sendEnhancedCard({
                    title: "构建成功",
                    contents: contents,
                    imagePath: imagePath,
                    template: "green"
                });
                return;
            }
        }
        //发送其他的消息
        await sendEnhancedCard({
            title: "构建成功",
            contents: contents,
            template: "green"
        });
    }
    logFunctionEnd("sendNotifyMessage")
}

/**
 * 发送消息
 */
async function sendErrorMessage(buildParam: BuildParam, title: string, msg: string) {
    const contents = await getBasicMessage(buildParam,false);
    // 将多行 msg 内容动态格式化为 Markdown 引用格式
    const formattedMsg = msg
        .split("\n") // 按行分隔
        .map(line => `> ${line}`) // 在每行前加上 "> "
        .join("\n"); // 重新合并为多行字符串
    contents.push('### 错误信息')
    contents.push(`${formattedMsg}`)

    await sendEnhancedCard({
        title: title,
        contents: contents,
        template: `red`
    });
}

