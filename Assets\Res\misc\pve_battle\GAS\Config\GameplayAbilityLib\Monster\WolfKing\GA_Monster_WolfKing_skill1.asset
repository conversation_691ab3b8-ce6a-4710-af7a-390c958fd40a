%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d763af469c524557946c477b9bea3a46, type: 3}
  m_Name: GA_Monster_WolfKing_skill1
  m_EditorClassIdentifier: 
  Description: 
  UniqueName: GA_Monster_WolfKing_Skill1
  Cost: {fileID: 0}
  Cooldown: {fileID: 11400000, guid: f2a34249ac93f374487b30663f3b9380, type: 2}
  CooldownTime: 0
  DoCostImmediately: 1
  AssetTags:
  - _name: Ability.Skill_1
    _hashCode: 410277607
    _shortName: Skill_1
    _ancestorHashCodes: b84ed1e0
    _ancestorNames:
    - Ability
  CancelAbilityTags: []
  BlockAbilityTags: []
  ActivationOwnedTags:
  - _name: Ability.Skill_1.running
    _hashCode: 955764930
    _shortName: running
    _ancestorHashCodes: b84ed1e0e7567418
    _ancestorNames:
    - Ability
    - Ability.Skill_1
  ActivationRequiredTags: []
  ActivationBlockedTags:
  - _name: Ability.Skill_1.CD
    _hashCode: 145654248
    _shortName: CD
    _ancestorHashCodes: b84ed1e0e7567418
    _ancestorNames:
    - Ability
    - Ability.Skill_1
  - _name: Ability.Skill_1.running
    _hashCode: 955764930
    _shortName: running
    _ancestorHashCodes: b84ed1e0e7567418
    _ancestorNames:
    - Ability
    - Ability.Skill_1
  priority: 0
  Show: 0
  skillType: 0
  attackDistance: 0
  originalSpeedPlayback: 1
  attributeName: AS_Fight.AttackSpeed
  manualEndAbility: 0
  FrameCount: 40
  DurationalCues: []
  InstantCues: []
  ReleaseGameplayEffect:
  - trackName: 
    markEvents:
    - startFrame: 27
      jsonTargetCatcher:
        Type: GAS.Runtime.CatchAreaSector
        Data: 
      gameplayEffectAssets:
      - {fileID: 11400000, guid: 0bd214783b7bd094fac99374a8af0ca3, type: 2}
  BuffGameplayEffects: []
  InstantTasks: []
  OngoingTasks: []
  AnimationTracks:
  - trackName: 
    clipEvents:
    - startFrame: 0
      durationFrame: 40
      animationClip: {fileID: 1827226128182048838, guid: cfd8a74ecae7b60438075fd659eff940,
        type: 3}
  SoundEffectTracks:
  - trackName: 
    clipEvents:
    - startFrame: 0
      durationFrame: 40
      AudioWwiseName: Play_sfx_monster_wolfking_skill1
      IsStopOnEnd: 1
      isAttachToOwner: 1
  FxTracks:
  - trackName: 
    clipEvents:
    - startFrame: 6
      durationFrame: 34
      fxPrefab: {fileID: 2083282497805244620, guid: bb77b134e6e45614999faa91caec54b8,
        type: 3}
      isAttachToTarget: 1
      attachedPath: dummy1
      offset: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
      scale: {x: 1, y: 1, z: 1}
      activeWhenAdded: 1
      disableWhenClipEnd: 1
  - trackName: 
    clipEvents:
    - startFrame: 8
      durationFrame: 32
      fxPrefab: {fileID: 7034168182972672556, guid: 3c38cdad9bd65fd469b33d0af4ddb98b,
        type: 3}
      isAttachToTarget: 1
      attachedPath: dummy1
      offset: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
      scale: {x: 1, y: 1, z: 1}
      activeWhenAdded: 1
      disableWhenClipEnd: 0
  - trackName: 
    clipEvents:
    - startFrame: 27
      durationFrame: 13
      fxPrefab: {fileID: 1795893173959374175, guid: ed90a8f678e83454f9b3a3a192fe9e31,
        type: 3}
      isAttachToTarget: 1
      attachedPath: dummy2
      offset: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
      scale: {x: 1, y: 1, z: 1}
      activeWhenAdded: 1
      disableWhenClipEnd: 0
