//此代码自动生成，请勿手动修改
using UnityEngine;
using Game.Common;
using UnityEngine.UI;
using TMPro;

namespace Game.View
{
    public partial class battle_pve_fail_info
    {
        #region UI Initialization
        
        protected UIIDs ui;
        protected UIObjs uiObjs;
        /// <summary>
        /// 自动生成的UI初始化方法，请勿手动修改
        /// </summary>
        protected override void __AutoInitUI()
        {
            ui = new UIIDs();
            uiObjs = new UIObjs();
            
            // 此方法从容器键初始化所有UI组件
            ui.backImgBtn = GetCompFromContainerKey("backImgBtn", UIControlType.UIButtonEx);
            uiObjs.backImgBtn = GetUnityCompFromContainerByKey<UIButtonEx>("backImgBtn", UIControlType.UIButtonEx);
            ui.selfRootObj = GetCompFromContainerKey("selfRootObj", UIControlType.UI_RectTransform);
            uiObjs.selfRootObj = GetUnityCompFromContainerByKey<RectTransform>("selfRootObj", UIControlType.UI_RectTransform);
            ui.content = GetCompFromContainerKey("content", UIControlType.OSA_List);
        }
        
        /// <summary>
        /// UI对象引用类，包含所有需要直接访问的UI组件
        /// </summary>
        protected class UIObjs
        {
            public UIButtonEx backImgBtn;
            public RectTransform selfRootObj;
        }
        
        /// <summary>
        /// UI ID类，包含所有UI元素的ID
        /// </summary>
        protected class UIIDs
        {
            public int backImgBtn;
            public int selfRootObj;
            public int content;
        }
        #endregion
    }
}
