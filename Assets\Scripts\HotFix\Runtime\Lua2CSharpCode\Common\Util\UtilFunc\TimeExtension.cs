using System;

using Game.Common.Logic.Event;

namespace Game.Common
{
    public static class Time
{
    #region Unity Time Properties
    private static float _deltaTime = 0;
    private static float _fixedDeltaTime = 0;
    private static float _fixedTime = 0;
    private static int _frameCount = 1;
    private static float _realtimeSinceStartup = 0;
    private static float _time = 0;
    private static float _timeScale = 1;
    private static float _timeSinceLevelLoad = 0;
    private static float _unscaledDeltaTime = 0;

    public static float deltaTime
    {
        get { return _deltaTime; }
        private set { _deltaTime = value; }
    }

    public static float fixedDeltaTime
    {
        get { return _fixedDeltaTime; }
        set { _fixedDeltaTime = value; }
    }

    public static float maximumDeltaTime
    {
        get { return UnityEngine.Time.maximumDeltaTime; }
        set { UnityEngine.Time.maximumDeltaTime = value; }
    }

    public static float fixedTime
    {
        get { return _fixedTime; }
        private set { _fixedTime = value; }
    }

    public static int frameCount
    {
        get { return _frameCount; }
        private set { _frameCount = value; }
    }

    public static float realtimeSinceStartup
    {
        get { return _realtimeSinceStartup; }
        private set { _realtimeSinceStartup = value; }
    }

    public static float time
    {
        get { return _time; }
        private set { _time = value; }
    }

    public static float timeScale
    {
        get { return UnityEngine.Time.timeScale; }
        set { UnityEngine.Time.timeScale = value; }
    }

    public static float timeSinceLevelLoad
    {
        get { return _timeSinceLevelLoad; }
        private set { _timeSinceLevelLoad = value; }
    }

    public static float unscaledDeltaTime
    {
        get { return _unscaledDeltaTime; }
        private set { _unscaledDeltaTime = value; }
    }

    public static float unscaledTime
    {
        get { return UnityEngine.Time.unscaledTime; }
    }
    #endregion

    #region Private Fields
    private static long serverTimeStamp;
    private static float markServerTime;
    private static int dayEndTime;
    private static bool isUsingUTC = true;
    private static readonly string UTCDes = "UTC ";
    private static string timePrefix = "UTC ";
    private static int? timeZoneIndex;
    #endregion

    public static void SetDeltaTime(float deltaTime, float unscaledDeltaTime)
    {
        _deltaTime = deltaTime;
        _unscaledDeltaTime = unscaledDeltaTime;
        _time += deltaTime;
        _realtimeSinceStartup += unscaledDeltaTime;
        _timeSinceLevelLoad += deltaTime;
    }

    public static void SetFixedDelta(float fixedDeltaTime)
    {
        _fixedDeltaTime = fixedDeltaTime;
        _fixedTime += fixedDeltaTime;
    }

    public static void SetFrameCount()
    {
        _frameCount++;
    }

    public static float SetTimeScale(float scale)
    {
        float last = _timeScale;
        _timeScale = scale;
        UnityEngine.Time.timeScale = scale;
        return last;
    }

    public static void SetServerTimeStamp(long serverTimeStamp)
    {
        Time.serverTimeStamp = serverTimeStamp;
        markServerTime = _realtimeSinceStartup * 1000;
    }

    public static long GetServerTimeMS()
    {
        var deltaTime = _realtimeSinceStartup * 1000 - markServerTime;
        return serverTimeStamp + (long)deltaTime;
    }

    public static long GetServerTimeS()
    {
        return (long)(GetServerTimeMS() * 0.001f);
    }

    public static long GetTimestamp()
    {
        return GetServerTimeS();
    }

    public static long GetZeroTimeStampByOffset(long timeStampMs, long offsetMs)
    {
        return timeStampMs - (timeStampMs + offsetMs) % 86400000;
    }

    public static int GetTimeZoneIndex()
    {
        if (!timeZoneIndex.HasValue)
        {
            timeZoneIndex = (int)TimeZoneInfo.Local.BaseUtcOffset.TotalSeconds;
        }
        return timeZoneIndex.Value;
    }

    public static void IsUsingUTC(bool isUsing)
    {
        isUsingUTC = isUsing;
        timePrefix = isUsingUTC ? UTCDes : "";
    }

    public static string GetUTCDes()
    {
        return timePrefix;
    }

    public static void CheckTimes()
    {
        if (dayEndTime == 0) return;

        long curTimeStamp = GetServerTimeS();
        if (curTimeStamp > dayEndTime)
        {
            dayEndTime += 24 * 60 * 60;
            Event.Instance.BroadcastDelay(EventDefine.TIME_ENTER_NEXT_DAY, 1);
        }
    }
        /// <summary>
        /// 获取时间差字符串
        /// </summary>
        /// <param name="ms">时间戳（毫秒）</param>
        /// <returns>时间差字符串</returns>
        public static string GetTimeAgoStr(long ms)
        {
            ms = ms == 0 ? 0 : ms;
            long now = GetServerTimeMS(); // 获取当前服务器时间（毫秒）
            long diff = Math.Max(0, (now - ms) / 1000); // 计算时间差（秒）

            if (diff < 60)
            {
                return "1m"; // 1分钟内
                // return "刚刚"; // 可以替换为本地化字符串
            }
            else if (diff < 3600)
            {
                return (diff / 60) + "分钟"; // 小于1小时
                // return $"{diff / 60}分钟前"; // 可以替换为本地化字符串
            }
            else if (diff < 86400)
            {
                return (diff / 3600) + "小时"; // 小于1天
                // return $"{diff / 3600}小时前"; // 可以替换为本地化字符串
            }
            else
            {
                return (diff / 86400) + "天"; // 大于1天
                // return $"{diff / 86400}天前"; // 可以替换为本地化字符串
            }
        }

        public static string TimeGetCurrentMonth()
    {
        return isUsingUTC ? 
            DateTime.UtcNow.ToString("yyyy/MM") : 
            DateTime.Now.ToString("yyyy/MM");
    }

    public static string TimeFormat_Ymd_Two(long startStamp, long endStamp)
    {
        var startTime = DateTimeOffset.FromUnixTimeMilliseconds(startStamp).DateTime;
        var endTime = DateTimeOffset.FromUnixTimeMilliseconds(endStamp).DateTime;
        
        string startStr = isUsingUTC ? startTime.ToUniversalTime().ToString("yyyy/MM/dd") : startTime.ToString("yyyy/MM/dd");
        string endStr = isUsingUTC ? endTime.ToUniversalTime().ToString("yyyy/MM/dd") : endTime.ToString("yyyy/MM/dd");
        
        return $"{timePrefix}{startStr} - {endStr}";
    }

    public static string TimeFormat_md_HM(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("MM-dd HH:mm") : dto.LocalDateTime.ToString("MM-dd HH:mm");
    }

    public static string TimeFormat_md_HMS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("MM-dd HH:mm:ss") : dto.LocalDateTime.ToString("MM-dd HH:mm:ss");
    }

    public static string TimeFormat_Ymd_A(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy/MM/dd dddd") : dto.LocalDateTime.ToString("yyyy/MM/dd dddd");
    }

    public static string TimeFormat_Ymd(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy/MM/dd") : dto.LocalDateTime.ToString("yyyy/MM/dd");
    }

    public static string TimeFormat_Ymd_HM(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy/MM/dd HH:mm") : dto.LocalDateTime.ToString("yyyy/MM/dd HH:mm");
    }

    public static string TimeFormat_Ymd_HM2(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy-MM-dd HH:mm") : dto.LocalDateTime.ToString("yyyy-MM-dd HH:mm");
    }

    public static string TimeFormat_Ymd_HMS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy/MM/dd HH:mm:ss") : dto.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss");
    }

    public static string TimeFormat_Ymd_HMS2(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy-MM-dd HH:mm:ss") : dto.LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
    }

    public static string TimeFormat_Ymd_HS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("yyyy-MM-dd HH:mm:ss") : dto.LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
    }

    public static string TimeFormat_md(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("MM/dd") : dto.LocalDateTime.ToString("MM/dd");
    }

        public static string TimeFormat_md_Two(long startStamp,long endStamp)
        {
            var startDto = DateTimeOffset.FromUnixTimeMilliseconds(startStamp);
            var endDto = DateTimeOffset.FromUnixTimeMilliseconds(endStamp);
            string startStr = isUsingUTC ? startDto.UtcDateTime.ToString("MM/dd") : startDto.LocalDateTime.ToString("MM/dd");
            string endStr = isUsingUTC ? endDto.UtcDateTime.ToString("MM/dd") : endDto.LocalDateTime.ToString("MM/dd");
            return $"{timePrefix}{startStr}-{endStr}";
        }

        public static string TimeFormat_md_HS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("MM/dd HH:mm") : dto.LocalDateTime.ToString("MM/dd HH:mm");
    }

    public static string TimeFormat_HS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("HH:mm") : dto.LocalDateTime.ToString("HH:mm");
    }

    public static string TimeFormat_HMS(long timeStamp)
    {
        var dto = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);
        return isUsingUTC ? dto.UtcDateTime.ToString("HH:mm:ss") : dto.LocalDateTime.ToString("HH:mm:ss");
    }

    public static DateTime GetOSDate(long timeStamp)
    {
        return isUsingUTC ? 
            DateTimeOffset.FromUnixTimeMilliseconds(timeStamp).UtcDateTime : 
            DateTimeOffset.FromUnixTimeMilliseconds(timeStamp).DateTime;
    }

    public static long GetTimeStampForData(DateTime data)
    {
        return ((DateTimeOffset)data).ToUnixTimeMilliseconds();
    }

    public static void SetDayEndTimeMS(long dayEndTime)
    {
        Time.dayEndTime = (int)(dayEndTime * 0.001f);
    }

    public static long GetTodayZeroTimeStampMS()
    {
        return GetTodayZeroTimeStampS() * 1000;
    }

    public static long GetTodayZeroTimeStampS()
    {
        CheckTimes();
        return dayEndTime;
    }

    public static long GetLeftCustomTime(int hour)
    {
        long leftTime = GetTodayZeroTimeStampS() - GetServerTimeS() + hour * 3600;
        if (leftTime > 24 * 3600)
        {
            leftTime = leftTime - 24 * 3600;
        }
        return leftTime;
    }

    public static string GetZeroLeftTimeString()
    {
        float leftTime = GetTodayZeroTimeStampS() - GetServerTimeS();
        return GetLeftTimeString(leftTime);
    }

    public static string GetLeftTimeString(float timeStamp, bool onlyMS = false)
    {
        if (timeStamp < 0) return "";

        int days = (int)(timeStamp / 86400);
        int hours = (int)(timeStamp % 86400) / 3600;
        int minutes = (int)(timeStamp % 3600) / 60;
        int seconds = (int)(timeStamp % 60);

        if (onlyMS)
        {
            return $"{minutes:D2}:{seconds:D2}";
        }
        else if (days > 0)
        {
            return $"{days}d {hours:D2}:{minutes:D2}:{seconds:D2}";
        }
        else
        {
            return $"{hours:D2}:{minutes:D2}:{seconds:D2}";
        }
    }

    public static string GetBriefLeftTimeString(long timeStamp)
    {
        if (timeStamp < 0) return "";

        int hours = (int)(timeStamp % 86400) / 3600;
        int minutes = (int)(timeStamp % 3600) / 60;
        int seconds = (int)(timeStamp % 60);

        if (hours > 0)
            return $"{hours}h{minutes}m{seconds}s";
        else if (minutes > 0)
            return $"{minutes}m{seconds}s";
        else
            return $"{seconds}s";
    }

    public static string TimeFormat_MToH_D(int min)
    {
        bool isAboveZero = min > 0;
        if (!isAboveZero) min = -min;

        if (min > 1440)
        {
            int day = min / 1440;
            int hour = (min - 1440 * day) / 60;
            if (hour > 0)
                return string.Format(isAboveZero ? "{0}d{1}h" : "-{0}d{1}h", day, hour);
            else
                return string.Format(isAboveZero ? "{0}d" : "-{0}d", day);
        }

        if (min > 60)
        {
            int hour = min / 60;
            int minute = min % 60;
            return string.Format(isAboveZero ? "{0}h{1}m" : "-{0}h{1}m", hour, minute);
        }
        else
        {
            return string.Format(isAboveZero ? "{0}m" : "-{0}m", min);
        }
    }

    public static (int days, int hours, int minutes) TimeFormat_SToH_D(long timestamp)
    {
        const int secondsPerDay = 24 * 60 * 60;
        int days = (int)(timestamp / secondsPerDay);
        int hours = (int)((timestamp % secondsPerDay) / 3600);
        int minutes = (int)Math.Ceiling((timestamp % secondsPerDay) % 3600.0 / 60);
        
        return (days, hours, minutes);
    }

    public static (int hours, int minutes, int seconds) TimeFormat_SToH_M_S(long timeStamp)
    {
        int hours = (int)(timeStamp / 3600);
        int minutes = (int)(timeStamp % 3600) / 60;
        int seconds = (int)(timeStamp % 60);
        
        return (hours, minutes, seconds);
    }
}
}
