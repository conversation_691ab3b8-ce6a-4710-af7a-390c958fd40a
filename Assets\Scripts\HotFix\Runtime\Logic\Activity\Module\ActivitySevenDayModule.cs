

using System.Collections.Generic;
using System.Linq;
using ELEX.Config;
using faster.pb.impl;
using Game.CityEventSystem;
using Game.Common;
using Game.Common.Logic.Event;
using Game.Common.StateMachine;
using Game.Module;
using Game.View;
using LuaFramework;
using Event = Game.Common.Logic.Event.Event;

namespace Game.UnActivity
{
    public class ActivitySevenDayModule : GameModule<ActivitySevenDayModule>
    {
        public override void OnEnterGame()
        {
            base.OnEnterGame();
            Event.Instance.AddListener<ScUnActivityReward>(this, EventDefine.UI_ACTIVITY_GET_REWARD, GetReward);
            Event.Instance.AddListener<string>(this, EventDefine.UI_ACTIVITY_OPEN_UI, OpenPanel);
            Event.Instance.AddListener<int>(this, EventDefine.UI_ACTIVITY_ADD, AddPanel);
            Event.Instance.AddListener<int>(this, EventDefine.MissionAchieveMission, AddHudConfig);
        }
        
        public override void OnClearAll()
        {
            Event.Instance.RemoveListener(this,EventDefine.UI_ACTIVITY_GET_REWARD);
            Event.Instance.RemoveListener(this,EventDefine.UI_ACTIVITY_OPEN_UI);
            Event.Instance.RemoveListener(this,EventDefine.UI_ACTIVITY_ADD);
            Event.Instance.RemoveListener(this,EventDefine.MissionAchieveMission);
        }
        
        public const string ACTIVITY_TYPE = EActivityTypeString.SevenDaysLogin;
        
        private List<ActivitySevenDayData> _activityDataList = new List<ActivitySevenDayData>();

        private int _eventId = 20010045;
        
        public ActivityBaseData GetActivityBaseData()
        {
            return ActivityBaseModule.Instance.GetActivityByType(ACTIVITY_TYPE);
        }
        
        public void AddHudConfig(int eventId)
        {
            _eventId = GetActivityBaseData().Param3.SafeToInt();
            if (eventId == _eventId)
            {
                var baseData = GetActivityBaseData();
                ActivityBaseModule.Instance.AddHudConfig(baseData);
                Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_GET_ALL);
            }
        }
        
        public bool IsActivityOpen()
        {
            return CityEventData.Instance.IsEventAchieved(_eventId);
        }
        
        public List<ActivitySevenDayData> GetActivityDataList()
        {
            return _activityDataList;
        }
        
        #region net
        
        public void UpdateActivitySevenDayInfo(ScUnActivitySevenDay data)
        {
            if (ActivityBaseModule.Instance.GetActivity(data.activityId) != null)
            {
                if (_activityDataList.Count == 0)
                {
                    SetSevenDayInfoByConfig(data.activityId);
                }

                SetSevenDayInfoByInfo(data.rewardId, data.loginDay);
                
                Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_SEVEN_DAY_UPDATE);
            }
        }
        
        public void SetSevenDayInfoByConfig(int activityId)
        {
            _activityDataList.Clear();
            var confList = ConfHelper.GetConfUnicornActivityLoginList().list.FindAll(x => x.activityid == activityId);
            foreach (var conf in confList)
            {
                var data = new ActivitySevenDayData();
                data.InitWithSevenDayInfo(conf);
                _activityDataList.Add(data);
            }
        }

        public void SetSevenDayInfoByInfo(List<int> rewardId, int loginDay)
        {
            _activityDataList.Sort((x, y) => x.day - y.day);
            for(int i = 0; i < _activityDataList.Count; i++)
            {
                var data = _activityDataList[i];
                if (data.day <= loginDay)
                {
                    data.rewardState = EActivityRewardState.Claimable;
                }
                if (rewardId.Contains(data.id))
                {
                    data.rewardState = EActivityRewardState.Claimed;
                }
            }
        }

        public void GetReward(ScUnActivityReward reward)
        {
            if (reward.activityId == GetActivityBaseData().ActivityId)
            {
                if (reward.param1 == 0) // 全部领取
                {
                    foreach (var value in _activityDataList)
                    {
                        if (value.rewardState == EActivityRewardState.Claimable)
                        {
                            value.rewardState = EActivityRewardState.Claimed;
                        }
                    }
                    Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_SEVEN_DAY_REWARD_UPDATE);
                }
                else
                {
                    var conf = ConfHelper.GetConfUnicornActivityLoginList().list.Find(x => x.activityid == reward.activityId && x.id == reward.param1);
                    if (conf != null)
                    {
                        foreach (var value in _activityDataList)
                        {
                            if (value.day == conf.day)
                            {
                                value.rewardState = EActivityRewardState.Claimed;
                                Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_SEVEN_DAY_REWARD_UPDATE);
                                break;
                            }
                        }
                    }
                }
                
                // 更新红点
                var item = _activityDataList.Find(x => x.rewardState == EActivityRewardState.Claimable);
                GetActivityBaseData().Red = item != null;
                Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_SINGLE_UPDATE, GetActivityBaseData());
                    
                // 领完奖励关闭入口
                if (IsAllClaimed())
                {
                    ActivityBaseModule.Instance.RemoveActivity(GetActivityBaseData().ActivityId);
                    Event.Instance.Broadcast(EventDefine.UI_ACTIVITY_GET_ALL);
                }
            }
        }

        public bool IsAllClaimed()
        {
            return _activityDataList.All(data => data.rewardState == EActivityRewardState.Claimed);
        }

        #endregion

        #region ui
        public void OpenPanel(string key)
        {
            var keyList = key.Split('_');
            if (keyList.Length == 3 && keyList[1] == ACTIVITY_TYPE)
            {
                VIEW.OpenUI(UIDefine.ActivitySevendaysPanel);
            }
        }

        public void AddPanel(int activityId)
        {
            if (activityId == GetActivityBaseData().ActivityId)
            {
                if(Procedure.ProcedureContext.Instance.State.StateType != EStateType.MainCity 
                   || PanelManager.IsFunctionUIShow())
                {
                    return;
                }
                VIEW.OpenUI(UIDefine.ActivitySevendaysPanel);
            }
        }
        
        #endregion
    }
}
