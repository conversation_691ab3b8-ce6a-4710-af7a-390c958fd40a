﻿using Game.Common;
using ParadoxNotion.Design;
using Logger = ParadoxNotion.Services.Logger;

namespace Game.CityEventSystem
{
    [Name("透明度")]
    public class Fade : ActionBase
    {
        [Head<PERSON>("透明度值"), Slider<PERSON>ield(0, 1)]
        public float Value;

        [Header("物体或角色ID")]
        public string Id;

        [<PERSON><PERSON>("变换时间")]
        public float Duration;

        protected override void OnExecute()
        {
            if (string.IsNullOrEmpty(Id))
            {
                Logger.LogError("Fade ID is null");
                EndAction(false);
                return;
            }

            var obj = ActionBridge.CurrentBridge.GetTargetObject(Id);
            if (obj == null)
            {
                Logger.LogError($"Fade object with id : {Id} not found");
                EndAction(false);
                return;
            }

            obj.gameObject.FadeIn(Duration, 1 - Value);
            EndAction(true);
        }
    }
}