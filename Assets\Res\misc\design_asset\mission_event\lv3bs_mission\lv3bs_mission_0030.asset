%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 28bb8ce96b064d87aeaa4b3f25882879, type: 3}
  m_Name: lv3bs_mission_0030
  m_EditorClassIdentifier: 
  _serializedGraph: '{"type":"Game.CityEventSystem.CityEventTree","nodes":[{"_position":{"x":2.614746,"y":363.8342},"$type":"Game.CityEventSystem.NodeStart","$id":"0"},{"action":{"lockInput":true,"Node":{"$ref":"1"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.PlayerInput"},"_name":"\u7528\u6237\u8f93\u5165","_position":{"x":129.5437,"y":341.9879},"$type":"Game.CityEventSystem.NodeAction","$id":"1"},{"action":{"ObjectId":"lv3bs_hominid_1_interact_1","Node":{"$ref":"2"},"_unfolded":true,"$type":"Game.CityEventSystem.Interaction.Show"},"_name":"\u4ea4\u4e92\u7269\u663e\u793a\u6216\u9690\u85cf","_position":{"x":315.2667,"y":344.6179},"$type":"Game.CityEventSystem.NodeAction","$id":"2"},{"action":{"Duration":1.0,"Node":{"$ref":"3"},"_unfolded":true,"$type":"Game.CityEventSystem.Wait"},"_name":"\u7b49\u5f85","_position":{"x":640.6537,"y":297.5651},"$type":"Game.CityEventSystem.NodeAction","$id":"3"},{"action":{"RoleId":"lv3bs_hominid_1","TargetPath":"Interactions/lv3bs_CatchAnimals/completepoint2","Node":{"$ref":"4"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.Teleportation"},"_name":"\u89d2\u8272\u4f20\u9001","_position":{"x":756.6556,"y":-55.60495},"$type":"Game.CityEventSystem.NodeAction","$id":"4"},{"action":{"RoleId":"lv3bs_hominid_1","TargetId":"lv3bs_completepoint1","Node":{"$ref":"5"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.Rotation"},"_name":"\u89d2\u8272\u8f6c\u5411","_position":{"x":1405.746,"y":-46.09537},"$type":"Game.CityEventSystem.NodeAction","$id":"5"},{"action":{"RoleId":"Player","TargetPath":"Interactions/lv3bs_CatchAnimals/completepoint1","Node":{"$ref":"6"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.Teleportation"},"_name":"\u89d2\u8272\u4f20\u9001","_position":{"x":806.0882,"y":304.231},"$type":"Game.CityEventSystem.NodeAction","$id":"6"},{"action":{"RoleId":"Player","TargetId":"lv3bs_completepoint2","Node":{"$ref":"7"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.Rotation"},"_name":"\u89d2\u8272\u8f6c\u5411","_position":{"x":1406.736,"y":332.5062},"$type":"Game.CityEventSystem.NodeAction","$id":"7"},{"action":{"Duration":1.0,"Node":{"$ref":"8"},"_unfolded":true,"$type":"Game.CityEventSystem.Wait"},"_name":"\u7b49\u5f85","_position":{"x":1642.022,"y":357.5151},"$type":"Game.CityEventSystem.NodeAction","$id":"8"},{"action":{"ObjectId":"lv3bs_catch_animals_wall","Node":{"$ref":"9"},"_unfolded":true,"$type":"Game.CityEventSystem.Interaction.Show"},"_name":"\u4ea4\u4e92\u7269\u663e\u793a\u6216\u9690\u85cf","_position":{"x":2222.898,"y":147.7609},"$type":"Game.CityEventSystem.NodeAction","$id":"9"},{"action":{"BubbleDialogueUIInfo":{"BubbleDialogueType":1,"ImagePath":"1","DialogueId":"\u8c22\u8c22\u4f60\u597d\u5fc3\u4eba\uff01\u8fd9\u662f\u4f60\u7684\u62a5\u916c\uff01","CharacterId":"lv3bs_hominid_1","BubbleContinueTime":2.0},"Node":{"$ref":"10"},"_unfolded":true,"$type":"Game.CityEventSystem.Dialogue"},"_name":"\u5bf9\u8bdd","_position":{"x":2103.405,"y":343.7819},"$type":"Game.CityEventSystem.NodeAction","$id":"10"},{"action":{"Duration":2.0,"Node":{"$ref":"11"},"_unfolded":true,"$type":"Game.CityEventSystem.Wait"},"_name":"\u7b49\u5f85","_position":{"x":2500.381,"y":356.0071},"$type":"Game.CityEventSystem.NodeAction","$id":"11"},{"action":{"Node":{"$ref":"12"},"$type":"Game.CityEventSystem.Role.PlayerInput"},"_name":"\u7528\u6237\u8f93\u5165","_position":{"x":2985.381,"y":359.0071},"$type":"Game.CityEventSystem.NodeAction","$id":"12"},{"waitConditions":[false],"_position":{"x":3220.467,"y":393.1509},"$type":"Game.CityEventSystem.NodeEnd","$id":"13"},{"action":{"AnimationId":"Victory","RoleId":"Player","WaitForAnimationFinish":true,"Node":{"$ref":"14"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.PlayAnimation"},"_name":"\u89d2\u8272\u64ad\u653e\u52a8\u753b","_position":{"x":2141.262,"y":518.752},"$type":"Game.CityEventSystem.NodeAction","$id":"14"},{"action":{"AnimationId":"happy","RoleId":"lv3bs_elf_1","WaitForAnimationFinish":true,"Node":{"$ref":"15"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.PlayAnimation"},"_name":"\u89d2\u8272\u64ad\u653e\u52a8\u753b","_position":{"x":2158.286,"y":692.7819},"$type":"Game.CityEventSystem.NodeAction","$id":"15"},{"action":{"AnimationId":"idle","RoleId":"lv3bs_elf_1","Node":{"$ref":"16"},"_unfolded":true,"$type":"Game.CityEventSystem.Role.PlayAnimation"},"_name":"\u89d2\u8272\u64ad\u653e\u52a8\u753b","_position":{"x":2399.659,"y":701.2809},"$type":"Game.CityEventSystem.NodeAction","$id":"16"},{"action":{"ObjectId":"lv3bs_treasure_1","IsShow":true,"Node":{"$ref":"17"},"_unfolded":true,"$type":"Game.CityEventSystem.Interaction.Show"},"_name":"\u4ea4\u4e92\u7269\u663e\u793a\u6216\u9690\u85cf","_position":{"x":2162.866,"y":881.4179},"$type":"Game.CityEventSystem.NodeAction","$id":"17"},{"action":{"AudioName":"Play_sfx_rpg_pet_appear","InteractionId":"lv3bs_treasure_1","ChildRelativePath":"Sound","Node":{"$ref":"18"},"_unfolded":true,"$type":"Game.CityEventSystem.PlayInteractionAudio"},"_name":"\u4ea4\u4e92\u7269\u64ad\u653e\u97f3\u9891","_position":{"x":2170.866,"y":1066.418},"$type":"Game.CityEventSystem.NodeAction","$id":"18"},{"action":{"Node":{"$ref":"19"},"_unfolded":true,"$type":"Game.CityEventSystem.Camera.FadeOutAndIn"},"_name":"\u753b\u9762\u6de1\u5165\u6de1\u51fa","_position":{"x":720.5787,"y":606.31},"$type":"Game.CityEventSystem.NodeAction","$id":"19"}],"connections":[{"_sourceNode":{"$ref":"0"},"_targetNode":{"$ref":"1"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"1"},"_targetNode":{"$ref":"2"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"2"},"_targetNode":{"$ref":"3"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"2"},"_targetNode":{"$ref":"19"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"3"},"_targetNode":{"$ref":"4"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"3"},"_targetNode":{"$ref":"6"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"4"},"_targetNode":{"$ref":"5"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"6"},"_targetNode":{"$ref":"7"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"7"},"_targetNode":{"$ref":"8"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"9"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"10"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"14"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"15"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"17"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"8"},"_targetNode":{"$ref":"18"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"10"},"_targetNode":{"$ref":"11"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"11"},"_targetNode":{"$ref":"12"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"12"},"_targetNode":{"$ref":"13"},"$type":"Game.CityEventSystem.ConnectionBase"},{"_sourceNode":{"$ref":"15"},"_targetNode":{"$ref":"16"},"$type":"Game.CityEventSystem.ConnectionBase"}],"canvasGroups":[],"localBlackboard":{"_variables":{"Id":{"_value":10030030,"_name":"Id","_id":"9c44caec-be7f-4e38-b553-0125cd96ecc8","$type":"NodeCanvas.Framework.Variable`1[[System.Int32,
    mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]"}}}}'
  _objectReferences: []
  _graphSource:
    _version: 3.31
    _category: 
    _comments: 
    _translation: {x: -903.1512, y: 44.78788}
    _zoomFactor: 0.5646264
  _haltSerialization: 0
  _externalSerializationFile: {fileID: 0}
