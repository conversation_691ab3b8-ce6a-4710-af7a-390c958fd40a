using Game.Common;
using Game.Data;
using Game.Module;
using System;
using DG.Tweening;
using UnityEngine;

//-----------------------------------------------
// [FILE] f_goods_item_simple.lua
// [DATE] 2024-01-09 16:46:49
// [CODE] BY wangyian01
// [MARK] 道具item
//-----------------------------------------------

/*
    ------------- 和容器结合 ---------------
    self.goodsItemContainer = GoodsContainer(self, uid, goods_item);
    self.goodsItemContainer:RgListener({
        OnSelectedItem = self.__OnSelectedGoodsItem,
    })
    
    ----------- 单独使用（或者容器外部回调中自定义处理） --------------
    self.UI.goods_uid = self:GetCompFromContainer(5, UIControlType.UI_GameObject);
    self.goodsItem = goods_item(self, self.UI.goods_uid); -- 实例化并绑定对应的GameObjectContainer
    
    显示指定配置id的道具
    self.goodsItem:SetData(cfgId, E_ITEM_TYPE.ITEM_GOODS); -- 道具配置id; -- 抽象道具类型，默认为E_ITEM_TYPE.ITEM_GOODS
*/

using ELEX.Config;
using Infinity;
using ItemInfo = faster.pb.impl.ItemInfo;

namespace Game.View
{
    public partial class com_goods_item_simple : IGoodsItem
    {
        private IdNumType m_data;
        private bool m_showFrame = true;
        private ItemBgType m_bgType = ItemBgType.Square;
        private Action<com_goods_item_simple> m_callBack;
        private bool gray;
        

        public IdNumType Data
        {
            get { return m_data; }
            private set { m_data = value; }
        }

        public com_goods_item_simple(ViewNode hoster, int id, string name_ = null, int subUid = 0) : base(id, name_)
        {
            OnAttach(hoster);
            __AutoInitUI();

            m_data = null;
            m_showFrame = true;
            m_bgType = ItemBgType.Square;
            
            InitEventUI();
        }



        private void InitEventUI()
        {
            ButtonAddClick(ui.button, OnClick);
        }

        private void OnClick()
        {
            if (m_callBack != null)
            {
                m_callBack.Invoke(this);
                return;
            }

            var cfg = GetCfg();
            if (cfg == null)
            {
                return;
            }
            ShowTips(cfg);
        }

        private void ShowTips(IConfItemData cfg)
        {
            // Skip tips implementation for now as it involves many dependencies
            // Original implementation includes hero card, worker props handling and tips display
        }
        
        public void SetData(int data, int itemType = 0)
        {
            m_data = new IdNumType { id = (int)data, type = (E_ITEM_TYPE)itemType };
            RefreshUI();
        }

        public override void SetNumType(NumberType type)
        {
            m_data.numType = type;
            base.SetNumType(type);
        }

        public void SetData(IdNumType data)
        {
            m_data = data;
            RefreshUI();
        }

        public IdNumType GetData()
        {
            return m_data;
        }

        public void SetOnClickCallBack(Action<com_goods_item_simple> callback)
        {
            m_callBack = callback;
        }

        public bool IsGrid()
        {
            return m_data != null && (m_data.cfg as ConfItem) != null && (m_data.cfg as ItemInfo).isGrid;
        }

        public bool IsItem()
        {
            return m_data != null && m_data.type == E_ITEM_TYPE.ITEM_GOODS;
        }

        public bool UseAtlas()
        {
            return m_data != null && MODULE.Item.UseAtlas(m_data.type);
        }

        public override void SetShowFrame(bool isShow)
        {
            m_showFrame = isShow;
        }

        public bool UseShowFrame()
        {
            IdNumType data = m_data;
            return m_data != null && MODULE.Item.UseShowFrame(data.type) && m_showFrame;
        }

        public IConfItemData GetCfg()
        {
            if (m_data == null)
            {
                Debug.LogError("goodsItem data error!");
                return null;
            }

            var data = m_data;
            if (data.cfg == null)
            {
                data.cfg = MODULE.Item.GetItemCfgById(data.id, data.type);
            }
            if (data.cfg == null)
            {
                Debug.LogWarning($"goodsItem cfg not find!! id:{data.id} type:{(data.type)}");
                return null;
            }
            
            return data.cfg;
        }

        public override void SetGray(bool grayValue)
        {
            gray = grayValue;
        }

        public void RefreshUI()
        {
            if (m_data == null)
            {
                return;
            }
            RefreshCfg();
        }

        public void RefreshCfg()
        {
            var cfg = GetCfg();
            if (cfg == null)
            {
                return;
            }

            SetActive(ui.icon, true);
            
            RefreshText(m_data);
            RefreshFrame(cfg);
            RefreshTop(cfg);
            RefreshIcon(cfg);
        }

        private void RefreshText(IdNumType data)
        {
            if (data != null)
            {
                string txt = string.Empty;
                if (data.numType == NumberType.None)
                {
                    txt = string.Empty;
                }
                else if(data.numType == NumberType.Real_Value)
                {
                    txt = data.num.ToString("N0");
                }
                else
                {
                    txt = Infinity.StringUtil.FormatResourceString(data.num);
                }
                SetTextValueTMP(ui.num_txt, txt);
            }
        }

        private void RefreshIcon(IConfItemData cfg)
        {
            if (ui.icon == -1)
            {
                return;
            }

            IdNumType data = m_data;
            IConfItemData config = cfg;

            if (data.type == E_ITEM_TYPE.ITEM_HERO)
            {
                // TODO: lua2C# Hero
                //MODULE.Hero.UISetHeroSmallHeadIcon(cfg, ui.icon);
                SetDynTex(ui.icon, config.Icon, false, gray);
            }
            else if (data.type == E_ITEM_TYPE.ITEM_ALLIANCE_GIFT)
            {
                SetDynTex(ui.icon, config.IconMini, false, gray);
            }
            else
            {
                SetDynTex(ui.icon, config.Icon, false, gray);
            }
        }

        private void RefreshTop(IConfItemData cfg)
        {
            var txt = (cfg as ConfItem)?.icon_text;
            bool showTop = !string.IsNullOrEmpty(txt);
            LuaFramework.PanelManager.SetActive(ui.top_bg, showTop);
            LuaFramework.PanelManager.SetActive(ui.top_txt, showTop);
            
            if (showTop)
            {
                var qualityCfg = MODULE.Item.GetQualitySpriteCfg(m_data.cfg.Quality);
                if(qualityCfg == null) return;
                
                LuaFramework.PanelManager.SetAtlasColor(ui.top_bg, qualityCfg.TxtBgColor);
                LuaFramework.PanelManager.SetTextTMPColor(ui.top_txt, qualityCfg.TxtColor);
                LuaFramework.PanelManager.SetTextValueWithTMP(ui.top_txt, txt);
            }
        }

        private void RefreshFrame(IConfItemData cfg)
        {
            if (ui.frame == -1)
            {
                return;
            }

            bool showFrame = UseShowFrame();
            SetActive(ui.frame, showFrame);
            if (!showFrame)
            {
                return;
            }

            IdNumType data = m_data;

            int quality = cfg.Quality;
            if (data.type == E_ITEM_TYPE.ITEM_HERO)
            {
                // TODO: lua2C# Hero
                //quality = quality ?? cfg.hero_quality;
            }

            var qualityCfg = MODULE.Item.GetQualitySpriteCfg(quality);
            if (qualityCfg == null)
            {
                SetActive(ui.frame, false);
                return;
            }
            
            LuaFramework.PanelManager.SetSprite(ui.frame, qualityCfg.Bg);
            SetActive(ui.frame, true);
            SetAtlasImageGray(ui.frame, gray);
        }

        public void ShowOrHideFrame(bool show_hide)
        {
            SetActive(ui.frame, show_hide);
        }

        public bool IsNeedCloseUI()
        {
            // var cfg = GetCfg() as ConfItem;
            // return (E_ITEM_SERVER_TYPE)cfg.type == E_ITEM_SERVER_TYPE.RelocateCity || cfg.bag_use == 4;
            return false;
        }

        public void SetItemBgType(ItemBgType type)
        {
            m_bgType = type;
        }

        // Container callbacks
        public override void OnSetItemData(IdNumType data, int index)
        {
            SetData(data);
        }

        public override void OnSelectedItem(IdNumType data, int index)
        {
            OnClick();
        }

        public void OnCancelItem(object data, int index)
        {
        }
        

        public bool OnBeforeClickItem(object data, int index)
        {
            return true;
        }

        public void OnSetRedPoint(int count)
        {
            // Red point implementation
        }

        public int GetIconId()
        {
            return ui.icon;
        }
    }
}
