/**
 * Upload CDN Tool (Aliyun OSS 版本)
 * 用于将Unity构建的资源和配置文件上传到阿里云OSS
 */

import OSS from 'ali-oss';
import {BuildParam} from './build_params.js';
import {GitCdnManager} from './git_cdn_manager.ts';
import {Timer} from './timer.js';
import {delay, isInvalidStr, logFunctionEnd, logFunctionStart, logWithKey} from './utility.ts';
import {CDN_VERSION_PREFIX} from './const.ts';
import {stringify} from 'flatted';

// 敏感信息从环境变量中获取
const AccessKeyId = process.env.OSS_ACCESS_KEY_ID ?? '';
const AccessKeySecret = process.env.OSS_ACCESS_KEY_SECRET ?? '';
const Bucket = process.env.OSS_BUCKET ?? '';
const Region = process.env.OSS_REGION ?? '';
const Endpoint = process.env.OSS_ENDPOINT ?? '';
// logWithKey('OSS_PARAMS',`AccessKeyId:${AccessKeyId} AccessKeySecret:${AccessKeySecret} Bucket:${Bucket} Region:${Region}`)
logWithKey('OSS_PARAMS', `AccessKeyId:******* AccessKeySecret:****** Bucket:${Bucket} Region:${Region}`);

function createOssClient() {
    return new OSS({
        region: Region,
        accessKeyId: AccessKeyId,
        accessKeySecret: AccessKeySecret,
        bucket: Bucket,
        endpoint: Endpoint,
    });
}

export default async function test_upload_to_oss() {
    console.log(`upload_to_oss`);
    const client = createOssClient();
    try {
        const result = await client.list({ 'max-keys': 10 }, {});
        console.log('OSS list result:', result.objects);
    } catch (err) {
        console.error(err);
    }
}

/**
 * 上传资源到阿里云OSS
 * @param buildParam
 */
export async function uploadToOssWithParam(buildParam: BuildParam): Promise<void> {
    logFunctionStart(`uploadToOssWithParam:`, stringify(buildParam));

    if (buildParam.isUseOuterCDN() === false) {
        logFunctionEnd(`uploadToOssWithParam:`, 'skip upload to oss, isUseOuterCDN === false');
        return;
    }

    let state = buildParam.getState();
    let params = buildParam.getParams();
    const manager = new GitCdnManager(params.cdnRootDirectory, params.gitResDirectory, {
        env: state.envName,
        platform: state.platform,
        safeModel: true,
    });
    let cdnDirectory = manager.CdnRootDirectory;
    // 这里只自动上传配置和资源，暂时没有考虑字体
    let confs = await manager.getAllFilesInCurrentVersionWithPrefix(state.cfgVersion, [`${params.platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`]);
    let ress = await manager.getAllFilesInCurrentVersionWithPrefix(state.resVersion, [`${params.platform}_${CDN_VERSION_PREFIX.BUILD_RES}`]);
    let allfiles = [ ...(confs || []), ...(ress || []) ];
    logWithKey('uploadToOssWithParam', `needUpdateFilesCount:${allfiles.length}`);

    let existkeys = await checkAlreadyExistOssFiles();
    logWithKey('uploadToOssWithParam', `exist keys cnt:${existkeys.length}`);

    let uploadResult: boolean;
    try {
        uploadResult = await uploadByOssSdk(allfiles, cdnDirectory, existkeys);
    } catch (err) {
        logWithKey('uploadToOssWithParam', `uploadByOssSdk exception: ${err}`);
        throw new Error(`上传oss失败，uploadByOssSdk异常，详情请查看日志: ${err}`);
    }
    if (uploadResult === false) {
        logWithKey('uploadToOssWithParam', `upload failed`);
        throw new Error(`上传oss失败,详情请查看日志`);
    } else {
        logWithKey('uploadToOssWithParam', `upload success`);
    }
    logFunctionEnd('uploadToOssWithParam');
}

/**
 * 批量上传文件到OSS，失败自动重试
 */
export async function uploadByOssSdk(localfiles: string[], baseDir: string, existKeys: string[]): Promise<boolean> {
    logFunctionStart('uploadByOssSdk', `baseDir:${baseDir}`);
    const UPLOAD_TO_OSS_TIMER = 'UPLOAD_TO_OSS_TIMER';
    Timer.start(UPLOAD_TO_OSS_TIMER);
    const client = createOssClient();

    let uploadFiles = localfiles;
    let retry: boolean = false;
    let uploadIndex = 0;
    let MAX_UPLOAD_CNT = 10;
    let uploadSuccess: boolean = false;
    do {
        uploadIndex++;
        const ossfiles: { localfile: string, key: string }[] = [];
        for (const localfile of uploadFiles) {
            let key = localfile.substring(baseDir.length + 1).replace(/\\/g, '/');
            if (existKeys.indexOf(key) === -1) {
                logWithKey('uploadByOssSdk', `key:${key},localfile:${localfile}`);
                ossfiles.push({ localfile, key });
            }
        }
        logWithKey('uploadByOssSdk', `Upload Start,uploadIndex:${uploadIndex},upload total ossfiles count:${ossfiles.length}`);

        let allvalid = true;
        let fails: string[] = [];
        for (const file of ossfiles) {
            try {
                await client.put(file.key, file.localfile);
                logWithKey('uploadByOssSdk', `upload success: ${file.key}`);
            } catch (err) {
                allvalid = false;
                fails.push(file.localfile);
                logWithKey('uploadByOssSdk', `upload error: ${file.key}, err: ${err}`);
            }
        }
        logWithKey('uploadByOssSdk', `Upload Result,uploadIndex:${uploadIndex},success:${allvalid}`);
        retry = !allvalid;
        if (retry)
            uploadFiles = fails;
        else
            uploadSuccess = true;
        await delay(1000);
    } while (retry && uploadIndex < MAX_UPLOAD_CNT);

    logWithKey('uploadByOssSdk', `Upload Finish,success:${uploadSuccess},duration:${Timer.formatDuration(Timer.end(UPLOAD_TO_OSS_TIMER))}`);
    return uploadSuccess;
}

/**
 * 检查OSS中已存在的文件列表
 */
export async function checkAlreadyExistOssFiles(): Promise<string[]> {
    logFunctionStart('checkAlreadyExistOssFiles');
    const client = createOssClient();
    let existKeys: string[] = [];
    let nextMarker: string | undefined = undefined;
    do {
        const result = await client.list({ 'max-keys': 1000, marker: nextMarker }, {});
        nextMarker = result.nextMarker;
        if (result.objects && result.objects.length > 0) {
            result.objects.forEach((item: { name: string }) => {
                if (item.name) {
                    existKeys.push(item.name);
                }
            });
        }
        logWithKey('checkAlreadyExistOssFiles', `nextMarker:${nextMarker}`);
        await delay(500);
    } while (isInvalidStr(nextMarker) === false);
    logWithKey('checkAlreadyExistOssFiles', `oss total count:${existKeys.length}`);
    logFunctionEnd('checkAlreadyExistOssFiles');
    return existKeys;
} 