using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Game.Battle;

namespace GAS.Runtime
{
    /// <summary>
    /// 组合型目标捕获区域，可以包含多个不同类型的CatchAreaData
    /// </summary>
    public class CatchAreaComposite : CatchArea
    {
        // 包含的所有捕获区域数据
        public CatchAreaData[] catchAreaDatas = new CatchAreaData[0];
        
        // 用于物理检测的碰撞器数组
        private static readonly Collider[] colliders = new Collider[32];

        protected override void CatchTargetsNonAlloc(AbilitySystemComponent mainTarget, List<AbilitySystemComponent> results)
        {
          
            InitializeCache();
            
            try
            {
                if (catchAreaDatas != null)
                {
                    for (int i = 0; i < catchAreaDatas.Length; i++)
                    {
                        CatchAreaData data = catchAreaDatas[i];
                        switch (data.AreaType)
                        {
                            case CatchAreaType.Box:
                                CatchTargetsInBox(data, mainTarget, results);
                                break;
                            case CatchAreaType.Sector:
                                CatchTargetsInSector(data, mainTarget, results);
                                break;
                        }
                    }
                }
                AddSelfIfNeeded(results);
                PostProcessResults(results);
#if UNITY_EDITOR
                OnEditorPreview(this.Owner.gameObject);
#endif
            }
            finally
            {
               
                ClearCache();
            }
        }

#if UNITY_EDITOR
        public override void OnEditorPreview(GameObject obj)
        {
            base.OnEditorPreview(obj);
            
            // 为每个CatchAreaData绘制预览
            if(catchAreaDatas == null) return;
            foreach (var data in catchAreaDatas)
            {
                switch (data.AreaType)
                {
                    case CatchAreaType.Box:
                        DrawBoxPreview(obj, data);
                        break;
                        
                    case CatchAreaType.Sector:
                        DrawSectorPreview(obj, data);
                        break;
                }
            }
        }
        
        // 绘制盒形区域预览
        private void DrawBoxPreview(GameObject obj, CatchAreaData data)
        {
            // 1. 获取基础位置
            Vector3 pos = obj.transform.position;
            Quaternion baseRotation = obj.transform.rotation;

            // 2. 计算中心点偏移
            Vector3 offset = baseRotation * new Vector3(data.CenterOffset.x, 0, data.CenterOffset.y);
            
            // 3. 计算旋转后的前向方向（用于底部偏移）
            Quaternion finalRotation = baseRotation * Quaternion.Euler(0, data.OffsetAngle, 0);
            Vector3 rotatedForward = finalRotation * Vector3.forward;

            // 4. 应用中心点偏移和底部偏移
            Vector3 center = pos + offset;
            
            // 计算盒子尺寸
            Vector3 box = new Vector3(
                data.BoxX.GetFloatValue(this.Owner),
                3f,
                data.BoxZ.GetFloatValue(this.Owner)
            );
            
            if (data.IsBottom)
            {
                center += rotatedForward * box.z/2;
            }

            Vector3 size = box * 0.5f;

            // 计算8个顶点
            Vector3[] points = new Vector3[8];
            points[0] = center + finalRotation * new Vector3(-size.x, -size.y, -size.z);
            points[1] = center + finalRotation * new Vector3(size.x, -size.y, -size.z);
            points[2] = center + finalRotation * new Vector3(size.x, -size.y, size.z);
            points[3] = center + finalRotation * new Vector3(-size.x, -size.y, size.z);
            points[4] = center + finalRotation * new Vector3(-size.x, size.y, -size.z);
            points[5] = center + finalRotation * new Vector3(size.x, size.y, -size.z);
            points[6] = center + finalRotation * new Vector3(size.x, size.y, size.z);
            points[7] = center + finalRotation * new Vector3(-size.x, size.y, size.z);

            // 绘制底部四条线
            Debug.DrawLine(points[0], points[1], Color.green,1);
            Debug.DrawLine(points[1], points[2], Color.green, 1);
            Debug.DrawLine(points[2], points[3], Color.green, 1);
            Debug.DrawLine(points[3], points[0], Color.green, 1);

            // 绘制顶部四条线
            Debug.DrawLine(points[4], points[5], Color.green, 1);
            Debug.DrawLine(points[5], points[6], Color.green, 1);
            Debug.DrawLine(points[6], points[7], Color.green, 1);
            Debug.DrawLine(points[7], points[4], Color.green, 1);

            // 绘制连接顶部和底部的四条线
            Debug.DrawLine(points[0], points[4], Color.green, 1);
            Debug.DrawLine(points[1], points[5], Color.green, 1);
            Debug.DrawLine(points[2], points[6], Color.green, 1);
            Debug.DrawLine(points[3], points[7], Color.green, 1);

            // 绘制前向方向（用于调试）
            Debug.DrawRay(pos, rotatedForward * 2f, Color.blue, 1);
        }
        
        // 绘制扇形区域预览
        private void DrawSectorPreview(GameObject obj, CatchAreaData data)
        {
            // 获取半径 - 直接使用RadiusSelector获取值，而不是依赖临时变量radius
            float radius = data.RadiusSelector.GetFloatValue(this.Owner);
                
            // 考虑物体的当前旋转
            float objectYRotation = obj.transform.eulerAngles.y;
            Vector3 center = obj.transform.position + obj.transform.rotation * new Vector3(data.CenterOffset.x, 0, data.CenterOffset.y);

            float startAngle = -data.Angle / 2f + data.OffsetAngle + objectYRotation;
            float endAngle = data.Angle / 2f + data.OffsetAngle + objectYRotation;
            int segments = 36;

            Vector3 previousPoint = center + Quaternion.Euler(0, startAngle, 0) * Vector3.forward * radius;

            for (int i = 0; i <= segments; i++)
            {
                float currentAngle = Mathf.Lerp(startAngle, endAngle, (float)i / segments);
                Vector3 currentPoint = center + Quaternion.Euler(0, currentAngle, 0) * Vector3.forward * radius;

                Debug.DrawLine(center, currentPoint, Color.yellow, 1);
                if (i > 0) Debug.DrawLine(previousPoint, currentPoint, Color.yellow, 1);

                previousPoint = currentPoint;
            }
        }
#endif
        // 在盒形区域中捕获目标
        private void CatchTargetsInBox(CatchAreaData data, AbilitySystemComponent mainTarget, List<AbilitySystemComponent> results)
        {
            // 计算盒子尺寸
            Vector3 box = new Vector3(
                data.BoxX.GetFloatValue(this.Owner),
                3f,
                data.BoxZ.GetFloatValue(this.Owner)
            );
            // 计算检测位置
            Vector3 pos;
            Transform baseTransform;
            if (mainTarget != null)
            {
                baseTransform = data.OffsetType == OffsetType.Self ? this.Owner.transform : mainTarget.transform;
            }
            else
            {
                baseTransform = this.Owner.transform;
            }
            pos = baseTransform.position;

            // 应用偏移和旋转
            Quaternion baseRotation = baseTransform.rotation;
            Quaternion finalRotation = baseRotation * Quaternion.Euler(0, data.OffsetAngle, 0);
            pos += finalRotation * new Vector3(data.CenterOffset.x, 0, data.CenterOffset.y);
            Vector3 rotatedForward = finalRotation * Vector3.forward;

            // 底部偏移
            if (data.IsBottom)
            {
                pos += rotatedForward * box.z/2;
            }
            
            UnicornCharacter character = this.Owner.GetComponent<UnicornCharacter>();
            // 执行物理检测
            int count = Physics.OverlapBoxNonAlloc(pos, box/2, colliders, finalRotation);

            for (int i = 0; i < count; i++)
            {
                Collider c = colliders[i];
                AbilitySystemComponent ac = c.GetComponent<AbilitySystemComponent>();
                if (ac != null && IsValidTarget(ac))
                {
                    results.Add(ac);
                }
            }
        }
        
        // 在扇形区域中捕获目标
        private void CatchTargetsInSector(CatchAreaData data, AbilitySystemComponent mainTarget, List<AbilitySystemComponent> results)
        {
            // 获取半径
            float radius = data.RadiusSelector.GetFloatValue(this.Owner);
            // 计算检测位置
            Vector3 pos = (data.OffsetType == OffsetType.Self ? this.Owner.transform.position : mainTarget.transform.position) + new Vector3(0, radius/2, 0);
            pos += (data.OffsetType == OffsetType.Self ? this.Owner.transform.rotation : mainTarget.transform.rotation) * new Vector3(data.CenterOffset.x, 0, data.CenterOffset.y);
            
            UnicornCharacter character = this.Owner.GetComponent<UnicornCharacter>();
            int count = Physics.OverlapSphereNonAlloc(pos, radius, colliders);
            
            for (int i = 0; i < count; i++)
            {
                Collider c = colliders[i];
                AbilitySystemComponent ac = c.GetComponent<AbilitySystemComponent>();
                if (ac != null && IsValidTarget(ac) && IsInFanShape(ac.transform.position, data))
                {
                    results.Add(ac);
                }
            }
        }
        
        // 判断目标是否在扇形区域内
        private bool IsInFanShape(Vector3 targetPosition, CatchAreaData data)
        {
            // 1. 计算实际的中心点（考虑偏移）
            Transform ownerTransform = this.Owner.transform;
            Vector3 center = ownerTransform.position + ownerTransform.rotation * new Vector3(data.CenterOffset.x, 0, data.CenterOffset.y);

            // 2. 计算到目标的方向向量
            Vector3 directionToTarget = targetPosition - center;
            float distanceToTarget = directionToTarget.magnitude;

            // 3. 计算前向基准方向（考虑偏转角度）
            Vector3 baseForward = ownerTransform.rotation * Quaternion.Euler(0, data.OffsetAngle, 0) * Vector3.forward;

            // 4. 计算目标方向与基准方向的夹角
            float angleToTarget = Vector3.SignedAngle(baseForward, directionToTarget, Vector3.up);
            
            // 5. 将角度转换到 [-180, 180] 范围
            angleToTarget = angleToTarget < -180 ? angleToTarget + 360 : angleToTarget;
            angleToTarget = angleToTarget > 180 ? angleToTarget - 360 : angleToTarget;

            // 6. 检查是否在扇形角度范围内
            return Mathf.Abs(angleToTarget) <= data.Angle / 2f;
        }
    }
}