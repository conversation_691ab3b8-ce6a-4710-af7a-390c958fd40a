<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Unity.Linker.Api.Output</name>
    </assembly>
    <members>
        <member name="T:Unity.Linker.Api.Output.Analytics.LinkerDataTable">
            <summary>
            Analytics data from UnityLinker
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.build_event_id">
            <summary>
            The build_event_id from the Editor
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.node_executed">
            <summary>
            Whether or not UnityLinker.exe was executed
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_always_link_assembly">
            <summary>
            The number of assemblies with the always link assembly attribute that were marked
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#alwayslinkassembly)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_swept_count_always_link_assembly">
            <summary>
            The number of assemblies with the always link assembly attribute there were deleted
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#alwayslinkassembly)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_always_link_assembly">
             <summary>
             The total number of assemblies with the always link assembly attribute
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#alwayslinkassembly)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_preserve">
            <summary>
            The number of types, methods, fields, events, properties, etc that were marked as a result of preserve attributes
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#preserve)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_preserve">
            <summary>
            The total number of preserve attributes found in all assemblies
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#preserve)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_preserve_body">
             <summary>
             The total number of preserve body attributes found in all assemblies
            
             This count includes occurrences that may end up being swept.
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#preservebody)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_required_member">
            <summary>
            The number of members that had the required member attribute and were marked
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requiredmember)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_swept_count_required_member">
            <summary>
            The number of members that had the required member attribute and were swept
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requiredmember)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_required_member">
             <summary>
             The total number of required member attributes found in all assemblies
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requiredmember)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_require_derived">
            <summary>
            The number of types that were marked due to the require derived attribute
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requirederived)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_swept_count_require_derived">
            <summary>
            The number of types that had require derived and were swept
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requirederived)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_require_derived">
             <summary>
             The total number of require derived attributes found in all assemblies
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requirederived)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_require_implementors">
            <summary>
            The number of types that were marked due to the require implementors attribute
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requireimplementors)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_swept_count_require_implementors">
            <summary>
            The number of types that had require implementors and were swept
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requireimplementors)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_require_implementors">
             <summary>
             The total number of require implementors attributes found in all assemblies
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requireimplementors)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_required_interface">
             <summary>
             The total number of required interface attributes found in all assemblies
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requiredinterfacetype)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_require_attribute_usages">
            <summary>
            The number of types that were marked due to the require attribute usages attribute
            #details
            [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requireattributeusages)
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_require_attribute_usages">
             <summary>
             The total number of require attribute usages attributes found in all assemblies
            
             This count includes occurrences that may end up being swept
             #details
             [Attribute Documentation](../../UnityLinker/ANNOTATIONS.md#requireattributeusages)
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_total_count_dynamic_dependency">
            <summary>
            The total number of dynamic dependency attributes found in all assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_marked_count_dynamic_dependency">
            <summary>
            The total number of members marked due to dynamic dependency attributes
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.attribute_swept_count_dynamic_dependency">
            <summary>
            The number of members with dynamic dependency attributes that were swept
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.assembly_counts_total_in">
            <summary>
            The number of assemblies the linker will process after resolving all references and rooting preservations
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.assembly_counts_link">
             <summary>
             The number of assemblies with the link action prior to the output step
            
             These are the assemblies that are swept
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.assembly_counts_copy">
             <summary>
             The number of assemblies with the copy or save action prior to the output step
            
             These assemblies are not swept
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.assembly_counts_delete">
             <summary>
             The number of assemblies with the deleted action prior to the output step
            
             This essentially means how many assemblies were removed because all references to them were swept
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.assembly_counts_total_out">
            <summary>
            The number of assemblies the linker produced as outputs
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unresolved_stubbing_total_count">
            <summary>
            The total number of marked types and methods that were stubbed
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unresolved_stubbing_missing_interface_method_count">
            <summary>
            The number of missing methods that were interface methods.
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unresolved_stubbing_missing_abstract_class_method_count">
            <summary>
            The number of missing methods that were from an abstract class
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unresolved_stubbing_missing_type_count">
            <summary>
            The number of marked types that could not be resolved and were stubbed as a result
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unresolved_stubbing_missing_method_count">
            <summary>
            The number of marked methods that could not be resolved and were stubbed as a result
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unrecognized_reflection_access_total_count">
            <summary>
            The total number of unrecognized reflection calls that appeared in marked methods across all assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unrecognized_reflection_access_core_count">
            <summary>
            The total number of unrecognized reflection calls that appear in class library assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unrecognized_reflection_access_unity_count">
             <summary>
             The total number of unrecognized reflection calls that appear in Unity maintained assemblies.
            
             A "unity maintained" assembly classifies as any of the following
             1) It has the `[UnityEngineModuleAssembly]` attribute
             2) The assembly name starts with `UnityEngine.`
             3) The assembly name starts with `Unity.`
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.unrecognized_reflection_access_user_count">
            <summary>
            The total number of unrecognized reflection calls that appear in assemblies that are neither Unity nor Core assemblies.
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.recognized_reflection_access_total_count">
            <summary>
            The total number of recognized reflection calls that appeared in marked methods across all assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.recognized_reflection_access_core_count">
            <summary>
            The total number of recognized reflection calls that appear in class library assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.recognized_reflection_access_unity_count">
             <summary>
             The total number of recognized reflection calls that appear in Unity maintained assemblies.
            
             A "unity maintained" assembly classifies as any of the following
             1) It has the `[UnityEngineModuleAssembly]` attribute
             2) The assembly name starts with `UnityEngine.`
             3) The assembly name starts with `Unity.`
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.recognized_reflection_access_user_count">
            <summary>
            The total number of recognized reflection calls that appear in assemblies that are neither Unity nor Core assemblies.
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_total_count">
             <summary>
             The total number of link xml files that were processed
            
             Note: This includes both files on disk and embedded resources
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_embedded_count">
            <summary>
            The total number of link xml files embedded in assemblies that were processed
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_embedded_unity_count">
             <summary>
             The number of embedded link xml files in Unity maintained assemblies
            
             A "unity maintained" assembly classifies as any of the following
             1) It has the `[UnityEngineModuleAssembly]` attribute
             2) The assembly name starts with `UnityEngine.`
             3) The assembly name starts with `Unity.`
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_embedded_user_count">
            <summary>
            The number of embedded link xml files in assemblies that are neither Unity nor Core assemblies.
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_file_count">
            <summary>
            The total number of link xml files from files on disk
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_assembly_preserve_all_total_count">
            <summary>
            The total number of link xml elements that preserve the entire assembly
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_assembly_preserve_all_unity_count">
            <summary>
            The number of link xml elements that preserve an entire Unity maintained assembly
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_assembly_preserve_all_core_count">
            <summary>
            The number of link xml elements that preserve an entire Core assembly
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.link_xml_assembly_preserve_all_user_count">
            <summary>
            The number of link xml elements that preserve an entire assembly that are neither Unity nor Core assemblies
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.engine_module_total_in">
             <summary>
             The number of engine modules that were input in the linker
            
             Note: This field will be null when engine code stripping is not enabled
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.engine_module_deleted">
             <summary>
             The number of engine modules that were removed
            
             Note: This field will be null when engine code stripping is not enabled
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.engine_module_total_out">
             <summary>
             The number of engine modules that survived engine module stripping
            
             Note: This field will be null when engine code stripping is not enabled
             </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_rule_set">
            <summary>
            The rule set that was used
            
            Possible Values:
              Minimal
              Conservative
              Aggressive
              Experimental
              Copy
            
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_enable_report">
            <summary>
            Whether or not emitting additional diagnostic reports was enabled
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_enable_snapshot">
            <summary>
            Whether or not the snapshot diagnostic ability was enabled
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_enable_engine_module_stripping">
            <summary>
            Whether or not engine code stripping was enabled
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_unity_root_strategy">
            <summary>
            The strategy used for determining the roots
            
            Possible Values:
              Undefined
              AllNonEngineAndNonClassLibraries
              UltraConservative
              LessConservative
              RootMonoBehaviours
              TypesInScenes
            
            </summary>
        </member>
        <member name="P:Unity.Linker.Api.Output.Analytics.LinkerDataTable.option_enable_ildump">
            <summary>
            Whether or not the diagnostic ability to dump the assemblies to il was enabled
            </summary>
        </member>
    </members>
</doc>
