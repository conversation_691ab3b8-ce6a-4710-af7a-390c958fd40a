using System.Collections.Generic;
using Coffee.UIExtensions;
using ELEX.Config;
using Game.Battle;
using Game.Common;
using Game.Common.Logic.Event;
using Game.Data;
using UnityEngine;
using Event = Game.Common.Logic.Event.Event;
using Time = UnityEngine.Time;

namespace Game.View
{
    public partial class hero_battling_item : ViewNode
    {
        #region 常量管理

        private const float REMAIN_RATIO_OFFSET = 0.0f; // 显示的时间偏移量 // old value:0.1f, 回调接口调整了，结束的时候会返回一个0
        private const string CD_LEFT_TXT_FORMAT = "F0"; // 倒计时的格式，动态调整


        private const string USE_SKILL_FX = "Play_sfx_ui_button_pve_skill"; // 点击释放技能的音效
        private const string SKILL_COULD_USE_FX = "Play_sfx_notice_pve_skillUseable"; // 技能可以使用的音效

        #endregion

        #region 成员变量 -- 显示使用

        private battlepve_heard_new battlepve_heard_new; // 头像处理

        #endregion

        #region 数据管理

        private UnicornEntity currentEntityData; // 当前数据
        private HeroItemData currentItemData; // 当前的item数据

        private bool isCouldClickItem = false; // 记录当前是否能使用技能
        private bool isCurrentHeroDead = false; // 当前的英雄是否死亡

        #region 技能配置数据

        private int currentUltimateSkillId = -1; // 当前的技能id - 大招
        private ConfUnicornPveSkill currentUltimateSkillConfig; // 当前的技能配置数据 - 大招

        private void InitSkillConfig(UnicornEntity data)
        {
            // 获取英雄数据中的skill
            if (data == null)
            {
                Log.UI.Error("战中UI获取英雄数据失败，不能初始化技能数据");
                return;
            }

            // 遍历英雄的技能，初始化相关配置
            currentUltimateSkillId = currentEntityData.UltimateSkill?.id ?? -1;
            currentUltimateSkillConfig = currentEntityData.UltimateSkill;
        }

        #endregion

        #endregion

        #region 周期

        public hero_battling_item(ViewNode hoster, int id, string name_) : base(id, name_)
        {
            this.OnAttach(hoster);
            this.__AutoInitUI();
            // InitAllTimer();
            // 调整数据初始化的位置
            battlepve_heard_new = new battlepve_heard_new(this.ui.battlepve_heard_new, this);
        }

        public override void Dispose()
        {
            base.Dispose();
            // StopAllTimer();
            RemoveBtnsClick();
            RemoveEvents();
            couldUseTime = 0;
        }

        #endregion

        #region 事件绑定

        private void BindEvents()
        {
            Event.Instance.AddListener<int, float, float>(this, EventDefine.BATTLE_SKILL_CD, OnSkillUsed);

            if (currentEntityData != null)
            {
                Event.Instance.AddListener<float>(this,
                    EventDefine.BATTLE_HERO_HP_EVENT(this.currentEntityData.configId), OnHpChanged);
            }
        }

        private void RemoveEvents()
        {
            Event.Instance.RemoveListener(this, EventDefine.BATTLE_SKILL_CD);
        }

        #region 技能事件

        private bool triggerSkillAfterCDOver = true; // 冷却后重新触发了技能

        /// <summary>
        /// 目前英雄只有一个技能，暂时就这么处理
        /// </summary>
        /// <param name="skillId"></param>
        /// <param name="remainCD"></param>
        private void OnSkillUsed(int skillId, float totalCD, float remainCD)
        {
            // 打印
            // Common.Log.UI.Info($"技能使用了，技能id：{skillId}，剩余CD：{remainCD}");

            // 技能id判定，不相同则不处理
            if (currentUltimateSkillId != skillId)
            {
                return;
            }

            // 如果已经死亡，也不刷新
            if (isCurrentHeroDead)
            {
                return;
            }

            // 设定skillCdEndTime
            skillRemainTime = remainCD;
            currentSkillTotalCD = totalCD;

            // // 技能来了，重新开启下timer
            // if (!IsSkillTimerRunning())
            // {
            //     ResetSkillTimer();
            // }

            if (!triggerSkillAfterCDOver)
            {
                triggerSkillAfterCDOver = true;
                PlaySkillClick(); // 技能使用了，播放点击
                StopSkillCdReady(); // 技能使用了，则关闭ready特效
                StopSkillCdOver(); // 使用技能之后，关闭CD完成的特效
            }

            RefreshSkillCd(); // 即时刷新一下UI
            // Debug.Log($"当前的英雄使用了技能，技能id ：{skillId}，剩余CD：{remainCD} 英雄id：{currentEntityData.configId} ");
        }

        #endregion

        #region 血条事件

        private void OnHpChanged(float ratio)
        {
            if (this.uiObjs.heroHpSlider != null)
            {
                this.uiObjs.heroHpSlider.value = ratio;
            }

            // 如果ratio小于0，则置灰
            if (ratio <= 0)
            {
                battlepve_heard_new.SetHeroIconGrey(true);
                this.SetActive(this.ui.support, false);
                isCurrentHeroDead = true;
                // Debug.Log($"当前的英雄已经死亡，不能使用技，英雄id：{currentEntityData.configId} ");
                // todo： 可能还有新的event过来，需要处理下
            }
        }

        #endregion

        #endregion

        #region 绑定点击

        private void BindBtnsClick()
        {
            this.uiObjs.itemBtn.AddListener(OnItemClick);
        }

        private void RemoveBtnsClick()
        {
            this.uiObjs.itemBtn.RemoveListener(OnItemClick);
        }

        /// <summary>
        /// Item的点击触发
        /// </summary>
        private void OnItemClick()
        {
            if (!isCouldClickItem)
            {
                return;
            }

            // 触发技能使用
            if (currentEntityData != null)
            {
                currentEntityData.Auto = true; // 设定后表示点击了技能

                // 释放技能的时候，播放音效
                UIMisc.PlayUISound(USE_SKILL_FX);

                // 播放使用特效
                SetActive(ui.vfx_skill_click, true);
            }
        }

        #endregion

        #region 内部方法

        private void SetItemData(UnicornEntity data)
        {
            ClearAllVFX();

            currentEntityData = data;
            currentItemData = DATA.Hero.GetHeroItem(data.configId);
            this.SetName(this.ui.root, data.configId.ToString());

            // 绑定需要数据赋值之后，不然绑定的时候用到的数据没有
            RemoveEvents(); // 先清空一下，防止重新赋值的时候没有清空
            BindEvents();
            BindBtnsClick();


            // 初始化英雄数据
            battlepve_heard_new.SetHeroItemData(currentItemData);
            // 初始化技能数据
            InitSkillConfig(currentEntityData);

            // 技能CD显示
            RefreshSkillCd();
        }

        #region 技能倒计时

        private float skillCdEndTime = -1f; // 倒计时结束的时间  - Time.time
        private float skillRemainTime = -1f; // 倒计时结束的时间  - Time.time
        private float currentSkillTotalCD = -1f; // 当前技能的总CD时间

        /// <summary>
        ///  获取技能的cd  
        /// </summary>
        /// <returns></returns>
        private double GetSkillTotalCD()
        {
            // if (currentUltimateSkillConfig == null)
            // {
            //     return 0.0f;
            // }
            //
            // if (skillUseTime < 1)
            // {
            //     return currentUltimateSkillConfig.pre_cd;
            // }
            // else
            // {
            //     return currentUltimateSkillConfig.cd;
            // }
            return currentSkillTotalCD;
        }

        private double GetSkilllRemainTime()
        {
            return skillRemainTime;
            if (currentUltimateSkillConfig == null)
            {
                return 0.0f;
            }

            var time = skillCdEndTime - Time.time;
            if (time <= 0)
            {
                return 0.0f;
            }

            return time;
        }

        private float GetCurrentSkillCdRatio()
        {
            if (currentUltimateSkillConfig == null)
            {
                return 0.0f;
            }

            var totalCd = GetSkillTotalCD();
            if (totalCd <= 0)
            {
                return 0.0f;
            }

            var remainTime = GetSkilllRemainTime();
            if (remainTime <= 0)
            {
                return 0.0f;
            }

            var res = remainTime / totalCd;
            return (float)res;
        }

        private void RefreshSkillCd()
        {
            // -1表示没有大招或者没有初始化，所以就不需要显示
            if (currentUltimateSkillConfig == null)
            {
                this.SetActive(this.ui.support, false);
                return;
            }

            // if (skillCdEndTime > 0)
            {
                // var time = GetSkilllRemainTime();
                var ratio = GetCurrentSkillCdRatio();

                // 比例在范围内，表示就能点击了
                if (ratio <= REMAIN_RATIO_OFFSET)
                    // if (ratio == 0)
                {
                    skillCdEndTime = -1f;
                    this.SetActive(this.ui.support, false);
                    SetActive(this.ui.cdLeftTxt, false);

                    // 设定可以点击
                    isCouldClickItem = (true);

                    // 这种时候表示快结束了，发一个推送
                    SendSkilCDOver();

                    // 播放可以使用技能的音效
                    UIMisc.PlayUISound(SKILL_COULD_USE_FX);

                    if (couldUseTime > 1)
                    {
                        // 技能好了，展示特效
                        PlayCdOver();
                        PlaySkillCdReady();
                        triggerSkillAfterCDOver = false; // 技能好了，重新刷新记录
                    }
                }
                else
                {
                    this.SetActive(this.ui.support, true);
                    SetActive(this.ui.cdLeftTxt, true);
                    if (this.uiObjs.support != null)
                    {
                        this.uiObjs.support.fillAmount = ratio;
                    }

                    isCouldClickItem = false;
                }

                SetCDLeftTxt();
            }
        }

        private int couldUseTime = 0; // 记录冷却好的次数

        private void SendSkilCDOver()
        {
            // 这里事件暂时这里抛出去，因为没有其他地方使用，并且这里和UI表现能统一
            Event.Instance.Broadcast(EventDefine.PVE_SKILL_CD_ALMOST_FINISH, currentUltimateSkillId);
            if (couldUseTime == 1) // 为1是因为初始化的时候会默认一次
            {
                Event.Instance.Broadcast(EventDefine.PVE_SKILL_CD_ALMOST_FINISH_FISRT_TIME,
                    UnicornPveControl.Instance.CurrPveSceneId, currentUltimateSkillId);
            }

            couldUseTime++;
        }

        private void SetCDLeftTxt()
        {
            var leftTime = GetSkilllRemainTime();
            if (leftTime <= 0)
            {
                this.SetTextValueTMP(this.ui.cdLeftTxt, "");
            }
            else
            {
                this.SetTextValueTMP(this.ui.cdLeftTxt, leftTime.ToString(CD_LEFT_TXT_FORMAT) + "s");
            }
        }

        #endregion

        #endregion

        #region 特效管理

        private Dictionary<string, UIParticle> particleSystems = new Dictionary<string, UIParticle>();

        private void PlayVFX(RectTransform rect)
        {
            if (rect == null)
            {
                return;
            }

            if (rect.gameObject == null)
            {
                return;
            }

            // 获取ParticleSystem particleSystem
            particleSystems ??= new Dictionary<string, UIParticle>();
            particleSystems.TryGetValue(rect.name, out UIParticle particleSystem);
            if (particleSystem == null)
            {
                // 从物体上获取
                particleSystem = rect.gameObject.GetComponent<UIParticle>();
                particleSystems[rect.name] = particleSystem;
            }

            if (particleSystem == null)
            {
                Log.UI.Error("战斗页面中未找到特效" + rect.name);
                return;
            }

            if (particleSystem.gameObject.activeSelf)
            {
                particleSystem.Resume();
            }
            else
            {
                particleSystem.gameObject.SetActive(true);
            }

            particleSystem.Play();
        }

        private void ClearVFX(RectTransform rect)
        {
            if (rect == null)
            {
                return;
            }

            if (rect.gameObject == null)
            {
                return;
            }

            rect.gameObject.SetActive(false);
            particleSystems ??= new Dictionary<string, UIParticle>();
            if (particleSystems.ContainsKey(rect.name))
            {
                particleSystems.Remove(rect.name);
            }
        }


        private void PlayCdOver()
        {
            PlayVFX(uiObjs.vfx_skill_cd_over);
            PlayVFX(uiObjs.vfx_skill_cd_over_enhance);
        }

        private void PlaySkillCdReady()
        {
            PlayVFX(uiObjs.vfx_skill_ready);
        }

        private void PlaySkillClick()
        {
            PlayVFX(uiObjs.vfx_skill_click);
        }

        private void StopSkillCdReady()
        {
            ClearVFX(uiObjs.vfx_skill_ready);
        }

        private void StopSkillCdOver()
        {
            ClearVFX(uiObjs.vfx_skill_cd_over);
            ClearVFX(uiObjs.vfx_skill_cd_over_enhance);
        }

        private void ClearAllVFX()
        {
            ClearVFX(uiObjs.vfx_skill_cd_over);
            ClearVFX(uiObjs.vfx_skill_cd_over_enhance);
            ClearVFX(uiObjs.vfx_skill_ready);
            ClearVFX(uiObjs.vfx_skill_click);
        }

        #endregion

        #region 外部接口

        /// <summary>
        /// 初始化item数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="index"></param>
        public void OnSetItemData(UnicornEntity data, int index)
        {
            // 初始化数据
            isCurrentHeroDead = false;
            skillCdEndTime = -1f;
            skillRemainTime = -1f;
            currentSkillTotalCD = -1f;

            couldUseTime = 0;
            OnHpChanged(1.0f); // 初始化

            SetItemData(data);
        }

        #endregion
    }
}
