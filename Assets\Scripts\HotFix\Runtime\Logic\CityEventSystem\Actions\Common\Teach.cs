﻿using System;
using System.Collections.Generic;
using Game.Common;
using Game.View;
using ParadoxNotion.Design;
using UnityEngine;

namespace Game.CityEventSystem
{
    public enum PageType
    {
        Image = 1,
        Video = 2,
    }
    
    public class PageData
    {
        [ParadoxNotion.Design.Header("资源路径")]
        public string ResPath; 
        [ParadoxNotion.Design.Header("文字说明key")]
        public string TextKey; 
        [ParadoxNotion.Design.Header("页面类型")]
        public PageType Type;
        
        public int UIIndex { get; set; }  //引导页面索引
        public int PageCount { get; set; }  //引导页面总数
    }
    
    [Name("图片视频教学")]
    public class Teach : ActionBase
    {
        public List<PageData> PageDatas;
        
        protected override void OnExecute()
        {
            int pageCount = PageDatas.Count;
            for (int i = 0; i < pageCount; i++)
            {
                PageDatas[i].UIIndex = i + 1;
                PageDatas[i].PageCount = pageCount;
            }
            Dictionary<string,object> parameters = new Dictionary<string, object>();
            parameters.Add("onCloseClickCallback", (Action)this.OnDialoguePanelCloseClick);
            parameters.Add("pageData", PageDatas);
            VIEW.OpenUI(UIDefine.ExploreEventTeaching, parameters);
        }
        
        private void OnDialoguePanelCloseClick()
        {
            EndAction(true);
        }
    }
}