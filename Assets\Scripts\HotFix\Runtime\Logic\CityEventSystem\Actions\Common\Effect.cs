﻿using ELEX.Common;
using ParadoxNotion.Design;
using Sirenix.OdinInspector;
using UnityEngine;
using Game.Common;

namespace Game.CityEventSystem
{
    [Name("特效")]
    public class Effect : ActionBase
    {
        [ParadoxNotion.Design.Header("特效名"), Required]
        public string EffectName;
        
        [ParadoxNotion.Design.Header("使用相对位置")]
        public bool IsRelativeToRole = false;
        
        [ParadoxNotion.Design.ShowIf("IsRelativeToRole", 1), ParadoxNotion.Design.Header("角色ID")]
        public string RoleId;

        [ParadoxNotion.Design.Header("特效位置")]
        public Vector3 EffectPos;

        [ParadoxNotion.Design.Header("特效朝向")]
        public Vector3 EffectRotation;

        private ParticleInstance _particleInstance;
        private bool _isLoading = false;
        
        protected override void OnExecute()
        {
            if (!_isLoading)
            {
                _isLoading = true;
                _particleInstance = ParticleManager.GetInstance().GetParticleInstance(EffectName, EParticleType.Scene);
                _particleInstance.AddLoadedCallBack(ParticleLoadedComplete);
                EndAction(true);
            }
            else
            {
                EndAction(false);
            }
        }

        private void ParticleLoadedComplete(ParticleInstance particleInstance)
        {
            Vector3 fianlPos = EffectPos;
            if (IsRelativeToRole)
            {
                var role = ActionBridge.CurrentBridge.GetRoleController(RoleId);
                if (role == null)
                {
                    ParadoxNotion.Services.Logger.LogError($"can't find role object with role id: {RoleId}");
                    EndAction(false);
                    return;
                }

                fianlPos = role.RoleTransform.position + role.RoleTransform.TransformDirection(EffectPos);
            }
            
            if (fianlPos != Vector3.zero)
                particleInstance.rootTran.position = fianlPos;
            

            if (EffectRotation != Vector3.zero)
                particleInstance.rootTran.rotation = Quaternion.Euler(EffectRotation);

            particleInstance.root.SetActive(false); // 先关再开，防止不重播
            particleInstance.root.SetActive(true); // 先关再开，防止不重播
            particleInstance.Play();
            
            _isLoading = false;
            
            // 加到缓存中后，会设定SetAutoRemove
            ParticleManager.GetInstance().AddCache(particleInstance, particleInstance.Duration);
        }

    }
}