#!/bin/bash -il


 
# 默认参数设置

<<COMMENT
ENV_NAME="develop"
ROOT_PATH="/Users/<USER>/u3dclient"
BRANCH_NAME="master"
DEBUG=true
WEB_SERVER_URL="http://localhost"
BUILD_TYPE="BuildAll"  # 可选：BuildAll,BuildRes, BuildApp, BuildConfig, BuildLua, BuildHybridCLR
CHANNEL="default"
CDN_RELATION_URL="game/client"
APP_MODEL="default"
TEST_SDK_PAY=false
SERVER_TYPE="dev"
DEFINE_SYMBOLS=""
GAME_VERSION="1.0.0"
UPDATE_VERSION=""
SERVER_GROUP="1"
PACK_NAME=""
CLEAR_LIBRARY=false
IS_GOOGLE_AAB=false
VERSION_CODE="1"
UPLOAD_HYBRID_CLR=false
SERVER_ID=""
GATEWAY=""
KEEP_GUEST=false
UNTIY_PATH=""
VERION_GIT_DIRECTORY=""
WEIXIN_MINI_GAME_CDN_ROOT=""
UPLOAD_MINI_GAME_TO_OFFICIAL=false
COMMENT


# 构建参数字符串
BUILD_PARAMS="rootPath=$ROOT_PATH
debug=$DEBUG
branchName=$BRANCH_NAME
webServerUrl=$WEB_SERVER_URL
buildType=$BUILD_TYPE
channel=$CHANNEL
cdnRelationUrl=$CDN_RELATION_URL
appModel=$APP_MODEL
testSDKPay=$TEST_SDK_PAY
serverType=$SERVER_TYPE
defineSymbols=$DEFINE_SYMBOLS
gameVersion=$GAME_VERSION
updateVersion=$UPDATE_VERSION
serverGroup=$SERVER_GROUP
packName=$PACK_NAME
clearLibrary=$CLEAR_LIBRARY
isGoogleAAB=$IS_GOOGLE_AAB
uploadHybridCLR=$UPLOAD_HYBRID_CLR
serverId=$SERVER_ID
gateway=$GATEWAY
gateWayWebSocketPort=$GATEWAY_WEBSOCKET_PORT
gateWayTcpPort=$GATEWAY_TCP_PORT
keepGuest=$KEEP_GUEST
unityPath=$UNITY_PATH
cdnRootDirectory=$VERSION_GIT_DIRECTORY
gitResDirectory=$GIT_RES_DIRECTORY
weixinMiniGameCdnRoot=$WEIXIN_MINI_GAME_CDN_ROOT
useGitSave=$USE_GIT_SAVE
uploadOuterCDN=$UPLOAD_OUTER_CDN
useEnvName=$USE_ENV_NAME
autoUpdateRemoteVersion=$AUTO_UPDATE_REMOTE_VERSION
uploadMiniGameToOfficial=$UPLOAD_MINI_GAME_TO_OFFICIAL
copyBuildinFileOption=$COPY_BUILDIN_FILE_OPTION"
# 执行构建
echo "开始构建..."
echo "使用以下参数:"
echo "$BUILD_PARAMS"

# 切换到工作目录
cd "$ROOT_PATH" || exit 1

#---------------------------------------------------切换分支----------------------------------------
# 获取远程分支名称
remote_branch=$BRANCH_NAME

# 提取本地分支名称（去掉 origin/minigame/ 前缀）
#local_branch=$(echo $remote_branch | sed 's/origin\/minigame\///')
local_branch=$(echo "$remote_branch" | sed 's|^origin/||')

echo "远程分支: $remote_branch"
echo "本地分支: $local_branch"

# 检查本地分支是否已存在
if git show-ref --verify --quiet refs/heads/$local_branch; then
    echo "本地分支 '$local_branch' 已存在，切换到该分支"
    git checkout $local_branch
else
    echo "创建并切换到新的本地分支 '$local_branch'"
    git checkout -b $local_branch $remote_branch
fi

# 显示当前分支
echo "当前分支: $(git branch --show-current)"
#---------------------------------------------------切换分支----------------------------------------

# -------------------------------------显示最新的10条提交日志-------------------------------------

echo -e "\n重置当前工作区..."
if ! git reset --hard; then
    echo "重置工作区失败" >&2
    exit 1
fi

# 1. 清理未追踪的文件
echo "清理未被git追踪的文件..."
git clean -fd

echo -e "\n更新git仓库..."
if ! git fetch origin; then
    echo "获取远程更新失败" >&2
    exit 1
fi

echo "更新当前分支..."
if ! git pull; then
    echo "更新当前分支失败" >&2
    exit 1
fi

echo -e "\n最近的10条提交记录："
echo "----------------------------------------"
git log -n 10 --pretty=format:"%h - %an, %ar : %s" --graph
echo -e "\n----------------------------------------\n"
# -------------------------------------显示最新的10条提交日志-------------------------------------


# -------------------------------执行构建命令-------------------------------
echo "执行 build_minigame_to_local_cdn.ts..."
cd scriptV2
#111111
export PATH="/opt/homebrew/bin/:$PATH"
#export PATH="$PATH:/opt/homebrew/bin"
export NODE_PATH=$(npm root -g)
npm install date-fns
npm install zx
npm install fs-extra
npm install crc-32
npm install cos-nodejs-sdk-v5
#11111
npm i
if ! npm run build-minigame "$BUILD_PARAMS"; then
    echo "build_minigame_to_local_cdn.ts 执行失败" >&2
    exit 1
fi
# -------------------------------执行构建命令-------------------------------



# 检查最后一个命令的退出状态
if [ $? -ne 0 ]; then
    echo "构建失败" >&2
    exit 1
fi

echo "构建完成"

# 清理工作目录（可选）
# rm -rf build/* # 示例清理命令