﻿using ParadoxNotion.Design;
using UnityEngine;
using Logger = ParadoxNotion.Services.Logger;

namespace Game.CityEventSystem
{
    [Name("等待")]
    public class Wait : ActionBase
    {
        [ParadoxNotion.Design.Header("等待时长")]
        [Range(0, float.MaxValue)]
        public float Duration;

        protected override void OnExecute()
        {
            if (Duration < 0)
            {
                Logger.LogError("Wait duration less to zero, please check it!");
                EndAction(false);
            }
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();
            if (ElapsedTime >= Duration)
            {
                EndAction(true);
            }
        }
    }
}