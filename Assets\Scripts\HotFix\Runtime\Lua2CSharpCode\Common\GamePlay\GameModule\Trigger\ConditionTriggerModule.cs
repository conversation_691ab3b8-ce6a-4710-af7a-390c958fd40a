using System;
using System.Collections.Generic;
using ELEX.Config;
using Game.Common;
// using Game.Common.ConditionSystem;
// using Game.Module.ConditionSystem;
using Game.Common.Check;
using Game.Common.Logic.Event;
using Game.Data;
using UnityEngine;
using Event = Game.Common.Logic.Event.Event;
using Time = UnityEngine.Time;

namespace Game.Module
{
    public abstract class TriggerBaseData
    {
        public virtual void OnSetRedPoint(int count, EntityMenuData param = null)
        {
        }
    }

    // TriggerData class
    public class TriggerData : TriggerBaseData
    {
        private ConfTriggerConfig __cfg;
        public ConfTriggerConfig Cfg { get { return __cfg; } }

        public TriggerData(ConfTriggerConfig cfg)
        {
            __cfg = cfg;
        }

        public override void OnSetRedPoint(int count, EntityMenuData param = null)
        {
            if (count <= 0) return;
            MODULE.ConditionTrigger.DoTrigger(__cfg, param);
        }
    }

    // CustomTriggerData class
    public class CustomTriggerData : TriggerBaseData
    {
        private string __key;
        private Action __func;

        public string Key { get { return __key; } }

        public Action Func
        {
            get { return __func; }
            set { __func = value; }
        }

        public CustomTriggerData(string key, Action func)
        {
            __key = key;
            __func = func;
        }

        public override void OnSetRedPoint(int count, EntityMenuData param = null)
        {
            if (count <= 0) return;

            MODULE.ConditionTrigger.RmCustomTrigger(__key);
            if (__func != null)
            {
                __func();
            }
        }
    }

    // ConditionTriggerModule class
    public partial class ConditionTriggerModule : IGameModule
    {
        private Dictionary<string, TriggerBaseData> __triggerData = new Dictionary<string, TriggerBaseData>();
        private Dictionary<string, List<string>> __triggerTypeData = new Dictionary<string, List<string>>();
        private List<string> __deleteList;
        private bool __initFlag;
        private Dictionary<int, ConfTriggerConfig> cfgMap;

        public Dictionary<string, TriggerBaseData> TriggerData { get { return __triggerData; } }
        public Dictionary<string, List<string>> TriggerTypeData { get { return __triggerTypeData; } }

        public void OnEnterGame()
        {
            __triggerData = new Dictionary<string, TriggerBaseData>();
            __triggerTypeData = new Dictionary<string, List<string>>();
            __deleteList = new List<string>();
            __initFlag = false;
            cfgMap = __GetConfig();

            Event.Instance.AddListener(this, EventDefine.OnEnterRPGLoadingFinsh, __OnSceneLoadFinished);
            Event.Instance.AddListener<bool>(this, EventDefine.OnCityModeLoadFinish, __OnCityModeLoadFinish);
            Event.Instance.AddListener<string, int>(this, EventDefine.OnCloseUI, __OnCloseUI);
            Event.Instance.AddListener<string, int>(this, EventDefine.OnShowUI, __OnShowUI);
            Event.Instance.AddListener<int>(this, EventDefine.PVE_SUCCESS_START, __OnPveSuccessStart);
            Event.Instance.AddListener<int, int>(this, EventDefine.PVE_SKILL_CD_ALMOST_FINISH_FISRT_TIME,
                __OnPveSkillCDFirstTimeOver);
            Event.Instance.AddListener<int>(this, EventDefine.PVE_SCENE_LOADED, __OnPveLoadScene);

            Event.Instance.AddListener<int>(this, EventDefine.BuildClamReward, (gid) => __OnBuildClamReward($"{gid}"));
            AddBuildingLevelUpListener();
            AddBuildingStateChangeListener();
        }

        public void OnEnterGameFinished() { }

        public void OnClearAll() { }

        private void __OnSceneLoadFinished()
        {
            __Init();
        }

        private void __OnCityModeLoadFinish(bool isSelf)
        {
            __Init();
            __ReStartGuide(); // 这里调整位置，因为进入rpg的时候也会init，不适合在init中调用重新开启引导
            if (MODULE.NewbieGuide.IsNewewbieGuideState())
            {
                if (!GLOABAL.CONDITION.CheckCondition("FinishPveBattle:90002"))
                {
                    ActiveTriggerByType(TriggerConfigType.NewbieGuide);
                    return;
                }

                if (!MODULE.BattleNpc.hasBoss)
                {
                    ActiveTriggerByType(TriggerConfigType.NewbieGuide);
                    return;
                }

                if (MODULE.BattleNpc.haveDieBoss)
                {
                    MODULE.BattleNpc.haveDieBoss = false;
                    Timer.DelayCall(() =>
                    {
                        ActiveTriggerByType(TriggerConfigType.NewbieGuide);
                    }, 2.5f);
                }
                else
                {
                    ActiveTriggerByType(TriggerConfigType.NewbieGuide);
                }
            }
            else
            {
                Timer.DelayCall(() =>
                {
                    ActiveTriggerByType(TriggerConfigType.FirstRecharge);
                }, 1f);
            }
        }

        private void __OnBuildClamReward(string buildGroupId)
        {
            var cfgIds = __GetBuildClamIds(buildGroupId);
            if (cfgIds != null && cfgIds.Count > 0)
            {
                foreach (var cfgId in cfgIds)
                {
                    ActiveTriggerByKey(cfgId);
                }
            }
        }

        #region UI的引导检测

        private void __OnCloseUI(string uiName, int pid)
        {
            ActiveTriggerByParamAndType(uiName, TriggerConfigType.CloseUIUnlock);
        }

        private void __OnShowUI(string uiName, int pid)
        {
            ActiveTriggerByParamAndType(uiName, TriggerConfigType.FirstOpenUI);
        }

        #endregion

        #region 通用获取

        /// <summary>
        /// 获取配置了UI相关操作的trigger，目前数据少，直接获取就行；如果未来数据多了在增加缓存
        /// 可以获取UI类型的，或者其他类型的
        /// </summary>
        /// <param name="paramStr"></param>
        /// <param name="configType"></param>
        /// <returns></returns>
        private List<string> __GetTriggerConfigIds(string paramStr, TriggerConfigType configType)
        {
            List<string> cfgIds = null;
            if (__triggerData == null)
                return cfgIds;
            
            foreach (var kvp in __triggerData)
            {
                var triggerData = kvp.Value as TriggerData;
                if (triggerData != null && triggerData.Cfg.type == (int)configType)
                {
                    if (triggerData.Cfg.type_param == paramStr)
                    {
                        if (cfgIds == null)
                            cfgIds = new List<string>();
                        cfgIds.Add(kvp.Key);
                    }
                }
            }
            return cfgIds;
        }

        private void ActiveTriggerByParamAndType(string paramStr, TriggerConfigType configType)
        {
            var cfgIds = __GetTriggerConfigIds(paramStr, configType);
            if (cfgIds != null && cfgIds.Count > 0)
            {
                foreach (var cfgId in cfgIds)
                {
                    ActiveTriggerByKey(cfgId);
                }
            }
        }

        #endregion

        private List<string> __GetBuildClamIds(string groupId)
        {
            List<string> cfgIds = null;
            if (__triggerData == null)
                return cfgIds;

            foreach (var kvp in __triggerData)
            {
                if (kvp.Value is TriggerData triggerData &&
                    triggerData.Cfg.type == (int)TriggerConfigType.ConstructionFinish)
                {
                    if (triggerData.Cfg.type_param == groupId)
                    {
                        if (cfgIds == null)
                            cfgIds = new List<string>();
                        cfgIds.Add(kvp.Key);
                    }
                }
            }

            return cfgIds;
        }

        private void __Init()
        {
            if (__initFlag)
                return;

            __initFlag = true;

            var cfgMap = this.cfgMap;
            if (cfgMap == null)
            {
                return;
            }

            var rootCheckNode = GLOABAL.CHECK.GetChild(CheckConst.TRIGGERS);
            if (rootCheckNode == null)
            {
                return;
            }

            foreach (var cfg in cfgMap)
            {
                __AddTrigger(cfg.Value, rootCheckNode as CheckGroup);
            }
        }

        public void ActiveTrigger(string key, EntityMenuData param = null)
        {
            if (!__triggerData.TryGetValue(key, out var triggerData))
                return;

            triggerData.OnSetRedPoint(1, param);
        }

        public void ActiveTriggerByType(TriggerConfigType type)
        {
            if (__triggerTypeData.TryGetValue(((int)type).ToString(), out var data) && data.Count > 0)
            {
                foreach (var v in data)
                {
                    if (__triggerData.TryGetValue(v, out var trigger))
                    {
                        if (trigger is TriggerData triggerData &&
                            GLOABAL.CONDITION.CheckCondition(triggerData.Cfg.conditions))
                        {
                            trigger.OnSetRedPoint(1);
                            break;
                        }
                    }
                }
            }
        }

        public void ActiveTriggerByKey(string key)
        {
            if (!__triggerData.TryGetValue(key, out var trigger))
            {
                Log.Trigger.Error($"TriggerData is null !!!! 当前的key = {key}");
                return;
            }

            if (trigger is TriggerData triggerData && GLOABAL.CONDITION.CheckCondition(triggerData.Cfg.conditions))
            {
                triggerData.OnSetRedPoint(1);
            }
        }

        private void __ReStartGuide()
        {
            //todo lua2c# by:pengtong.pt
            // var saveData = DATA.CustomServer.GetKeyValue(GuideModule.CUR_GUIDE_KEY);
            // if (saveData == null) return;
            //
            // if (int.TryParse(saveData.ToString(), out int guideId) && guideId > 0)
            // {
            //     MODULE.Guide.StartGuide(guideId, null);
            // }
        }

        private void __AddTrigger(ConfTriggerConfig config, CheckGroup rootCheckNode)
        {
            if (!__CheckValid(config))
                return;

            var data = new TriggerData(config);
            __triggerData[config.key.ToString()] = data;

            if (!__triggerTypeData.TryGetValue(data.Cfg.type.ToString(), out var typeData))
            {
                typeData = new List<string>();
                __triggerTypeData[data.Cfg.type.ToString()] = typeData;
            }

            typeData.Add(config.key.ToString());

            //todo lua2c# by:pengtong.pt
            // if (config.trigger_cond.Count > 0)
            // {
            //     var checkNode = rootCheckNode.AddChild(CheckConst.TRIGGER(config.key), new CheckTrigger(config.key.ToString(), config.trigger_cond.ToArray()));
            //     CheckSystem.GetInstance().Bind(this, data, checkNode);
            // }
        }

        private bool __CheckValid(ConfTriggerConfig config)
        {
            var cnt = DATA.CustomServer.GetKeyCount(config.key.ToString());
            if (cnt < 0)
                return false;

            if (config.times == 0)
                return true;

            return cnt < config.times;
        }

        public void DoTrigger(ConfTriggerConfig config, EntityMenuData param = null)
        {
            var keyString = config.key.ToString();
            if (!__triggerData.TryGetValue(keyString, out var data))
                return;

            if (GLOABAL.CONDITION.CheckCondition(config.conditions, param))
            {
                GLOABAL.ACTION.ExecuteAction(config.actions.ToArray());

                if (config.times > 0)
                {
                    DATA.CustomServer.AddKeyValue(keyString);
                }

                if (__CheckValid(config) && !config.trigger_once)
                    return;
            }
            else
            {
                if (config.remove_mode == 1)
                {
                    DATA.CustomServer.SetTriggerKeyValue(keyString, -1);
                }
                else
                {
                    return;
                }
            }

            __DeleteCheckNode(keyString);
            __DeleteConditionTypeData(data as TriggerData, keyString);
            __triggerData.Remove(keyString);
        }

        public void AddCustomTrigger(string key, List<string> conditions, Action func)
        {
            //todo lua2c# by:pengtong.pt (这里lua写的就有问题)
            // if (__triggerData.ContainsKey(key))
            // {
            //     UnityEngine.Debug.LogError($"custom trigger {key} is existed!!!");
            //     return;
            // }
            //
            // var data = new CustomTriggerData(key, func);
            // __triggerData[key] = data;
            //
            // if (!__triggerTypeData.TryGetValue(data.Key, out var typeData))
            // {
            //     typeData = new List<string>();
            //     __triggerTypeData[data.Key] = typeData;
            // }
            // typeData.Add(config.key);
            //
            // if (conditions != null && conditions.Count > 0)
            // {
            //     var rootNode = CheckSystem.GetInstance().GetChild(CheckConst.TRIGGERS);
            //     var checkNode = rootNode.AddChild(key, new CheckTrigger(key, conditions));
            //     CheckSystem.GetInstance().Bind(this, data, checkNode);
            // }
        }

        public void RmCustomTrigger(string key)
        {
            if (!__triggerData.ContainsKey(key))
                return;

            __DeleteCheckNode(key);
            __DeleteConditionTypeData(__triggerData[key], key);
            __triggerData.Remove(key);
        }

        private void __DeleteCheckNode(string key)
        {
            __deleteList.Add(key);
            if (__deleteList.Count == 1)
            {
                Timer.DelayCall(__RealClearNodes, 0);
            }
        }

        private void __DeleteConditionTypeData(TriggerBaseData data, string key)
        {
            if (data == null)
                return;
            var baseData = data as TriggerData;
            if (baseData == null)
                return;
            if (!__triggerTypeData.TryGetValue(baseData.Cfg.type.ToString(), out var list))
                return;

            list.Remove(key);
        }

        private void __RealClearNodes()
        {
            if (__deleteList.Count == 0)
                return;

            var rootNode = GLOABAL.CHECK.GetChild(CheckConst.TRIGGERS);
            for (int i = __deleteList.Count - 1; i >= 0; i--)
            {
                //todo lua2c# by:pengtong.pt (这里lua写的就有问题.没有RemoveNode函数)
                //rootNode.RemoveNode(__deleteList[i]);
            }

            __deleteList.Clear();
        }

        #region PVE战斗相关

        private void __OnPveSuccessStart(int pveSceneId)
        {
            if (pveSceneId == -1)
                return;

            ActiveTriggerByParamAndType(pveSceneId.ToString(), TriggerConfigType.PveBattleStart);
        }


        private void __OnPveSkillCDFirstTimeOver(int pveSceneId, int skillId)
        {
            if (pveSceneId == -1)
                return;

            ActiveTriggerByParamAndType(pveSceneId.ToString() + "," + skillId.ToString(),
                TriggerConfigType.PveSkillCdOver);
        }

        private void __OnPveLoadScene(int pveSceneId)
        {
            if (pveSceneId == -1)
                return;

            ActiveTriggerByParamAndType(pveSceneId.ToString(), TriggerConfigType.PveLoadScene);
        }

        #endregion

        #region 建筑相关

        /// <summary>
        /// 监听配置中所有的建筑状态变化的触发
        /// </summary>
        private void AddBuildingStateChangeListener()
        {
            // 获取配置类型为10的所有数据，并注册监听
            var allBuildingStatusConfigs = GetTriggersByType((int)TriggerConfigType.BuildingStateChange);
            // 进行注册
            foreach (var confTriggerConfig in allBuildingStatusConfigs)
            {
                if (string.IsNullOrEmpty(confTriggerConfig.type_param))
                {
                    Log.Trigger.Error(
                        $"策划修改： triggerConfig配置错误，类型{(int)TriggerConfigType.BuildingStateChange}需要填写type_param，第{confTriggerConfig.key}未进行填写");
                    continue;
                }

                Event.Instance.AddListener<int>(this, EventDefine.BuildingStatusChange(confTriggerConfig.type_param),
                    __OnBuildingStatusChanged);
            }
        }

        private void __OnBuildingStatusChanged(int groupId)
        {
            ActiveTriggerByParamAndType(groupId.ToString(), TriggerConfigType.BuildingLevelUpFinish);
        }

        /// <summary>
        /// 监听配置中所有建筑升级的触发
        /// </summary>
        private void AddBuildingLevelUpListener()
        {
            // 获取配置类型为10的所有数据，并注册监听
            var allBuildingLvConfigs = GetTriggersByType((int)TriggerConfigType.BuildingLevelUpFinish);
            // 进行注册
            foreach (var confTriggerConfig in allBuildingLvConfigs)
            {
                if (string.IsNullOrEmpty(confTriggerConfig.type_param))
                {
                    Log.Trigger.Error(
                        $"策划修改： triggerConfig配置错误，类型{(int)TriggerConfigType.BuildingLevelUpFinish}需要填写type_param，第{confTriggerConfig.key}未进行填写");
                    continue;
                }

                Event.Instance.AddListener<int>(this, EventDefine.BuildingLevelChange(confTriggerConfig.type_param),
                    __OnBuildingLevelChanged);
            }
        }

        private void __OnBuildingLevelChanged(int groupId)
        {
            ActiveTriggerByParamAndType(groupId.ToString(), TriggerConfigType.BuildingLevelUpFinish);
        }

        #endregion
    }
}
