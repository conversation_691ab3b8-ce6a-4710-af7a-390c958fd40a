import {JENKIN_BUILD_TIMER} from "./const.ts";
import {BuildParam} from "./build_params.ts";
import {Timer} from "./timer.ts";
import {buildUnity} from "./build_unity.ts";
import {saveBuildToGitCdn} from "./save_build_to_git_cdn.ts";
import {uploadToCosWithParam} from "./upload_to_cos.ts";
import {uploadToOssWithParam} from "./upload_to_oss.ts";
import {updateCentralControl} from "./update_central_control.ts";
import {sendErrorMessage, sendNotifyMessage} from "./send_notify_message.ts";
import {logFunctionEnd, logFunctionStart} from "./utility.ts";


/**
 * todo 打不同平台的包统一走这个方法
 */
async function build_player() {
    logFunctionStart('build_player')
    // 创建构建参数实例
    const buildParam = new BuildParam();
    try {
        Timer.start(JENKIN_BUILD_TIMER)
        // 解析基础参数
        await buildParam.parseParamsStr(process.argv[2]);
        await buildParam.initState();
        buildParam.ShowInfo();
        // 执行构建
        await buildUnity(buildParam);
        // 上传到git保存一下版本
        await saveBuildToGitCdn(buildParam)
        //是否上传到外网
        // await uploadToCosWithParam(buildParam);
        await uploadToOssWithParam(buildParam); // 默认上传到oss
        // 上传二维码
        // 内网环境直接上传总控文件
        await updateCentralControl(buildParam);
        // warning 手动操作总控配置
        await sendNotifyMessage(buildParam)
    }
    catch (error) {
        console.error('build_player 失败:', error);
        let message = (error as Error).message
        await sendErrorMessage(buildParam,'打包失败',message)
        process.exit(1);
    }
    finally {
        logFunctionEnd('build_player')
    }
}

export { build_player };

// 直接运行此脚本
build_player().catch(console.error);
