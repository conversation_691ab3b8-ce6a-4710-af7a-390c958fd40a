using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System;
using System.Linq;
using Cinemachine;
using UnityEngine.Pool;
using Object = UnityEngine.Object;

public enum Enum_FindType
{
    NameEqual,
    NameContains,
    NameEndWith
}

[RequireComponent(typeof(PlayableDirector))]
public class TimelineFx : MonoBehaviour
{
    
    private static bool isLockTimeScale = false;

    private bool _inited = false;
    void Awake()
    {
       Init();
        // this._hasUIPostProcess = HasUIPostProcess();
    }

    private void Init(bool force = false)
    {
       // Debug.Log("TimelineFx Init _inited "+ _inited+" isTest "+ _isTest);
        if (_inited && !force) return;
        this.PlayableDirector = this.gameObject.GetComponent<PlayableDirector>();
        ConstructBindingDict();
      //  ConstructClipNameDict();
        this.TimelineAutoBinding = this.gameObject.GetComponent<TimelineAutoBinding>();
        _inited= true;
    }
    public string GetTrackNames()
    {
        return string.Join(",", _bindingDict.Keys);
    }

    private void OnDestroy()
    {
       
    }

    public void FireEvent(string arg)
    {
        
    }

    private Dictionary<string, PlayableBinding> _bindingDict = new();
    //private Dictionary<TimelineClip, string> _clipNameDict = new();
    private HashSet<CinemachineVirtualCameraBase> _bindedCameras = null;
    private double? _firstCameraEndTime = null;
    private CinemachineVirtualCameraBase _firstCamera = null;

    [HideInInspector][NonSerialized]
    public PlayableDirector PlayableDirector;

    [HideInInspector][NonSerialized]
    public TimelineAutoBinding TimelineAutoBinding;
    /*
    private bool _hasUIPostProcess= false;
*/
    public Action<PlayableDirector> OnStopped;
    public Action<PlayableDirector> OnPlayed;
    public Action<PlayableDirector> OnPaused;

    public bool IsModified
    {
        get { return _bindingDict.Count > 0; }
    }

    public bool IsPlaying => this.PlayableDirector && this.PlayableDirector.state == PlayState.Playing;
    public bool IsLoop => this.PlayableDirector && this.PlayableDirector.extrapolationMode == DirectorWrapMode.Loop;

    private bool _isTest = false;
    public void BeginPlay(bool isTest = false)
    {
        _isTest = isTest;
        Init();
        //Debug.LogError("Timeline begin Play "+this.gameObject.name ,this.gameObject);
        if (this.PlayableDirector)
        {
            if(this.TimelineAutoBinding != null)
            {
                this.TimelineAutoBinding.Bind(this,isTest);
            }
            this.PlayableDirector.stopped -= _OnStopped;
            this.PlayableDirector.stopped += _OnStopped;

            this.PlayableDirector.played -= _OnPlayed;
            this.PlayableDirector.played += _OnPlayed;

            this.PlayableDirector.paused -= _OnPaused;
            this.PlayableDirector.paused += _OnPaused;

            if (this.PlayableDirector.timeUpdateMode == DirectorUpdateMode.Manual)
            {
                this.PlayableDirector.time = 0;
                this.PlayableDirector.Play();
                this.PlayableDirector.Evaluate();
                this._manualPlayStatus = ManualPlayStatus.Playing;
            }
            else 
            {
                this.PlayableDirector.Play();
                this.PlayableDirector.Evaluate();
            }
        }
    }

    [ContextMenu("Stop")]
    public void Stop()
    {
        this.ClearCallback();
        ClearBindedCameras();
        if (this.PlayableDirector)
        {
            this.PlayableDirector.Stop();
        }
    }

    private void ClearBindedCameras()
    {
        if (_bindedCameras != null)
        {
            _bindedCameras.Clear();
            _bindedCameras = null;
        }
    }

    private void _OnStopped(PlayableDirector pd)
    {
        if (TimelineAutoBinding != null)
        {
            TimelineAutoBinding.UnBind(this);
            TimelineAutoBinding.Clear();
        }
        
        //Debug.LogError("Timeline OnStopped " + this.gameObject.name, this.gameObject);
        if (OnStopped != null)
        {
            OnStopped(pd);
            OnStopped = null;
        }

        ClearBindedCameras();
        ClearCallback();
        _firstCameraEndTime = null;
        _firstCamera = null;
        
        
        /*
        if (this._hasUIPostProcess)
        {
            DisableUICameraPostProcess();
        }
        */
    }

    private void _OnPlayed(PlayableDirector pd)
    {
        if (OnPlayed != null)
        {
            OnPlayed(pd);
        }
        //Debug.LogError("Timeline OnPlayed " + this.gameObject.name, this.gameObject);
        /*
        if (this._hasUIPostProcess)
        {
            EnableUICameraPostProcess();
        }
        */
    }

   
    private void _OnPaused(PlayableDirector pd)
    {
        if (OnPaused != null)
        {
            OnPaused(pd);
        }
    }

    public void ClearCallback()
    {
        OnStopped = null;
        OnPlayed = null;
        OnPaused = null;
    }

    private void OnDisable()
    {
        ClearCallback();
        if(TimelineAutoBinding != null)
        {
            TimelineAutoBinding.Clear();
        }
        /*
        if (this._hasUIPostProcess)
        {
            DisableUICameraPostProcess();
        }
        */
    }

    public void ConstructBindingDict()
    {
        _bindingDict.Clear();

        if (PlayableDirector && PlayableDirector.playableAsset)
        {
            var outputs = PlayableDirector.playableAsset.outputs;
           // Debug.Log($"TimelineFx ConstructBindingDict outputs count: {outputs.Count()}");
            
            foreach (var at in outputs)
            {
                if (!_bindingDict.ContainsKey(at.streamName))
                {
                    _bindingDict.Add(at.streamName, at);
                    //Debug.Log($"TimelineFx ConstructBindingDict track: {at.streamName}, type: {at.outputTargetType}, isTest: {_isTest}");
                }
            }
        }
    }

    /*
    public void ConstructClipNameDict()
    {
        _clipNameDict.Clear();

        foreach (var pair in this._bindingDict)
        {
            var track = GetBinding<TrackAsset>(pair.Value.streamName);
            if (track == null)
            {
                // Debug.Log("asd");
                continue;
            }

            foreach (var clip in track.GetClips())
            {
                _clipNameDict.Add(clip, clip.displayName);
            }
        }
    }
    */

    //-----------------------------------------------------------------
/*
    public void Apply(bool playAfterApplied = true)
    {
        // Cinemachine's clip name will be change after RebuildGraph().
        if (IsModified)
        {
            PlayableDirector.RebuildGraph();
            ApplyModifiedClipsName();
        }

        if (playAfterApplied)
        {
            PlayableDirector.Play();
        }
    }

    private void ApplyModifiedClipsName()
    {
        foreach (var dict in _clipNameDict)
        {
            dict.Key.displayName = dict.Value;
        }
    }

    private void ModifyClipName(TimelineClip modifiedClip, string clipName)
    {
        _clipNameDict[modifiedClip] = clipName;
    }
    */

    //-----------------------------------------------------------------

    public void GetAllTracks(List<PlayableBinding> result)
    {
        Init();
        result.AddRange(_bindingDict.Values);
    }

    public bool TryGetTrack(string trackName, out PlayableBinding binding)
    {
        Init();
        return _bindingDict.TryGetValue(trackName, out binding);
    }

    public bool HasTrack(string trackName)
    {
        Init();
        return _bindingDict.ContainsKey(trackName);
    }
    
    public PlayableBinding GetTrack(string trackName)
    {
        try
        {
            Init();
            return _bindingDict[trackName];
        }
        catch (System.Exception)
        {
            throw new System.Exception(string.Format("Track '{0}' is not exist.", trackName));
        }
    }

    /*
    public bool HasUIPostProcess() 
    {
        foreach (var item in _bindingDict)
        {
            var trackAsset = item.Value.sourceObject as TrackAsset;
            if(trackAsset is PostProcessTrack ppt)
            {
                var clips = ppt.GetClips();
                if (clips != null && clips.Any())
                {
                    foreach (var clip in clips)
                    {
                        if (clip.asset is PostProcessClip ppl) 
                        {
                            if (ppl.template != null)
                            {
                                if (ppl.template.volumeType == UnityEngine.Rendering.EVolumeType.UI)
                                { 
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }*/

    public TrackType GetBinding<TrackType>(string trackName) where TrackType : Object
    {
        if(!HasTrack(trackName))
        {
            return null;
        }
        return GetTrack(trackName).sourceObject as TrackType;
    }

    public bool TryGetBinding<T>(string trackName, out T binding) where T : Object
    {
        if (TryGetTrack(trackName, out var track))
        {
            binding = track.sourceObject as T;
            return true;
        }

        binding = default;
        return false;
    }

    public Object GetKey(string trackName)
    {
        return GetBinding<Object>(trackName);
    }
    
    public bool TryGetKey(string trackName, out Object binding)
    {
        return TryGetBinding(trackName, out binding);
    }

    public void SetBinding(string trackName, Object binding)
    {
        PlayableDirector.SetGenericBinding(GetKey(trackName), binding);
    }

    //-----------------------------------------------------------------

    private bool CheckClip(TimelineClip clip, string trackName, string findArg, Enum_FindType findType)
    {
        var displayName = clip.displayName;
        if (findType == Enum_FindType.NameEqual && displayName == findArg)
            return true;
        if (findType == Enum_FindType.NameContains && displayName.Contains(findArg))
            return true;
        if (findType == Enum_FindType.NameEndWith && displayName.EndsWith(findArg))
            return true;
        return false;
    }

    public TimelineClip GetClip(string trackName, string findArg, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        var track = GetBinding<TrackAsset>(trackName);
        var clips = track.GetClips();
        if (clips != null && clips.Any())
        {
            foreach (var clip in clips)
            {
                if (CheckClip(clip, trackName, findArg, findType))
                    return clip;
            }
        }

        return null;
    }

    public void GetClips(List<TimelineClip> result, string trackName, string findArg, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        var track = GetBinding<TrackAsset>(trackName);
        if (track == null)
        {
            Debug.LogWarning($"can not find track name :{trackName} ");
            return;
        }   
        var clips = track.GetClips();
        if (clips != null && clips.Any())
        {
            foreach (var clip in clips)
            {
                if (CheckClip(clip, trackName, findArg, findType))
                    result.Add(clip);
            }
        }
    }

    public void GetAllClips(List<TimelineClip> result, string trackName)
    {
        var track = GetBinding<TrackAsset>(trackName);
        if (track == null)
        {
            Debug.LogWarning($"can not find track name :{trackName} ");
        }

        result.AddRange(track.GetClips());
    }

    //----------------------------------------------------------------

    public ClipAssetType GetClipAsset<ClipAssetType>(string trackName, string findArg, Enum_FindType findType = Enum_FindType.NameEqual) where ClipAssetType : Object
    {
        var clip = GetClip(trackName, findArg, findType);
        if (clip == null)
        {
            return null;
        }

        ClipAssetType asset = clip.asset as ClipAssetType;
      //  ModifyClipName(clip, findArg);
        return asset;
    }

    public void GetClipsAssets<ClipAssetType>(List<ClipAssetType> result, string trackName, string findArg, Enum_FindType findType = Enum_FindType.NameEqual) where ClipAssetType : Object
    {
        using (ListPool<TimelineClip>.Get(out var clips))
        {
            GetClips(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                if (clip.asset is ClipAssetType asset)
                    result.Add(asset);
        //        ModifyClipName(clip, findArg);
            }
        }
    }

    public void GetAllClipsAssets<ClipAssetType>(List<ClipAssetType> result, string trackName) where ClipAssetType : Object
    {
        using (ListPool<TimelineClip>.Get(out var clips))
        {
            GetAllClips(clips, trackName);
            foreach (var clip in clips)
            {
                if (clip.asset is ClipAssetType asset)
                    result.Add(asset);
            }
        }
    }

    public ClipAssetType GetFirstAssets<ClipAssetType>(string trackName, out double endTime) where ClipAssetType : Object
    {
        endTime = 0;
        using (ListPool<TimelineClip>.Get(out var clips))
        {
            GetAllClips(clips, trackName);
            foreach (var clip in clips)
            {
                if (clip.start == 0)
                {
                    endTime = clip.end;
                    return clip.asset as ClipAssetType;
                }
            }
        }

        return null;
    }
    //----------------------------------------------------------------

    public void SetCinemachineClip(string trackName, string findArg, CinemachineVirtualCameraBase virtualCamera, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<CinemachineShot>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                clip.VirtualCamera = new ExposedReference<CinemachineVirtualCameraBase>()
                {
                    defaultValue = virtualCamera
                };

                if (_bindedCameras == null)
                {
                    _bindedCameras = new HashSet<CinemachineVirtualCameraBase>();
                }

                if (!_bindedCameras.Contains(virtualCamera))
                {
                    _bindedCameras.Add(virtualCamera);
                }
            }
        }
    }

    public void SetCinemachineClipFollow(string trackName, string findArg, Transform followTarget, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<CinemachineShot>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                var camera = clip.VirtualCamera.Resolve(this.PlayableDirector);
                camera.Follow = followTarget;
            }
        }

        // Debug.LogError(trackName+"  "+ clipName+" typename "+ GetClipAsset<CinemachineShot>(trackName, clipName).VirtualCamera.defaultValue.name);
        // var vCamera=  GetClipAsset<CinemachineShot>(trackName, clipName).VirtualCamera.defaultValue as CinemachineVirtualCameraBase;
        //  vCamera.Follow = followTarget;
    }

    public void SetCinemachineClipLookAt(string trackName, string findArg, Transform lookTarget, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<CinemachineShot>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                var camera = clip.VirtualCamera.Resolve(this.PlayableDirector);
                camera.LookAt = lookTarget;
            }
        }
    }

    public void SetCinemachineClipFov(string trackName, string findArg, float fov, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<CinemachineShot>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                var camera = clip.VirtualCamera.Resolve(this.PlayableDirector);
                var vc = camera as CinemachineVirtualCamera;
                if (vc != null)
                {
                    var len = vc.m_Lens;
                    len.FieldOfView = fov;
                    vc.m_Lens = len;
                }
            }
        }
    }

    public void SetCinemachineClips(string trackName, string findArg, CinemachineVirtualCameraBase virtualCamera, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<CinemachineShot>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                clip.VirtualCamera.defaultValue = virtualCamera;
            }
        }
    }


    public void SetAnimationClip(string trackName, string findArg, AnimationClip animationClip, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        GetClipAsset<AnimationPlayableAsset>(trackName, findArg, findType).clip = animationClip;
    }

    public void SetAnimationClips(string trackName, string findArg, AnimationClip animationClip, Enum_FindType findType = Enum_FindType.NameEqual)
    {
        using (ListPool<AnimationPlayableAsset>.Get(out var clips))
        {
            GetClipsAssets(clips, trackName, findArg, findType);
            foreach (var clip in clips)
            {
                clip.clip = animationClip;
            }
        }
    }

    public void ActiveFirstCamera(string trackName)
    {
        var shot = GetFirstAssets<CinemachineShot>(trackName, out var endTime);
        if (shot != null)
        {
            var firstCamera = shot.VirtualCamera.Resolve(this.PlayableDirector);
            if (firstCamera)
            {
                _firstCameraEndTime = endTime;
                _firstCamera = firstCamera;
                firstCamera.gameObject.SetActive(true);
            }
            else
            {
                _firstCameraEndTime = null;
                _firstCamera = null;
                Debug.LogError($"{this.name} 的相机轨道第一个相机为空,这种设计大概率镜头会错乱");
            }
        }
    }

    public void Pause()
    {
        PlayableDirector.Pause(); 
    }

    public void Resume()
    {
        PlayableDirector.Resume();
    }

    public void SetSpeed(float speed)
    {
        isLockTimeScale = speed <= 0;
        PlayableDirector.playableGraph.GetRootPlayable(0).SetSpeed(speed);
    }

    [ContextMenu("GoEnd")]
    public void GoEnd()
    {
        PlayableDirector.time = PlayableDirector.duration;
        PlayableDirector.Evaluate();
    }
/*
    public void EnableUICameraPostProcess()
    {
        RenderPipelineSetting.SetUICameraPostProcess(true,this.GetInstanceID(), OnUIPostProcessEnable);
    }

    public void DisableUICameraPostProcess()
    {
        RenderPipelineSetting.SetUICameraPostProcess(false, this.GetInstanceID(), OnPostProcessDisable);
    }
    */
    // Start is called before the first frame update

    public static Action OnUIPostProcessEnable;

    public static Action OnPostProcessDisable;


    public enum ManualPlayStatus
    { 
        NoBegin,
        Begin,
        Playing,
        Stoped,
    }
    private ManualPlayStatus _manualPlayStatus = ManualPlayStatus.NoBegin;

    private void Update()
    {
        if(!this._inited)
        {
            return;
        }

        if (this.PlayableDirector.timeUpdateMode == DirectorUpdateMode.Manual) 
        {
            if (_manualPlayStatus == ManualPlayStatus.Begin)
            {
                _OnPlayed(this.PlayableDirector);
                this.PlayableDirector.time = 0;
                this.PlayableDirector.Evaluate();
                _manualPlayStatus = ManualPlayStatus.Playing;
            }
            else if (_manualPlayStatus == ManualPlayStatus.Playing)
            {
                var currentTime = this.PlayableDirector.time;
                currentTime += Time.deltaTime;

                if (currentTime >= this.PlayableDirector.duration)
                {
                    currentTime = this.PlayableDirector.duration;
                    this.PlayableDirector.time= currentTime;
                    this.PlayableDirector.Evaluate();
                    if (this.PlayableDirector.extrapolationMode == DirectorWrapMode.Loop)
                    {
                        _manualPlayStatus = ManualPlayStatus.Begin;
                    }
                    else 
                    {
                        _OnStopped(PlayableDirector);

                        _manualPlayStatus = ManualPlayStatus.Stoped;
                    }
                }
                else 
                {
                    this.PlayableDirector.time = currentTime;
                    this.PlayableDirector.Evaluate();
                }
            }
        }
        if (_firstCameraEndTime.HasValue && PlayableDirector.time > _firstCameraEndTime.Value)
        {
            if (_firstCamera)
            {
                if (_bindedCameras != null && _bindedCameras.Contains(_firstCamera))
                {
                    //绑定的相机不管
                }
                else
                {
                    _firstCamera.gameObject.SetActive(false);
                }
            }

            _firstCameraEndTime = null;
        }

        var deltaTime = Time.unscaledDeltaTime;
        if (PlayableDirector.timeUpdateMode != DirectorUpdateMode.UnscaledGameTime)
        {
            deltaTime = Time.deltaTime;
        }

        if (PlayableDirector.time + deltaTime >= PlayableDirector.duration)
        {
            //finish here
           // m_LuaScript?.CallSelf(LuaTimelineFx_FunctionID.OnCompleted);
        }
    }


    public static void SetTimeScale(float timeScale) 
    { 
        if(!isLockTimeScale)
        {
            Time.timeScale = timeScale;
        }
    }
    
    public Animator GetAnimator(string trackName)
    {
        var track = GetBinding<AnimationTrack>(trackName); 
        if(track != null)
        {
            // 获取该Track绑定的Animator
            return PlayableDirector.GetGenericBinding(track) as Animator;
        }
        return null;
    }
}