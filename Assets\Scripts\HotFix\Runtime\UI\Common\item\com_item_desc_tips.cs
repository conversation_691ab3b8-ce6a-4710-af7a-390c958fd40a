using ELEX.Config;
using Game.Common;
using Game.Module;


namespace Game.View
{
    public partial class com_power_tips3 : ViewBase
    {
        public override void OnAwake(int pannel = -1, string name = "")
        {
            base.OnAwake(pannel, name);
            SetActive(uiObjs.pointing_top, false);
            SetActive(uiObjs.pointing_bottom, false);
        }
    }
    /// <summary>
    /// 通用道具说明tips
    /// </summary>
    public class com_item_desc_tips : com_power_tips3
    {
        public class ItemDescData
        {
            public string title;
            public IdNumType data;
        }

        public override void OnShow(object parameters)
        {
            base.OnShow(parameters);
            var itemData = parameters as ItemDescData;
            if (itemData.data != null)
            {
                var itemConf = ConfHelper.GetConfItem(itemData.data.id);
                if (itemConf != null)
                {
                    SetTextValueTMP(uiObjs.name, Localization.Get("building_barrack_product_007"));
                    SetTextValueTMP(uiObjs.desc, Localization.Get(itemConf.desc));
                }
            }
            SetTextValueTMP(uiObjs.num, itemData.data.num.ToString("N0"));
            SetTextValueTMP(uiObjs.title, itemData.title);
            
            ((ViewBase)Parent).RefreshLayoutImmediate();
        }

    }

}
