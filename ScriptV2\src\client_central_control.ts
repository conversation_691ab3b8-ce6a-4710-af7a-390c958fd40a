import crypto from 'crypto';

// Updated ClientBaseConfig interface to include new fields
interface ClientBaseConfig {
    gateWayHost: string;
    gateWayTcpPort: number;
    gateWayWebSocketPort?: number;
    gateWaySSL: boolean;
    versionCustomStr?: string;
    directGateWayHost?: string[];
    recommendAppVersion: string;
    appDownloadUrl: string;
    resDownloadUrl: string;
    confDownloadUrl: string;
    protoDownloadUrl: string;
    resVersion: string;
    confVersion: string;
    protoVersion: string;
    appCustomStr?: string;
    updateStrategy?: number;  // New field added
}

// New version range override structure
interface AppVersionRangeOverride {
    begin?: string;
    end?: string;
    overRideData: Partial<ClientBaseConfig>;
}

// Environment-specific override
interface EnvOverride {
    evn: string;
    overRideData: Partial<ClientBaseConfig>;
}

// Query response structure with added fields
interface QueryResponseData {
    code: number;
    msg: null;
    data: {
        default: ClientBaseConfig;
        overRides_version: Array<{
            appVersion: string;
            overRideData: Partial<ClientBaseConfig>;
        }>;
        overRides_channel_platform?: Array<{
            platform: string;
            channel?: string[];
            overRideData: Partial<ClientBaseConfig>;
        }>;
        overRides_sid?: Array<{
            serverIds: number[];
            overRideData: Partial<ClientBaseConfig>;
        }>;
        overRides_evn?: Array<EnvOverride>;
        overRides_appVersionRange?: Array<AppVersionRangeOverride>; // New field added
    };
    ok: boolean;
}

// Update response structure
interface UpdateResponseData {
    code: number;
    msg: null;
    data: null;
    ok: boolean;
}

class ClientCentralControlManager {
    private baseUrl: string;
    private opsKey: string;
    private projectId: string;

    constructor(baseUrl: string, opsKey: string, projectId: string) {
        this.baseUrl = baseUrl;
        this.opsKey = opsKey;
        this.projectId = projectId;
    }

    private generateSign(timestamp: string): string {
        const signStr = `${timestamp}${this.opsKey}`;
        return crypto.createHash('md5')
            .update(signStr)
            .digest('hex')
            .toLowerCase();
    }

    private getTimestamp(): string {
        return Date.now().toString();
    }

    // Query configuration
    async queryConfig(): Promise<QueryResponseData | null> {
        try {
            const timestamp = this.getTimestamp();
            const sign = this.generateSign(timestamp);
            const url = `${this.baseUrl}/api/admin/v2/bizModelConfig/versionControl/list/${this.projectId}/VERSION_CONTROL_V2`;
            console.log('Query Request URL:', url);

            const requestBody = {
                time: timestamp,
                sign: sign,
                data: ""  // The query interface does not require data
            };

            console.log('Query Request Body:', JSON.stringify(requestBody, null, 2));
        
            const response = await fetch(
                url,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const responseData = await response.json();
            // console.log('Query Response:\n'+responseData); // 打印返回的数据
            return responseData as QueryResponseData;
        } catch (error) {
            console.error('Failed to query config:', error);
            return null;
        }
    }

    // Update environment configuration
    async updateEnvConfig(envName: string, overrideData: Partial<ClientBaseConfig>): Promise<boolean> {
        try {
            const timestamp = this.getTimestamp();
            const sign = this.generateSign(timestamp);
            const url = `${this.baseUrl}/api/admin/v2/bizModelConfig/versionControl/update/${this.projectId}`;
            console.log('Update Request URL:', url);
            const envOverride: EnvOverride = {
                evn: envName,
                overRideData: overrideData
            };

            const requestBody = {
                time: timestamp,
                sign: sign,
                data: JSON.stringify({
                    overRides_evn: [envOverride]
                })
            };

            console.log('Update Request Body:', JSON.stringify(requestBody, null, 2));

            const response = await fetch(
                url,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseJson = await response.json();
            console.log('Update Response:\n'+responseJson);
            const result = responseJson as UpdateResponseData;
            return result && result.ok && result.code === 0;

        } catch (error) {
            console.error('Failed to update environment config:', error);
            return false;
        }
    }
}

// Example usage
async function example() {
    const configManager = new ClientCentralControlManager(
        'http://test-tech-web.elex-tech.net',
        '123456',
        '10000005'
    );

    // Query configuration
    console.log('Querying config...');
    const queryResult = await configManager.queryConfig();
    console.log('Query result:', JSON.stringify(queryResult, null, 2));

    // Update configuration
    console.log('Updating config...');
    const updateData: Partial<ClientBaseConfig> = {
        gateWayHost: '***********',
        resVersion: '2.1.1',
        confVersion: '2.1.1',
        protoVersion: '2.1.1',
        appCustomStr: 'test_environment'
    };

    const success = await configManager.updateEnvConfig(
        'dev_pve',
        updateData
    );

    console.log('Update result:', success ? 'Success' : 'Failed');
}

// Exporting the modules
export {
    ClientCentralControlManager,
    ClientBaseConfig,
    EnvOverride,
    QueryResponseData,
    UpdateResponseData
};

// Uncomment the following if testing is required
// example().catch(console.error);
