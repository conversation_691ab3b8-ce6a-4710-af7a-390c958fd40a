/**
 * GitCdnManager
 *
 * CDN 资源版本管理工具，支持多环境隔离和 Git 版本控制。
 *
 * todo Git 配置文件为指定远程仓库设置专属密钥路径。
 * ~/.ssh/config
 * Host github.com
 *     HostName github.com
 *     User git
 *     IdentityFile C:/path/to/your/private_key
 *     IdentitiesOnly yes
 */

import fs from 'fs-extra';

import path from 'path';

import crc32 from 'crc-32';

import {$} from 'zx';

import {
    getFormatTime,
    getFormatTimeShow, humanReadableSize,
    isInvalidStr,
    logFunctionEnd,
    logFunctionStart,
    logWithKey
} from "./utility.ts";
import {BuildParam} from "./build_params.ts";
import simpleGit, {CleanOptions, ResetMode, SimpleGit} from "simple-git";
import FastGlob from "zx/build/vendor-extra";
import {BUILD_TYPE, BUILD_UNITY_TIMER, CDN_VERSION_PREFIX, PLATFORM} from "./const.ts";


export interface FileInfo {
    /** 文件名 */
    name: string;
    /** 文件大小(字节) */
    size: number;
    /** CRC32校验值 */
    crc32: number;
    /** 相对路径 */
    relativePath: string;
}

export interface VersionInfo {
    /** 版本号 */
    version: string;
    /** 资源前缀 */
    prefix: string;
    /** 文件列表 */
    files: FileInfo[];
    /** 可选的子目录 */
    targetSubDir?: string;
}

export interface ManagerOptions {
    /** 是否自动提交本地Git */
    gitEnable?: boolean;
    /** Git分支名 */
    gitBranch?: string;
    /** 环境名(必填) */
    env?: string;
    /** 平台(必填) */
    platform: string;
    /** 严格安全模式 */
    safeModel?: boolean;
    //cdn的下载地址
    cdnRootUrl?: string;
}

export interface RootFileInfo extends FileInfo {
    /** 标记为根目录文件 */
    isRootFile: true;
}

/**
 * git以及资源cdn管理
 */
export class GitCdnManager {
    // CDN根目录
    private cdnRootDirectory: string;
    //gitres路径
    private gitResDirectory?: string;

    private versionMap: Map<string, VersionInfo>;
    private options: Partial<ManagerOptions>;
    private simpleGit? : SimpleGit;
    /**
     *
     * @constructor
     */
    get CdnResDirectory(): string {
        const cdnResDirectory = path.join(this.CdnRootDirectory, this.CdnRelativePath);
        return cdnResDirectory;
    }

    /**
     * cdnBasePath
     * @constructor
     */
    get CdnRootDirectory(): string {
        return this.cdnRootDirectory;
    }

    get CdnRelativePath(): string {
        const relativePath  = path.join(this.options.platform!, isInvalidStr(this.options.env) ? '' : `${this.options.env}`);
        return relativePath;
    }

    constructor(cdnRootDirectory: string, gitResDirectory: string, options: ManagerOptions) {
        this.cdnRootDirectory = cdnRootDirectory;
        this.gitResDirectory = gitResDirectory;
        this.versionMap = new Map();
        this.options = {
            gitBranch: 'main',
            gitEnable :false,
            ...options
        };
        console.log(`GitCdnManager,options:${JSON.stringify(options)},cdnResDirectory:${this.CdnResDirectory},gitResDirectory:${this.gitResDirectory}`);
        fs.ensureDirSync(this.CdnResDirectory);
        if(options.gitEnable){
            fs.ensureDirSync(this.gitResDirectory);
            this.simpleGit = simpleGit({ baseDir: this.gitResDirectory, binary: 'git' });
        }
    }

    /**
     * 显示版本映射表的所有信息
     * @param detailed 是否显示详细文件信息
     */
    async showVersionMap(detailed: boolean = false) {
        console.log('\n=== Version Map Information ===');
        console.log(`Total Versions Count: ${this.versionMap.size}`);
        console.log('env:', this.options.env);
        console.log('platform', this.options.platform);
        console.log('cdnResDirectory:', this.CdnResDirectory);
        console.log('----------------------------------------');

        // 按前缀分组
        const groupedVersions: { [prefix: string]: VersionInfo[] } = {};
        for (const [key, info] of this.versionMap.entries()) {
            if (!groupedVersions[info.prefix]) {
                groupedVersions[info.prefix] = [];
            }
            groupedVersions[info.prefix].push(info);
        }

        // 遍历每个前缀组
        for (const [prefix, versions] of Object.entries(groupedVersions)) {
            console.log(`\n[Prefix: ${prefix}] (${versions.length} versions)`);

            // 按版本号排序
            versions.sort((a, b) => {
                const verA = parseInt(a.version.replace(/\D/g, ''));
                const verB = parseInt(b.version.replace(/\D/g, ''));
                return verB - verA; // 降序排列
            });

            // 显示每个版本的信息
            versions.forEach((versionInfo, index) => {
                console.log(`\n  ${index + 1}. Version: ${versionInfo.version}`);
                console.log(`     Total Files: ${versionInfo.files.length}`);
                if (versionInfo.targetSubDir) {
                    console.log(`     Target SubDir: ${versionInfo.targetSubDir}`);
                }

                // 如果需要显示详细信息
                if (detailed) {
                    console.log('     Files:');
                    versionInfo.files.forEach(file => {
                        console.log(`       - ${file.relativePath}`);
                        console.log(`         Size: ${this.formatFileSize(file.size)}`);
                        console.log(`         CRC32: ${file.crc32}`);
                        if ('isRootFile' in file) {
                            console.log('         Type: Root File');
                        }
                    });
                } else {
                    // 简略信息只显示文件数量统计
                    const totalSize = versionInfo.files.reduce((sum, file) => sum + file.size, 0);
                    console.log(`     Total Size: ${this.formatFileSize(totalSize)}`);
                }
            });
        }
        console.log('\n----------------------------------------\n');
    }

    /**
     * 格式化文件大小
     * @param bytes 字节数
     * @returns 格式化后的大小字符串
     */
    private formatFileSize(bytes: number): string {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    /**
     * 将当前的打包文件保存到git
     * @constructor
     */
    public async saveBuildToGit(buildParam: BuildParam) {
        logFunctionStart('SaveBuild2Git')
        await this.cleanAndPullGit()
        //todo 测试代码
        // const filePath = this.gitResDirectory+`/aa_${getFormatTime()}.txt`
        // // 确定文件目录是否存在
        // const dir = path.dirname(filePath);
        // if (!fs.existsSync(dir)) {
        //     await fs.promises.mkdir(dir, { recursive: true }); // 异步递归创建目录
        // }
        // const content = '1123'
        // // 创建文件并写入内容
        // await fs.promises.writeFile(filePath, content, 'utf8'); // utf8 编码
        // console.log(`File created successfully: ${filePath}`);

        //获取本地需要保存到git中的文件
        const allfiles:string[] = []
        const buildType =buildParam.getParams().buildType
        if(buildType == BUILD_TYPE.BUILD_ALL || buildType == BUILD_TYPE.BUILD_RES) {
            const version = buildParam.getState().resVersion;
            const prefix = `${buildParam.getParams().platform}_${CDN_VERSION_PREFIX.BUILD_RES}`
            const files = await this.getAllFilesInCurrentVersionWithPrefix(version,[prefix],true)
            allfiles.push(...files);
        }
        if(buildType == BUILD_TYPE.BUILD_ALL || buildType == BUILD_TYPE.BUILD_CONFIG) {
            const version = buildParam.getState().cfgVersion;
            const prefix = `${buildParam.getParams().platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`
            const files = await this.getAllFilesInCurrentVersionWithPrefix(version,[prefix],true)
            allfiles.push(...files);
        }

        //拷贝到指定的目录
        try {
            for (const file of allfiles) {
                const sourcePath = file;
                const relativePath =  sourcePath.substring(this.CdnRootDirectory.length + 1).replace(/\\/g, '/');
                const targetPath = path.join(this.gitResDirectory!, relativePath);

                if (fs.existsSync(targetPath)) {
                    //check crc32
                    let content = fs.readFileSync(targetPath);
                    const targetCrc32 = crc32.buf(content);
                    content = fs.readFileSync(sourcePath);
                    const sourceCrc32 = crc32.buf(content);
                    if(sourceCrc32 !== targetCrc32) {
                        logWithKey('saveBuildToGit',`sourcePath:${sourcePath} sourceCrc32:${sourceCrc32} != targetPath:${targetPath} targetCrc32:${targetCrc32}`);
                    }else{
                        continue;
                    }
                }
                fs.mkdirpSync(path.dirname(targetPath));
                fs.copyFileSync(sourcePath, targetPath);
                logWithKey('saveBuildToGit',`copy file,sourcePath:${sourcePath} targetPath:${targetPath}`);
            }
        } catch (error) {
            throw new Error(`Failed to copy files: ${error}`);
        }
        const resVersion = buildParam.getState().resVersion;
        const cfgVersion = buildParam.getState().cfgVersion;
        const appVersion = buildParam.getState().appVersion;
        const message =`SaveBuild => [appVersion:${appVersion}] [cfgVersion:${cfgVersion}] [resVersion:${resVersion}]`
        await this.commitAndPush(message)
        logFunctionEnd('SaveBuild2Git')
    }


    /**
     * 刷新仓库
     */
    private async cleanAndPullGit() {
        logFunctionStart('CleanAndPullGit')
        try {
            if(!this.options.gitEnable){
                logWithKey('CleanAndPullGit',`gitEnable:${this.options.gitEnable}`)
                return;
            }
            // //注意，windows平台需要指定一下bash
            // $.shell = 'C:\\Program Files\\Git\\bin\\bash.exe';
            // await $`cd "${this.gitResDirectory}" && git reset --hard`;
            // await $`cd ${this.gitResDirectory} && git clean -fd`;
            // await $`cd ${this.gitResDirectory} && git fetch`;
            // await $`cd ${this.gitResDirectory} && git checkout  ${this.options.gitBranch}`;
            // await $`cd ${this.gitResDirectory} && git pull origin ${this.options.gitBranch}`;
            //这里使用simple git插件

            await this.simpleGit?.reset(ResetMode.HARD)
            await this.simpleGit?.clean(CleanOptions.FORCE)
            await this.simpleGit?.fetch()

            const status = await this.simpleGit?.status();
            console.log('Current branch:', status?.current);

            if (status?.current === this.options.gitBranch!) {
                console.log(`Already on target branch: ${this.options.gitBranch!}`);
                return;
            }

            // 检查本地分支
            const branches = await this.simpleGit?.branchLocal();
            console.log('Local branches:', branches?.all);

            if (branches?.all.includes(this.options.gitBranch!)) {
                // 本地存在目标分支
                console.log(`Switching to existing local branch: ${this.options.gitBranch!}`);
                await this.simpleGit?.checkout(this.options.gitBranch!);
                return;
            }

            // 检查远程分支
            const remoteBranches = await this.simpleGit?.branch(['-r']);
            console.log('Remote branches:', remoteBranches?.all);

            const remoteBranchName = `origin/${this.options.gitBranch!}`;
            if (remoteBranches?.all.includes(remoteBranchName)) {
                // 本地不存在、但远程存在目标分支
                console.log(`Remote branch ${remoteBranchName} found, creating and switching.`);
                await this.simpleGit?.checkoutBranch(this.options.gitBranch!, remoteBranchName); // 拉取远程分支到本地并切换
                return;
            }

            // 如果本地和远程都没有目标分支
            console.log(`Branch ${this.options.gitBranch!} does not exist locally or remotely, creating new branch.`);
            // await this.simpleGit?.checkoutLocalBranch(this.options.gitBranch!); // 创建本地分支并切换
            // 创建一个空的 orphan（孤立分支）
            await this.simpleGit?.raw(['checkout', '--orphan', this.options.gitBranch!]);
            // 清空工作区内容
            // await this.simpleGit?.raw(['rm', '-rf', '.']);

        } catch (error) {
            throw new Error(`cleanAndPullGit,${error}`);
        }
        logFunctionEnd('CleanAndPullGit')
    }

    /**
     * 提交资源到仓库
     * @param message
     */
    private async commitAndPush(message?: string) {
        logFunctionStart('CommitAndPush')
        try {
            const status = await this.simpleGit?.status();
            logWithKey('CommitAndPush',`status:${status}`)
            // if (status?.isClean()) {
            //     console.log('Nothing to commit or push.');
            //     return;
            // }
            // $.env.GIT_SSH_COMMAND = 'ssh -F C:/Users/<USER>/.ssh/config';
            await  this.simpleGit?.add('.')
            await  this.simpleGit?.commit(message ?? getFormatTime())

            const currentBranch = (await this.simpleGit?.branchLocal())?.current;
            const remoteName = `origin/${currentBranch!}`;
            console.log(`currentBranch： ${currentBranch} remoteName：${remoteName}`);
            const branchSummary = await this.simpleGit?.branch(['-vv']); // 获取分支详细信息
            const branchInfo = branchSummary?.branches[currentBranch!];
            console.log(branchInfo!)

            const isTrackingRemote = branchInfo && branchInfo?.label.includes(`${remoteName}`);
            console.log(`isTrackingRemote:${isTrackingRemote}`)

            if (!isTrackingRemote) {
                // 强制设置 Git 使用特定的 SSH 配置
                // $.env.GIT_SSH_COMMAND = 'ssh -i ~/.ssh/elex-pub';
                console.log(`Branch "${currentBranch}" is not tracking any remote branch. Establishing upstream.`);
                // 如果没有关联，设置与远程分支的上游联系并推送
                // 设置上游分支
                 await this.simpleGit?.addConfig(`branch.${currentBranch}.remote`, `origin`);
                 await this.simpleGit?.addConfig(`branch.${currentBranch}.merge`, `refs/heads/${currentBranch}`);
            }
            await  this.simpleGit?.push()
        } catch (error) {
            throw new Error(`commitAndPush,Git commit/push failed: ${error}`);
        }
        logFunctionEnd('CommitAndPush')
    }

    /**
     * 加载当前环境下已经存在的版本号数据
     */
    public async loadExistingVersions() {
        logFunctionStart('loadExistingVersions', `cdnResDirectory: ${this.CdnResDirectory}`);
        const files = fs.readdirSync(this.CdnResDirectory);
        this.versionMap.clear();

        const errors: string[] = [];

        for (const file of files) {
            if (!file.endsWith('.json')) continue;
            //匹配前缀+版本号的json文件
            const match = file.match(/^[a-zA-Z0-9_-]+_\d+\.\d+\.\d+\.json$/);
            if (!match) {
                //todo 兼容老版本的配置
                const match2 = file.match(/^([a-zA-Z0-9_\-]+)\.(\d+)\.json$/);
                if (!match2) {
                    continue
                }
                // continue
            }
            ;

            // const [_, prefix, version] = match;
            const filePath = path.join(this.CdnResDirectory, file);
            logWithKey(`loadExistingVersions`, `find exist version file,filePath:${filePath}`);
            try {
                const versionContent = fs.readJsonSync(filePath) as VersionInfo;
                if (versionContent == null) {
                    errors.push(`Version file not valid VersionInfo format: ${versionContent}`);
                    continue;
                }
                // console.log(`loadExistingVersions,content:${content}`);
                if (!versionContent.version || !versionContent.prefix || !Array.isArray(versionContent.files)) {
                    errors.push(`Invalid JSON structure in ${file}`);
                    continue;
                }

                for (const fileInfo of versionContent.files) {
                    if (!fileInfo.name || !fileInfo.relativePath ||
                        typeof fileInfo.size !== 'number' ||
                        typeof fileInfo.crc32 !== 'number') {
                        errors.push(`Invalid file info in ${file}: ${JSON.stringify(fileInfo)}`);
                        continue;
                    }
                }

                this.versionMap.set(`${versionContent.prefix}_${versionContent.version}`, versionContent);

            } catch (error) {
                errors.push(`Error processing ${file}: ${error}`);
            }
        }

        if (errors.length > 0) {
            throw new Error('Integrity check failed during version loading:\n' + errors.join('\n'));
        }

        // for(let [key, value] of this.versionMap){
        //     console.log(`versionMap:key:${key}, value: ${JSON.stringify(value)}`);
        // }
        logFunctionEnd(`loadExistingVersions`);
    }

    /**
     * 当前版本所有的文件
     * @deprecated 不要使用这个方法
     * @param version
     * @param filterPrefixs 过滤掉的前缀
     */
    public async getAllFilesInCurrentVersion(version: string, filterPrefixs: string[]): Promise<string[]> {
        console.log(`getAllFilesInCurrentVersion,version:${version}`);
        if (this.versionMap == null || this.versionMap.size === 0) {
            await this.loadExistingVersions();
        }
        let allfiles: string[] = [];
        for (let [key, value] of this.versionMap) {
            if (value.version === version) {
                console.log(`matched version:${key}`);
                if (filterPrefixs != null && filterPrefixs.indexOf(value.prefix) >= 0) {
                    continue;
                }
                let versionfilepath = path.join(this.CdnResDirectory, key + ".json");
                console.log(`versionfilepath:${versionfilepath}`);
                allfiles.push(versionfilepath);
                let fileParentDirectory = this.CdnResDirectory;
                if (value.targetSubDir != null)
                    fileParentDirectory = path.join(fileParentDirectory, value.targetSubDir);
                if (value.files != null) {
                    for (let fileInfo of value.files) {
                        // fileInfo.relativePath = path.join(this.parentDirectory, fileInfo.relativePath);
                        let filefullpath = path.join(fileParentDirectory, fileInfo.relativePath);
                        allfiles.push(filefullpath);
                    }
                }
            }
        }
        return allfiles;
    }

    /**
     * 获取指定版本号的文件列表，指定前缀
     * @param version
     * @param onlyPrefix
     */
    public async getAllFilesInCurrentVersionWithPrefix(version: string, onlyPrefix: string[],includeVersionFile:boolean = false): Promise<string[]> {
        logFunctionStart('getAllFilesInCurrentVersionWithPrefix', `version${version},prefixs:${onlyPrefix?.join(',')}`);
        if (this.versionMap == null || this.versionMap.size === 0) {
            await this.loadExistingVersions();
        }
        let allfiles: string[] = [];
        let matchVersionAndPrefix = false;
        for (let [key, value] of this.versionMap) {
            if (value.version === version) {
                if (onlyPrefix != null && onlyPrefix.indexOf(value.prefix) < 0) {
                    continue;
                }
                matchVersionAndPrefix = true;
                logWithKey('getAllFilesInCurrentVersionWithPrefix', `matched version:${key}`);
                const versionfile  = path.join(this.CdnResDirectory, key + ".json");
                if(includeVersionFile)
                    allfiles.push(versionfile);
                let fileParentDirectory = this.CdnResDirectory;
                if (value.targetSubDir != null)
                    fileParentDirectory = path.join(fileParentDirectory, value.targetSubDir);
                if (value.files != null) {
                    for (let fileInfo of value.files) {
                        // fileInfo.relativePath = path.join(this.parentDirectory, fileInfo.relativePath);
                        let filefullpath = path.join(fileParentDirectory, fileInfo.relativePath);
                        allfiles.push(filefullpath);
                    }
                }
            }
        }
        if (!matchVersionAndPrefix) {
            logWithKey('getAllFilesInCurrentVersionWithPrefix', `not find version${version},prefixs:${onlyPrefix.join(',')}`);
        }
        return allfiles;
    }

    /**
     * 校验版本资源完整性
     * @param prefix
     * @param version
     * @private
     */
    private async checkVersionFileIntegrity(prefix: string, version: string): Promise<string[]> {
        const errors: string[] = [];
        const versionInfo = this.versionMap.get(`${prefix}_${version}`);

        if (!versionInfo) {
            errors.push(`Version file ${prefix}_${version}.json not found in version map`);
            return errors;
        }

        // 考虑 targetSubDir
        const baseDir = versionInfo.targetSubDir
            ? path.join(this.CdnResDirectory, versionInfo.targetSubDir)
            : this.CdnResDirectory;

        const targetDir = baseDir;//path.join(baseDir, prefix);

        for (const file of versionInfo.files) {
            const filePath = path.join(targetDir, file.relativePath);

            if (!fs.existsSync(filePath)) {
                errors.push(`File not found: ${file.relativePath}`);
                continue;
            }

            try {
                const stats = fs.statSync(filePath);
                const content = fs.readFileSync(filePath);
                const actualCrc32 = crc32.buf(content);

                if (stats.size !== file.size) {
                    errors.push(`Size mismatch for ${file.relativePath}: expected ${file.size}, got ${stats.size}`);
                }

                if (actualCrc32 !== file.crc32) {
                    errors.push(`CRC32 mismatch for ${file.relativePath}: expected ${file.crc32}, got ${actualCrc32}`);
                }
            } catch (error) {
                errors.push(`Error checking file ${file.relativePath}: ${error}`);
            }
        }

        return errors;
    }

    /**
     * 当前路径下的所有文件
     * @param dirPath
     * @param excludePatterns
     * @private
     */
    private scanDirectory(dirPath: string, excludePatterns?: string[]): FileInfo[] {
        const results: FileInfo[] = [];
        const excludeRegexes = excludePatterns
            ? excludePatterns.map(pattern => new RegExp(pattern))
            : null;

        const scanRecursive = (currentPath: string, basePath: string) => {
            const entries = fs.readdirSync(currentPath);

            for (const entry of entries) {
                if (excludeRegexes && excludeRegexes.some(regex => regex.test(entry))) {
                    continue;
                }

                const fullPath = path.join(currentPath, entry);
                const stats = fs.statSync(fullPath);

                if (stats.isDirectory()) {
                    scanRecursive(fullPath, basePath);
                } else {
                    const relativePath = path.relative(basePath, fullPath);
                    const content = fs.readFileSync(fullPath);

                    results.push({
                        name: path.basename(entry),
                        size: stats.size,
                        crc32: crc32.buf(content),
                        relativePath
                    });
                }
            }
        };

        scanRecursive(dirPath, dirPath);
        return results;
    }

    /**
     * 判断版本号的格式是否正确
     * @param version
     * @private
     */
    private isVersionFormatValid(version: string): boolean {
        //todo 这里暂时屏蔽版本号检测。等待打包配置合并代码完成
        return true;
        // 支持格式：
        // x.y.z (基础版本号)
        const pattern = /^\d+\.\d+\.\d+$/;
        return pattern.test(version);
    }

    /**
     * 将资源拷贝到cdn指定目录
     * @param sourceDir
     * @param files
     * @param prefix
     * @param targetSubDir
     * @private
     */
    private copyFiles(sourceDir: string, files: FileInfo[], prefix: string, targetSubDir?: string) {
        // 构建基础目录路径
        const baseDir = targetSubDir
            ? path.join(this.CdnResDirectory, targetSubDir)
            : this.CdnResDirectory;

        const targetDir = baseDir;//path.join(baseDir, prefix);

        // 第一步：检查所有目标文件
        for (const file of files) {
            const targetPath = path.join(targetDir, file.relativePath);

            if (fs.existsSync(targetPath)) {
                const content = fs.readFileSync(targetPath);
                const targetCrc32 = crc32.buf(content);

                if (targetCrc32 !== file.crc32) {
                    throw new Error(
                        `Safety check failed: File ${file.relativePath} exists with different content (CRC32 mismatch)`
                    );
                }
            }
        }

        // 第二步：所有检查通过后，执行复制操作
        try {
            for (const file of files) {
                const sourcePath = path.join(sourceDir, file.relativePath);
                const targetPath = path.join(targetDir, file.relativePath);

                if (fs.existsSync(targetPath)) {
                    continue;
                }

                fs.mkdirpSync(path.dirname(targetPath));
                fs.copyFileSync(sourcePath, targetPath);
            }
        } catch (error) {
            throw new Error(`Failed to copy files: ${error}`);
        }
    }

    /**
     * 将资源目录添加到cdn指定版本号下
     * @param localDirectory
     * @param version
     * @param prefix
     * @param excludePatterns
     * @param replaceIfExists
     * @param targetSubDir
     */
    async addDirectoryVersion(
        localDirectory: string,
        version: string,
        prefix: string = 'invalid_prefix',
        excludePatterns: string[],
         replaceIfExists: boolean = false,
        targetSubDir?: string
    ) {
        if (!this.isVersionFormatValid(version)) {
            throw new Error('Invalid version format. Should be a.b.c');
        }
        const versionKey = `${prefix}_${version}`;
        const existingVersion = this.versionMap.get(versionKey);

        if (existingVersion) {
            if (!replaceIfExists && this.options.safeModel) {
                throw new Error(`Version ${version} for prefix ${prefix} already exists.`);
                return;
            }

            await this.removeVersion(version, prefix);
        }

        const files = this.scanDirectory(localDirectory, excludePatterns);
        const versionFileName = `${versionKey}.json`;
        try {
            this.copyFiles(localDirectory, files, prefix, targetSubDir);
            const versionValue = {
                version,
                prefix,
                files,
                targetSubDir
            }
            fs.writeJsonSync(
                path.join(this.CdnResDirectory, versionFileName),
                versionValue,
                {spaces: 2}
            );
            this.versionMap.set(versionKey, versionValue);
        } catch (error) {
            // 清理操作
            fs.removeSync(path.join(this.CdnResDirectory, versionFileName));
            throw new Error(`Version operation failed: ${error}`);
        }
    }

    /**
     * 添加单个文件到cdn指定版本号下
     * @param filePath
     * @param version
     * @param prefix
     * @param replaceIfExists
     * @param targetSubDir
     */
    async addFileVersion(
        filePath: string,
        version: string,
        prefix: string = 'invalid_prefix',
        replaceIfExists: boolean = false,
        targetSubDir?: string
    ) {
        if (!this.isVersionFormatValid(version)) {
            throw new Error('Invalid version format. Should be a.b.c');
        }

        const fileName = path.basename(filePath);
        const versionKey = `${prefix}_${version}`;
        const existingVersion = this.versionMap.get(versionKey);

        if (existingVersion) {
            if (!replaceIfExists && this.options.safeModel) {
                throw new Error(`version ${version} with prefix ${prefix} already exists.`);
                return;
            }

            await this.removeVersion(version, prefix);
        }
        const versionFileName = `${prefix}_${version}.json`;
        try {
            const stats = fs.statSync(filePath);
            const content = fs.readFileSync(filePath);

            const fileInfo: RootFileInfo = {
                name: fileName,
                size: stats.size,
                crc32: crc32.buf(content),
                relativePath: fileName,
                isRootFile: true
            };

            const baseDir = targetSubDir
                ? path.join(this.CdnResDirectory, targetSubDir)
                : this.CdnResDirectory;

            const targetPath = path.join(baseDir, fileName);
            //拷贝之前检查一下目标文件是否存在
            if (fs.existsSync(targetPath)) {
                const content = fs.readFileSync(targetPath);
                const targetCrc32 = crc32.buf(content);

                if (targetCrc32 !== fileInfo.crc32) {
                    throw new Error(
                        `Safety check failed: File ${targetPath} exists with different content (CRC32 mismatch)`
                    );
                }
            }

            if (!fs.existsSync(targetPath)) {
                fs.mkdirpSync(path.dirname(targetPath));
                fs.copyFileSync(filePath, targetPath);
            }

            const versionValue = {
                version,
                prefix,
                files: [fileInfo],
                targetSubDir
            }
            fs.writeJsonSync(
                path.join(this.CdnResDirectory, versionFileName),
                versionValue,
                {spaces: 2}
            );

            this.versionMap.set(versionKey, versionValue);


        } catch (error) {
            fs.removeSync(path.join(this.CdnResDirectory, versionFileName));
            throw new Error(`Root file version operation failed: ${error}`);
        }
    }

    /**
     * 移除某个版本,开发阶段使用。正式版本不要用
     * @param version
     * @param prefix
     */
    async removeVersion(version: string, prefix: string) {
        logFunctionStart('removeVersion', `${version},${prefix}`);
        const key = `${prefix}_${version}`;
        const versionInfo = this.versionMap.get(key);
        if (!versionInfo) return;

        fs.removeSync(path.join(this.CdnResDirectory, `${key}.json`));
        logWithKey('removeVersion', `remove version: ${key}.json`)
        const otherVersionUsedFiles = new Set<string>();
        for (const [otherKey, info] of this.versionMap.entries()) {
            if (otherKey !== key && info.prefix === prefix) {
                const baseDir = info.targetSubDir
                    ? path.join(this.CdnResDirectory, info.targetSubDir)
                    : this.CdnResDirectory;
                info.files.forEach(f => otherVersionUsedFiles.add(path.join(baseDir, f.relativePath)));
            }
        }

        const baseDir = versionInfo.targetSubDir
            ? path.join(this.CdnResDirectory, versionInfo.targetSubDir)
            : this.CdnResDirectory;

        versionInfo.files.forEach(file => {
            const filePath = path.join(baseDir, file.relativePath);
            if (!otherVersionUsedFiles.has(filePath)) {
                fs.removeSync(filePath);
                logWithKey('removeVersion', `remove file: ${filePath}`)
            }
        });
        this.versionMap.delete(key);
    }

    // async keepLatestVersions(prefix: string, count: number) {
    //     // 1. 筛选出指定前缀的所有版本
    //     await this.showVersionMap();
    //     const versions = [...this.versionMap.entries()]
    //         .filter(([key]) => key.startsWith(prefix + '_'))
    //         .map(([key, info]) => ({
    //             version: info.version,
    //             fullKey: key
    //         }))
    //         .sort((a, b) => {
    //             // 2. 优化版本号比较逻辑
    //             const compareVersion = (ver1: string, ver2: string) => {
    //                 const parts1 = ver1.split(/[.-_]/); // 支持 . - _ 分隔符
    //                 const parts2 = ver2.split(/[.-_]/);
    //
    //                 for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
    //                     const num1 = parseInt(parts1[i] || '0');
    //                     const num2 = parseInt(parts2[i] || '0');
    //                     console.log("num1   " + num1 + "num2 " + num2)
    //                     if (num1 !== num2) {
    //                         return num2 - num1; // 降序排列
    //                     }
    //                 }
    //                 return 0;
    //             };
    //             console.log("a.version " + a.version + "b version " + b.version)
    //             return compareVersion(a.version, b.version);
    //         });
    //
    //     // 3. 保留最新的 count 个版本，删除其余版本
    //     if (versions.length > count) {
    //         const versionsToRemove = versions.slice(count);
    //         for (const versionInfo of versionsToRemove) {
    //             //  await this.removeVersion(versionInfo.version, prefix);
    //         }
    //
    //         console.log(`Removed ${versionsToRemove.length} old versions for prefix ${prefix}`);
    //         console.log('Kept versions:', versions.slice(0, count).map(v => v.version));
    //         console.log('Removed versions:', versionsToRemove.map(v => v.version));
    //     } else {
    //         console.log(`No versions to remove. Current count (${versions.length}) is less than or equal to target count (${count})`);
    //     }
    // }

    async getVersions(prefix: string): Promise<string[]> {
        return [...this.versionMap.entries()]
            .filter(([key]) => key.startsWith(prefix + '_'))
            .map(([_, info]) => info.version)
            .sort()
            .reverse();
    }

    /**
     * 检测当前所有版本的完整性
     */
    async checkAllVersionsIntegrity(): Promise<boolean> {
        logFunctionStart('checkAllVersionsIntegrity');
        if (this.versionMap == null || this.versionMap.size === 0) {
            await this.loadExistingVersions();
        }
        const results: { [key: string]: string[] } = {};
        for (const [key, info] of this.versionMap.entries()) {
            const errors = await this.checkVersionFileIntegrity(info.prefix, info.version);
            if (errors.length > 0) {
                results[key] = errors;
            }
        }

        if (Object.keys(results).length === 0) {
            logWithKey('checkAllVersionsIntegrity', 'All version files are valid and complete.')
            return true;
        }

        console.error('Found integrity issues:');
        for (const [version, errors] of Object.entries(results)) {
            console.error(`\nVersion ${version}:`);
            errors.forEach(error => console.error(`- ${error}`));
        }
        throw new Error('checkAllVersionsIntegrity,failed!!!');
        return false;
    }

    /**
     * 获取打包的apk,ipa,exe等下载地址
     */
    public async getBuildPlayerDownloadInfo(version:string): Promise<{ fileName:string,size:string,url:string }[]> {
        if (this.versionMap == null || this.versionMap.size === 0) {
            await this.loadExistingVersions();
        }
        const prefix =`${this.options.platform}_${CDN_VERSION_PREFIX.BUILD_EXE}`
        const allfiles:{ fileName:string,size:string,url:string }[] = [];
        for (let [key, value] of this.versionMap) {
            if (value.version === version && value.prefix === prefix) {
                for(let file of value.files){
                    let url = `${this.options.cdnRootUrl}/${this.CdnRelativePath}/${file.relativePath}`;
                    let fileInfo = {
                        fileName : file.relativePath,
                        url:url,
                        size:humanReadableSize(file.size),
                    }
                    allfiles.push(fileInfo);
                }
                break;
            }
        }
        return allfiles;
    }


    public  async test(){
        let ret =await this.getBuildPlayerDownloadInfo('')
        for(const aa of ret){

        }
    }
}


// async function  test(){
//     // 使用 Git Bash 的路径
//     console.log('test')
//     $.shell = 'C:\\Program Files\\Git\\bin\\bash.exe';
//     const path = 'f:/testgit'
//     const ret1 = await $`cd ${path} && git log`;
//     const ret2 = await $`cd "${path}" && git reset --hard`;
//     // process.chdir('f:/testgit'); // 切换到目标目录
//     // const ret = await $`git log`;
//     console.log(ret2)
//     // console.log('After change: ', process.cwd()); // 输出更改后的目录
//     console.log('test')
// }
// test().catch((e)=>{
//     console.error('test')});
