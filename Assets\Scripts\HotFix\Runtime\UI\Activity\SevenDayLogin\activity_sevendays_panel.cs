
using System.Collections.Generic;
using ELEX.Config;
using Game.Common;
using Game.Common.Logic.Event;
using Game.Data;
using Game.Module;
using Game.UnActivity;

namespace Game.View
{
    public partial class activity_sevendays_panel : ViewBase
    {
        private activity_sevendays_award_item _day7Item;
        public CommonContainer<activity_sevendays_award_item, ActivitySevenDayData> rewardContainer;
        public CommonContainer<activity_sevendays_award_item, ActivitySevenDayData> lastRdContainer;
        private ActivityBaseData _baseData;
        private List<ActivitySevenDayData> _sixDayDataList;
        private List<ActivitySevenDayData> _sevenDayData;
        private Timer freeTimer;
        public override void OnAwake(int id, string name)
        {
            base.OnAwake(id, name);
            rewardContainer = new CommonContainer<activity_sevendays_award_item, ActivitySevenDayData>(this, ui.group1, typeof(activity_sevendays_award_item));
            rewardContainer.RgOnSetItemData((item, data, index) => { item.OnSetItemData(data, index); });
            rewardContainer.RgOnSelectItem(OnSelectedRewardItem);
            rewardContainer.RgOnClickItem(OnClickItem);
            
            lastRdContainer = new CommonContainer<activity_sevendays_award_item, ActivitySevenDayData>(this, ui.group2, typeof(activity_sevendays_award_item));
            lastRdContainer.RgOnSetItemData((item, data, index) => { item.OnSetItemData(data, index); });
            lastRdContainer.RgOnSelectItem(OnSelectedRewardItem);
            lastRdContainer.RgOnClickItem(OnClickItem);
            
            ButtonAddClick(ui.close_btn, ()=>Close());
            ButtonAddClick(ui.mask_btn, ()=>Close());
            ButtonAddClick(ui.detail_btn, GetHeroDetail);
            ButtonAddClick(ui.get_reward_btn, GetAllReward);
            ButtonAddClick(ui.play_btn, PlayVideo);
            AddListener(false, EventDefine.UI_ACTIVITY_SEVEN_DAY_UPDATE, RefreshUI);
            AddListener(false, EventDefine.UI_ACTIVITY_SEVEN_DAY_REWARD_UPDATE, RefreshReward);
            
        }

        public override void OnShow(object parameters)
        {
            base.OnShow(parameters);
            MODULE.BI.SendEvent(BIModule_EventDefine.SevenDayLogin, builder =>
            {
                builder.Add("isOpenUi", true);
            });
            _baseData = ActivitySevenDayModule.Instance.GetActivityBaseData();
            if (_baseData != null)
            {
                GetNetActivitySevenDaysData(); 
            }
        }

        protected override void OnDestroy()
        {
            if (freeTimer != null)
            {
                freeTimer.Dispose();
                freeTimer = null;
            }
            base.OnDestroy();
        }
        
        public void GetNetActivitySevenDaysData()
        {
            ActivityBaseModule.Instance.CsUnActivityInfo(_baseData.ActivityId);
        }
        
        
        public void RefreshUI()
        {
            var heroItemData = MODULE.Hero.GetHeroConfig(_baseData.Param1.SafeToInt());
            SetSprite(ui.quality_img, ConstString.UI_HERO_QUALITY_IMG[heroItemData.Quality]);
            SetAtlasNative(ui.quality_img, true);
            MODULE.Hero.UISetHeroTypeIcon(heroItemData.type, ui.type_img);
            SetTextValueTMP(ui.name_txt, Localization.Get(heroItemData.Name));
            var isSpine = !string.IsNullOrEmpty(_baseData.Param2);
            if (isSpine)
            {
                SetSpine(ui.heroSpine, _baseData.Param2);
                PlaySpineAnim(ui.heroSpine, "idle", true);
                SetActive(ui.heroImg, false);
            }
            
            RefreshReward();
        }
        
        public void RefreshReward()
        {
            _sixDayDataList = ActivitySevenDayModule.Instance.GetActivityDataList().FindAll(x  => x.day < 7);
            _sevenDayData = ActivitySevenDayModule.Instance.GetActivityDataList().FindAll(x  => x.day == 7);
            
            rewardContainer.SetData(_sixDayDataList);
            rewardContainer.RefreshUI();
            
            lastRdContainer.SetData(_sevenDayData);
            lastRdContainer.RefreshUI();
            
            var hasClaimable = _sevenDayData.Count > 0 && _sevenDayData.Find(x => x.rewardState == EActivityRewardState.Claimable) != null || _sixDayDataList.Count > 0 && _sixDayDataList.Find(x => x.rewardState == EActivityRewardState.Claimable) != null;
            if (!hasClaimable)
            {
                var leftTime = (DATA.User.CurDayEndTime - Time.GetServerTimeMS())/1000;
                uiObjs.time_txt.text = TimeConvertUtil.SecondFormatHhMmSs(leftTime);
                SetFreeTimer(leftTime);
            }
            SetActive(ui.get_reward_btn, hasClaimable);
            SetActive(ui.time_txt_obj, !hasClaimable);
            SetActive(ui.tips_reward_txt_obj, !hasClaimable);
            SetActive(ui.txtBtn, hasClaimable);
            if (ActivitySevenDayModule.Instance.IsAllClaimed())
            {
                SetActive(ui.get_reward_btn, false);
                SetActive(ui.time_txt_obj, false);
                SetActive(ui.tips_reward_txt_obj, false);
            }
        }
        
        public void GetHeroDetail()
        {
            var heroId = _baseData.Param1.SafeToInt();
            var heroItemData = DATA.Hero.GetHeroItem(heroId);
            var isNew = !heroItemData.IsHave;
            MODULE.Hero.UIOpenHeroObtainPanel(heroId, true, isNew, true);
        }

        public void GetAllReward()
        {
            ActivityBaseModule.Instance.CsUnActivityReward(_baseData.ActivityId, 0);
        }

        public void PlayVideo()
        {
            Dictionary<string, object> videoData = new Dictionary<string, object>
            {
                { "videoId", "Lilith_CG.mp4" },
                { "finishAction", null },
                { "isShowSkip", "1"}
            };
            VIEW.OpenUICover(UIDefine.PlayVideoPanel, videoData);
        }

        private void OnSelectedRewardItem(activity_sevendays_award_item item, ActivitySevenDayData data, int index)
        {
            if (data.rewardState == EActivityRewardState.Claimable)
            {
                ActivityBaseModule.Instance.CsUnActivityReward(_baseData.ActivityId, data.id);
            }
            else
            {
                ConfItem config = ConfHelper.GetConfItem(item._data.reward.id);
                if (config.type == (int)E_GOODS_ITEM_TYPE.HERO_CARD)
                {
                    var heroItemData = DATA.Hero.GetHeroItem(config.para1.SafeToInt());
                    VIEW.OpenUI(UIDefine.TipsContainerPanel, new GoodsTipsParams()
                    {
                        OnClickUid = item.Id,
                        AttachedName = "hero_property_panel",
                        ArrowType = ArrowType.ArrowTop,
                        GroupParams = heroItemData,
                        IsShieldPassClick = true,
                        ContentOffsetY = 600,
                        HideArrow = true,
                    }); 
                }
                else
                {
                    string nameTxt = Localization.Get(config.name);
                    var itemDataTmp = new IdNumType() { id = item._data.reward.id, num = DATA.Item.GetNum(item._data.reward.id)};
                    com_item_desc_tips.ItemDescData itemData = new com_item_desc_tips.ItemDescData() { title = nameTxt, data = itemDataTmp, };
                    VIEW.OpenUI(UIDefine.TipsContainerPanel, new GoodsTipsParams()
                    {
                        OnClickUid = item.Id,
                        AttachedName = "com_item_desc_tips",
                        ArrowType = ArrowType.ArrowBottom,
                        GroupParams = itemData,
                        IsShieldPassClick = true,
                    }); 
                }
                
            }
        }
        
        private void SetFreeTimer(long timeCount)
        {
            if(freeTimer != null)
            {
                return;
            }
            freeTimer = Timer.New(() =>
            {
                if (timeCount > 0)
                {
                    uiObjs.time_txt.text = TimeConvertUtil.SecondFormatHhMmSs(timeCount);
                    timeCount--;
                }
                else if (timeCount == 0)
                {
                    freeTimer.Stop();
                    freeTimer.Dispose();
                    freeTimer = null;
                    RefreshUI();
                }
            }, 1f, -1);
            freeTimer.Start();
        }
        
        private void OnClickItem(activity_sevendays_award_item item, ActivitySevenDayData data, int index, bool _)
        {
            OnSelectedRewardItem(item, data, index);
        }
        
    }
}
