import {logFunctionEnd, logFunctionStart, logWith<PERSON>ey} from "./utility.ts";
import {Command} from "commander";
import {checkAlreadyExistCosfiles, uploadByCosSdK, uploadToCosWithParam} from "./upload_to_cos.ts";
import {GitCdnManager} from "./git_cdn_manager.ts";
import {CDN_VERSION_PREFIX} from "./const.ts";

logFunctionStart('----------------------manual_upload_to_cos start---------------------------');
interface InputParms {
    platform: string
    env: string
    cdnRootDirectory: string,
    gitResDirectory: string,
    //资源版本
    resVersion: string,
    //配置版本，可能跟资源不一致
    cfgVersion: string,
}

// const program = new Command();
// program
//     .option('--platform <type>', '平台')
//     .option('--env <type>', '开发阶段环境变量')
//     .option('--cdnRootDirectory <type>', 'cdnroot')
//     .option('--cfgVersion <type>', '配置版本号')
//     .option('--resVersion <type>', '资源版本号')
// console.log(process.argv);
// program.parse(process.argv);
// const options = program.opts();
// let platform = options.platform;
// let env = options.env;
// let baseUrl = options.baseUrl;
// let cdnRootDirectory = options.cdnRootDirectory;
// let cfgVersion = options.cfgVersion;
// let resVersion = options.resVersion;
// 输出所有的环境变量内容
console.log('所有环境变量:', JSON.stringify(process.env, null, 2));
const inputParam: InputParms = {
    platform: process.env.PLATFORM || "",
    env: process.env.ENV || "",
    cdnRootDirectory:  process.env.CDN_ROOT_DIRECTORY||"",
    gitResDirectory: process.env.GIT_RES_DIRECTORY||"",
    cfgVersion: process.env.CFG_VERSION || "",
    resVersion: process.env.RES_VERSION || "",
}

// const inputParam: InputParms = {
//     platform: PLATFORM.WeixinMiniGame,
//     env: 'weixinminigame_lishengjie_optimize-config-v2',
//     cdnRootDirectory:'F:/cdn',
//     gitResDirectory:'',
//     cfgVersion:'2.0.1241040',
//     resVersion:'2.0.1241040',
// }
logWithKey('manual_upload_to_cos', `inputParam:${JSON.stringify(inputParam)}`);

async function manualUpload2Cos(inputParam: InputParms) {
    logFunctionStart('manualUpload2Cos')
    const manager = new GitCdnManager(inputParam.cdnRootDirectory,inputParam.gitResDirectory, {
        env: inputParam.env,
        platform: inputParam.platform,
        safeModel: true,
    });
    let cdnRootDirectory = manager.CdnRootDirectory;
    //这里只自动上传配置和资源，暂时咩有考虑字体
    let confs = await manager.getAllFilesInCurrentVersionWithPrefix(inputParam.cfgVersion, [`${inputParam.platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`]);
    let ress = await manager.getAllFilesInCurrentVersionWithPrefix(inputParam.resVersion, [`${inputParam.platform}_${CDN_VERSION_PREFIX.BUILD_RES}`]);
    let allfiles = [ ...(confs || []),...(ress||[])];
    logWithKey('manualUpload2Cos',`needUoadFilesCount:${allfiles.length}`);

    let existkeys = await checkAlreadyExistCosfiles();
    logWithKey('manualUpload2Cos', `exist keys cnt:${existkeys.length}`);

    let uploadResult = await uploadByCosSdK(allfiles, cdnRootDirectory,existkeys);
    if (uploadResult === false) {
        logWithKey('manualUpload2Cos', `upload failed`);
        throw new Error("upload failed");
    }else{
        logWithKey('manualUpload2Cos',`upload success`);
    }
    logFunctionEnd('manualUpload2Cos')
}



manualUpload2Cos(inputParam).then((_)=>{
    logFunctionStart('-----------------------------manualUpload2Cos end-----------------------------')
});
