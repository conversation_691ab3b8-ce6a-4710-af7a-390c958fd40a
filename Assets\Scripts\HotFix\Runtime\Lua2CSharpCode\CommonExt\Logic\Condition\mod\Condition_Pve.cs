// [FILE] ConditionSystem.lua
// [DATE] 2022-01-25
// [CODE] BY zegqingfgeng
// [MARK] 条件检测模块

using Game.Battle;
using Game.Common;
using Game.Data.Map;

namespace Game.Module
{
    public partial class ConditionSystem
    {
        public bool IsHero(string[] parameters, object data, bool isShowTips)
        {
            bool isHero = false;
            if(parameters != null && parameters.Length>1)
            {
                var menuData = data as EntityMenuData;
                if (menuData != null)
                {
                    //isHero = MODULE.Hero.IsHero(menuData.CityBuildingData.Id);
                }
            }
            return isHero;
        }

        /// <summary>
        /// 是否拥有英雄
        /// </summary>
        public bool IsHeroHave(string[] strArr, object data, bool isShowTips)
        {
            int heroId = 0;
            if (strArr != null && strArr.Length > 0)
            {
                heroId = int.Parse(Localization.GetStrFunction(strArr[0]));
            }
            var hero = DATA.Hero.GetHeroItem(heroId);
            if (hero != null)
            {
                return hero.IsHave;
            }
            return false;
        }

        public bool FinishPveBattle(string[] strArr, object data, bool isShowTips)
        {
            int pveId = 0;
            if (strArr != null && strArr.Length > 0)
            {
                pveId = int.Parse(Localization.GetStrFunction(strArr[0]));
            }
            //bool pass = DATA.Pve.GetPveLevelPassById(pveId);
            //if (!pass)
            //{
            //    pass = MODULE.BattleNpc.GetPveLevelPassById(pveId);
            //}
            //return pass;
            //return MODULE.BattleNpc.GetPveLevelPassById(pveId);
            return true;
        }

        public bool PveBattleFinishAndNoFixBuild(string[] strArr, object data, bool isShowTips)
        {
            int cfgId = 0;
            if (strArr != null && strArr.Length > 0)
            {
                cfgId = int.Parse(Localization.GetStrFunction(strArr[0]));
            }
            return MODULE.BattleNpc.IsFinish(cfgId) && MODULE.BattleNpc.isHaveDropItem(cfgId);
        }

        public bool NotMechOutFire()
        {
            return MODULE.BattleNpc.GetMechFire();
        }

        /// <summary>
        /// 英雄试用模型展示引导
        /// </summary>
        public bool HeroTryShowGuide(string[] parameters, object data, bool isShowTips)
        {
            //var levelId = ELEX.Config.ConfHelper.GetConfDataConstant("trialbattlefieldid2").value;
            //var parms = new[] { levelId };
            //var pass = ConditionSystem.FinishPveBattle(parms);
            var building = DATA.WorldMap.SelfCityData.GetBuildingByGroupId((int)E_BuildingType.TryHero);
            int heroId = int.Parse(ELEX.Config.ConfHelper.GetConfDataConstant("saveHeroId2").value);
            var hero = DATA.Hero.GetHeroItem(heroId);
            return !(hero != null && hero.IsHave) && building == null;
        }

        public bool IsBubbleNpc(string[] parameters, object data, bool isShowTips)
        {
            if (MODULE.BattleNpc.GetCurProgressAreaState() == AreaState.PveFinish)
            {
                return false;
            }
            return true;
        }

        public bool IsCurrentSceneId(string[] parameters, object data, bool isShowTips)
        {
            int sceneId = -1;
            if (parameters != null && parameters.Length > 0)
            {
                sceneId = int.Parse(Localization.GetStrFunction(parameters[0]));
            }

            var currentSceneId = UnicornPveControl.Instance.CurrPveSceneId;
            if (sceneId != -1 && currentSceneId == sceneId)
            {
                return true;
            }

            return false;
        }
    }
}
