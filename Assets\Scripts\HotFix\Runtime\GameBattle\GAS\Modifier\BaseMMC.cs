using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;
using ELEX.Config;
using Game.Battle;
using Game.Data;
using Game.Common;

namespace GAS.Runtime
{
    public class BaseMMC : ModifierMagnitudeCalculation
    {
        public enum AttributeType
        {
            [LabelText("Base值")]
            Base,
            [LabelText("当前值")]
            Current,
        }
        [TabGroup("Parameters", "MMC参数", TextColor = "#70C3FF", Order = 0)]
        [Title("基底属性类型")]
        [LabelText("属性类型")]
        public AttributeType attributeType = AttributeType.Current;

        [OnInspectorGUI] private void Space2() { GUILayout.Space(20); }

        [TabGroup("Default", "AttributeBasedModCalculation", SdfIconType.Calculator, TextColor = "#70C3FF", Order = 1)]
        [Title("基础公式", " 最终值 = 基底属性 * 技能系数 + 附加数值，各参数配置在Effect中，支持读取表格【unicorn_pve_skill_formula】的id并实时计算结果")]
        [Button(ButtonSizes.Gigantic)]
        private void Document()
        {
            Application.OpenURL("https://elex-work.feishu.cn/wiki/NypVwbdY9ig62ikI4ErckyY9nNf");
        }

        public static double GetSkillFormulaValue(int id, GameplayEffectSpec spec)
        {
            int configId = 0;
            AbilitySystemComponent source = spec.Source;
            UnicornEntity entity = UnicornEntityManager.GetInstance().GetUnicornEntity(source);
            if (entity != null && entity.IsPlayer())
            {
                configId = entity.GetConfigId();
            }
           
            return BaseMMC.GetSkillFormulaValue(id, configId);
        }
        public static double GetSkillFormulaValue(int id, AbilitySystemComponent source)
        {
            int configId = 0;
            UnicornEntity entity = UnicornEntityManager.GetInstance().GetUnicornEntity(source);
            if (entity != null && entity.IsPlayer())
            {
                configId = entity.GetConfigId();
            }
           
            return BaseMMC.GetSkillFormulaValue(id, configId);
        }

        public override float CalculateMagnitude(GameplayEffectSpec spec, float modifierMagnitude,GameplayEffectModifier modifier)
        {
            float finalValue = 0;

            //基底属性
            string AtkAttributeName = spec.ModifierParametersAttribute;
            //伤害系数
            float damageRate = spec.GameplayEffect.DamageRateSourceType == DataSourceType.TableId ?
                (float)GetSkillFormulaValue((int)spec.DamageRate, spec)
                : (float)spec.DamageRate;
            //附加固定值
            float fixedDamage = spec.GameplayEffect.FixedDamageSourceType == DataSourceType.TableId ?
                (float)GetSkillFormulaValue((int)spec.FixedDamage, spec)
                : (float)spec.FixedDamage;

            float releaseRate = Mathf.Clamp(spec.ReleaseRate, 0, 1f);    //技能分段系数

            float atk = 0;
            if (!AtkAttributeName.IsNullOrEmpty())
            {
                var split = AtkAttributeName.Split('.');
                var attributeSetName = split[0];
                var attributeShortName = split[1];
                atk = attributeType == AttributeType.Current ?
                    (float)spec.Source.GetAttributeCurrentValue(attributeSetName, attributeShortName)
                    :(float)spec.Source.GetAttributeBaseValue(attributeSetName, attributeShortName);
            }

            finalValue = releaseRate * (atk * damageRate + fixedDamage);
            spec.OnBaseDamageChanged(ref finalValue);
            spec.AttRecordDic.TryGetValue(modifier.AttributeName, out float recordValue);
            finalValue -= recordValue;
            //finalValue -= spec.ShieldReductionValue;
            //Debug.Log("" +                "公式来源:" + spec.GameplayEffect.GameplayEffectName +                 "计算结果:" + finalValue +                 " DamageRateSourceType:" + spec.GameplayEffect.DamageRateSourceType +                "DamageRate:" + spec.DamageRate +                " FixedDamageSourceType:" + spec.GameplayEffect.FixedDamageSourceType +                " FixedDamage:" + spec.FixedDamage);
            return finalValue;
        }

        static int iterationCount = 0;
        static int iterationMaxCount = 10;
        public static double GetSkillFormulaValue(int id, int heroConfigId, int skillLv = 0)
        {
            iterationCount = 0;
            return SkillFormulaValue(id, heroConfigId, skillLv);
        }
        public static double SkillFormulaValue(int id, int configId, int skillLv = 0)
        {
            iterationCount++;
            if (iterationCount > iterationMaxCount)
            {
                Log.Game.Error("Formula嵌套层数超过上限" + iterationMaxCount + " id=" + id);
                return 1;
            }

            ConfUnicornPveSkillFormula skillFormula = ConfHelper.GetConfUnicornPveSkillFormula(id);
            if (skillFormula != null)
            {
                switch (skillFormula.formula_type)
                {
                    case 0:  //直接数值
                        {
                            if (skillFormula.skill_id <= 0)
                            {
                                double level = skillFormula.param_level.Count > 0 ? skillFormula.param_level[0] : 0;
                                double star = skillFormula.param_star.Count > 0 ? skillFormula.param_level[0] : 0;
                                if (skillFormula.param_method == 0) return level + star;
                                if (skillFormula.param_method == 1) return level * star;
                            }
                            else
                            {
                                if (configId > 0)
                                {
                                    HeroItemData hData = DATA.Hero.GetHeroItem(configId);
                                    
                                    if (hData.PveSkillDict.TryGetValue(skillFormula.skill_id, out HeroSkillData skillData))
                                    {
                                        skillLv = skillLv == 0 ? skillData.Level : skillLv;
                                        int levelIndex = Mathf.Clamp(skillLv, 1, skillFormula.param_level.Count);
                                        levelIndex -= 1;
                                        levelIndex = levelIndex < 0 ? 0 : levelIndex;
                                        int starIndex = Mathf.Clamp(skillData.Star, 0, skillFormula.param_star.Count - 1);
                                        starIndex = starIndex < 0 ? 0 : starIndex;
                                        double level = (skillFormula.param_level.Count>levelIndex) ? skillFormula.param_level[levelIndex] : 0;
                                        double star = (skillFormula.param_star.Count > starIndex) ? skillFormula.param_star[starIndex] : 0;
                                        if (skillFormula.param_method == 0) return level + star;
                                        if (skillFormula.param_method == 1) return level * star;
                                    }
                                }
                            }
                        }
                        break;
                    case 1:  //递归加法
                        {
                            double result = 0;
                            foreach (var sId in skillFormula.formula_list)
                            {
                                result += SkillFormulaValue(sId, configId);
                            }
                            return result;
                        }

                    case 2:  //递归乘法
                        {
                            double result = 1;
                            foreach (var sId in skillFormula.formula_list)
                            {
                                result *= SkillFormulaValue(sId, configId);
                            }
                            return result;
                        }
                }
            }
            return 0;
        }
    }
}
