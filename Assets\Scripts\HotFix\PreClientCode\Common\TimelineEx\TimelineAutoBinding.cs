﻿using System.Collections.Generic;
using UnityEngine;
using System;
using Cinemachine;
using UnityEngine.Internal;

//using UnityEngine.Cinemachine;

[Serializable]
public class TimelineAutoBindingItem
{
    public string trackName;
    public string roleName;
    public bool bindLocalTransform = false;
}

[Serializable]
public class TestRolePositionItem
{
    public string roleName;
    public Transform roleTestPosition;
}

public class TimelineAutoBinding : MonoBehaviour
{
    public TimelineAutoBindingItem[] Bindings = null;

    [HideInInspector]
    public TestRolePositionItem[] TestRolePositionItems = null;

    public bool AutoBindCinemachine = true;
    public string CinemachineTrackName = "Cinemachine Track";
    public bool EnableCameraIgnoreLayers = false;
    public LayerMask CameraIgnoreLayer = 0;

    private int? _backupCameraLayer = null;

    private string _virtualCameraName = string.Empty;
    private CinemachineVirtualCameraBase _virtualCamera = null;



    public void Clear()
    {
        if (EnableCameraIgnoreLayers)
        {
            if (_backupCameraLayer.HasValue)
            {
                Camera camera = MainCinemachineBrainAnchor.Instance?.CinemachineBrain.GetComponent<Camera>();
                if (camera != null)
                {
                    camera.cullingMask = _backupCameraLayer.Value;
                }
                _backupCameraLayer = null;
            }
        }
    }
    public void Bind(TimelineFx timelineFx, bool isTest = false)
    {
        if (timelineFx == null)
            return;
        if (AutoBindCinemachine)
        {
            var cinemachineBrain = MainCinemachineBrainAnchor.Instance?.CinemachineBrain;
            if (cinemachineBrain != null)
            {
                if (timelineFx.HasTrack(CinemachineTrackName))
                {
                    timelineFx.SetBinding(CinemachineTrackName, cinemachineBrain);
                    // 绑定虚拟相机 unity 的timeline 在cinemachine 上有bug，绑定虚拟相机的时候必须和原来的相机名字一样
                    var mca = cinemachineBrain.ActiveVirtualCamera as CinemachineVirtualCameraBase;
                    if (mca)
                    {
                        if (EnableCameraIgnoreLayers)
                        {
                            Camera camera = cinemachineBrain.GetComponent<Camera>();
                            if (camera != null)
                            {
                                _backupCameraLayer = camera.cullingMask;
                                camera.cullingMask &= ~CameraIgnoreLayer.value;
                            }
                        }
                        _virtualCameraName = mca.name;
                        _virtualCamera = mca;
                        mca.name = "rpg_virtual_camera";
                        timelineFx.SetCinemachineClip(CinemachineTrackName, "rpg_virtual_camera", mca);
                        timelineFx.SetCinemachineClipFollow(
                            CinemachineTrackName,
                            "player_follow",
                            mca.Follow,
                            Enum_FindType.NameContains);
                        timelineFx.SetCinemachineClipLookAt(
                            CinemachineTrackName,
                            "player_lookat",
                            mca.LookAt,
                            Enum_FindType.NameContains);
                    }

                }
            }
        }

        if (isTest)
        {
            if (TestRolePositionItems != null && TestRolePositionItems.Length > 0)
            {
                foreach (var item in TestRolePositionItems)
                {
                    if (item.roleTestPosition != null)
                    {
                        var role = TimelineRoleManager.Instance.GetRole(item.roleName);
                        if (role == null)
                        {
                            Debug.LogError($"TimelineAutoBinding: Role not found:{item.roleName}");
                            continue;
                        }
                        if (item.roleTestPosition == null)
                        {
                            Debug.LogError($"TimelineAutoBinding: RoleTestPosition not found:{item.roleName}");
                            continue;
                        }
                        var roleTestSetPosition = role as System.Object as ITimelineRoleTransform;
                        if (roleTestSetPosition == null)
                        {
                            Debug.LogError($"TimelineAutoBinding: RoleTestSetPosition not found:{item.roleName}");
                            continue;
                        }
                        // roleTestSetPosition.SetPostion(item.roleTestPosition.position); // timeline 播放的時候位置自动被动画控制的
                    }
                }
            }
        }

        if (Bindings != null && Bindings.Length > 0)
        {
            foreach (var binding in Bindings)
            {
                if (binding != null)
                {
                    if (string.IsNullOrEmpty(binding.trackName))
                        continue;
                    if (string.IsNullOrEmpty(binding.roleName))
                        continue;
                    var role = TimelineRoleManager.Instance.GetRole(binding.roleName);
                    if (role == null)
                    {
                        Debug.LogError($"TimelineAutoBinding: Role not found:{binding.roleName} trackName:{binding.trackName}");
                        continue;
                    }

                    if (!timelineFx.HasTrack(binding.trackName))
                    {
                        Debug.LogError($"TimelineAutoBinding: Track not found:{binding.trackName} roleName:{binding.roleName}");
                        continue;
                    }

                    var success = role.BindToTimelineTrack(timelineFx.PlayableDirector, timelineFx.GetTrack(binding.trackName), binding.bindLocalTransform);
                    if (!success)
                    {
                        Debug.LogError($"TimelineAutoBinding: Bind failed:{binding.roleName} trackName:{binding.trackName}");
                    }
                }
            }
        }
    }

    public void UnBind(TimelineFx timelineFx)
    {
        if (timelineFx == null)
            return;

        if (AutoBindCinemachine && _virtualCamera != null && !string.IsNullOrEmpty(_virtualCameraName))
        {
            _virtualCamera.name = _virtualCameraName;
            _virtualCamera = null;
            _virtualCameraName = string.Empty;
        }

        if (Bindings != null && Bindings.Length > 0)
        {
            foreach (var binding in Bindings)
            {
                if (binding != null)
                {
                    if (string.IsNullOrEmpty(binding.trackName))
                        continue;
                    if (string.IsNullOrEmpty(binding.roleName))
                        continue;
                    var role = TimelineRoleManager.Instance.GetRole(binding.roleName);
                    if (role == null)
                    {
                        Debug.LogError("TimelineAutoBinding: Role not found:" + binding.roleName);
                        continue;
                    }

                    if (!timelineFx.HasTrack(binding.trackName))
                    {
                        Debug.LogError("TimelineAutoBinding: Track not found:" + binding.trackName + " timelinefx trackNames:" + timelineFx.GetTrackNames());
                        continue;
                    }

                    var success = role.UnBindToTimelineTrack(timelineFx.PlayableDirector, timelineFx.GetTrack(binding.trackName), binding.bindLocalTransform);
                    if (!success)
                    {
                        Debug.LogError("TimelineAutoBinding: UnBind failed:" + binding.roleName);
                    }
                }
            }
        }
    }
}