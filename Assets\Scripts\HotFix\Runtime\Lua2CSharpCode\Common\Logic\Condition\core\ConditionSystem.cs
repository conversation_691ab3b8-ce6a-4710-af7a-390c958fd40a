// -----------------------------------------------
// [FILE] ConditionSystem.cs
// [DATE] 2022-01-25
// [CODE] BY <PERSON>
// [MARK] Condition checking module
// -----------------------------------------------

using System;
using System.Collections.Generic;
using System.Reflection;

namespace Game.Module
{
    /// <summary>
    /// Condition checking system
    /// </summary>
    public partial class ConditionSystem
    {
        #region 常量

        private const int MAX_COUNT = 256; // 缓存的最大数量上线，超过的时候，自动清理所有 - 数量不大，只是以防意外导致一直上涨

        #endregion

        #region 变量

        private delegate bool
            ConditionHandler(ConditionSystem instance, string[] args, object input, bool showTips); // 委托类型定义

        private static readonly Dictionary<string, ConditionHandler> _delegateCache = new(); // 委托的缓存

        #endregion

        #region 内部方法

        /// <summary>
        /// 执行委托绑定的方法
        /// </summary>
        /// <param name="handler"></param>
        /// <param name="parameters"></param>
        /// <param name="inputParam"></param>
        /// <param name="showTips"></param>
        /// <param name="invert"></param>
        /// <returns></returns>
        private bool ExecuteDelegateExecuteDelegate(ConditionHandler handler, string[] parameters, object inputParam,
            bool showTips, bool invert)
        {
            var result = false;
            try
            {
                var args = (parameters.Length > 1 && !string.IsNullOrEmpty(parameters[1]))
                    ? parameters[1].Split(',')
                    : null;
                result = handler(this, args, inputParam, showTips);
            }
            catch (Exception e)
            {
                Common.Log.Logic.Error("执行缓存的反射condition的时候出现错误" + e.Message);
            }

            return invert ? !result : result;
        }

        #endregion

        #region 外部调用

        /// <summary>
        /// 检测condition
        /// </summary>
        /// <param name="cond"></param>
        /// <param name="input_param"></param>
        /// <param name="showTips"></param>
        /// <returns></returns>
        public bool CheckCondition(string cond, object input_param = null, bool showTips = false)
        {
            if (string.IsNullOrEmpty(cond))
            {
                Common.Log.Logic.Error("调用condition的时候传递的方法为空");
                return true;
            }

            // 打印当前_methodCache占用的内存大小
            // Game.Common.Util.ReflectCall.DebugMemoryUsage(_delegateCache);

            var parameters = cond.Split(":"); // 使用冒号分割方法和参数

            if (parameters.Length <= 0)
            {
                Common.Log.Logic.Error("调用condition的方法名解析出错，请检查配置:" + cond);
                return true;
            }

            var funcName = parameters[0]; // 获取方法名
            var invert = funcName.StartsWith("!"); // 检测是否取反
            if (invert) funcName = funcName.Substring(1); // 如果取反了，则名称去掉叹号

            // 如果缓存中有，则直接调用
            if (_delegateCache.TryGetValue(funcName, out var cachedDelegate))
                return ExecuteDelegateExecuteDelegate(cachedDelegate, parameters, input_param, showTips, invert);

            // 如果没有，则判定是否需要清理下内存
            if (_delegateCache.Count > MAX_COUNT)
            {
                // 全部清理，重新缓存 -- 数量不多，性能消耗应该不大，只是防止内存无限占用
                _delegateCache.Clear();
                Common.Log.Logic.Info("condition的反射缓存数量过大，清理后重新缓存");
            }

            // 如果没有，则反射获取，并转换成委托调用
            var method = typeof(ConditionSystem).GetMethod(funcName,
                BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
            if (method == null)
            {
                Common.Log.Logic.Error($"配置的Condition FUNC {funcName} not existed! 默认返回了true");
                return true; // 反射找不到方法，默认是true - 防止填写错误导致的流程阻塞
            }

            // 创建委托并缓存
            var handler = (ConditionHandler)Delegate.CreateDelegate(typeof(ConditionHandler), method);
            _delegateCache[funcName] = handler;

            return ExecuteDelegateExecuteDelegate(handler, parameters, input_param, showTips, invert);
        }

        public bool CheckCondition(List<string> cond, object input_param = null, bool default_value = true,
            bool showTips_ = false)
        {
            if (cond.Count == 0)
            {
                return true;
            }

            // Check if OR condition
            var isOrCond = cond[0] == "Or";
            if (isOrCond)
            {
                for (var i = 1; i < cond.Count; i++)
                {
                    if (CheckCondition(cond[i], input_param, showTips_))
                    {
                        return true;
                    }
                }

                return false;
            }

            foreach (var str in cond)
            {
                if (!CheckCondition(str, input_param, showTips_))
                {
                    return false;
                }
            }

            return true;
        }

        #endregion
    }
}