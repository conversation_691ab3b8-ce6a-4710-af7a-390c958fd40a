import {$} from 'zx';
import fs from 'fs-extra';
import * as path from 'path';
import {BuildParam} from "./build_params.ts";

import {GitCdnManager} from "./git_cdn_manager.ts";
import {BUILD_TYPE, CDN_VERSION_PREFIX, PLATFORM, platformCamelize} from "./const.ts";
import {isValidStr, logFunctionEnd, logFunctionStart, logWithKey} from "./utility.ts";
import {extractApkFromaab} from "./android_package_process.ts";


// /**
//  * 发送消息
//  */
// async function sendErrorMessage(buildParam: BuildParam, title: string, msg: string) {
//     const params = buildParam.getParams();
//     const state = buildParam.getState();
//     const content =await buildParam.getBuildNotifyMessage();
//     const message = `${content}
//     - 错误信息: ${msg}`
//     await sendEnhancedCard({
//         title: title,
//         content: message,
//         template: `red`
//     });
// }

///将打包的资产拷贝到cdn
async function saveBuildToGitCdn(buildParam: BuildParam) {
    const params = buildParam.getParams();
    const state = buildParam.getState();
    //非开发环境被认为是release正式发包
    let releaseBuild = !params.useEnvName
    let gitEnable = releaseBuild || params.useGitSave;//isRelease
    // let safeModel= isRelease
    const manager = new GitCdnManager(params.cdnRootDirectory, params.gitResDirectory, {
        env: state.envName,
        platform: state.platform,
        safeModel: releaseBuild,
        gitEnable:gitEnable,
        cdnRootUrl:params.cdnRootUrl,
    });
    buildParam.getContext().gitCdnManager = manager;
    // let isRelease= buildParam.isRelease()
    let needGitCdn = params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_APP ||
        params.buildType === BUILD_TYPE.BUILD_RES || params.buildType === BUILD_TYPE.BUILD_CONFIG
    let logs: string[] = []

    if (needGitCdn) {
        await manager.loadExistingVersions();
        //检测版本是否完整
        await manager.checkAllVersionsIntegrity();
    }

    if (params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_APP) {
        // 先验证目录结构
        const buildPlayerPath = buildParam.getBuildPlayerFullPath()
        const stats = fs.lstatSync(buildPlayerPath); // 获取路径信息（同步）
        if (stats.isDirectory()) {
            // 注册资源
            const zipPath = path.join(buildPlayerPath, `../${params.platform}_${CDN_VERSION_PREFIX.BUILD_PROJECT}_${state.appVersion}.zip`);
            await zipDirectory(buildPlayerPath, zipPath);
            await manager.addFileVersion(
                zipPath,
                state.appVersion,
                `${params.platform}_${CDN_VERSION_PREFIX.BUILD_PROJECT}`,
                true
                //传res
            );
        } else if (stats.isFile()) {
            //打包是单一产物
            let singleBuildArtifact = true;
            //特殊情况,Android在,如果打包的是aab格式,那么同步生成apk
            let isAAB = state.platform === PLATFORM.ANDROID && params.isGoogleAAB
            if(isAAB || params.createSymbols){
                singleBuildArtifact = false;
                if(isAAB){
                    let apkName =path.basename(buildPlayerPath,path.extname(buildPlayerPath))+'.apk';
                    let apkPath =path.join(path.dirname(buildPlayerPath),apkName);
                    await extractApkFromaab(buildPlayerPath,apkPath);
                }
                let androidBuildDir = path.dirname(buildPlayerPath);
                await manager.addDirectoryVersion(
                    androidBuildDir,
                    state.appVersion,
                    `${params.platform}_${CDN_VERSION_PREFIX.BUILD_EXE}`,
                    ["^(?!.*\\.(aab|apk|symbols.zip)$).*"],
                    true
                )
            }

            if(singleBuildArtifact){
                // 检查资源打包结果
                await manager.addFileVersion(
                    buildPlayerPath,
                    state.appVersion,
                    `${params.platform}_${CDN_VERSION_PREFIX.BUILD_EXE}`,
                    true
                );
            }
        } else {
            throw new Error(`invalid path:${buildPlayerPath}`)
        }

        if (state.platform === PLATFORM.WeixinMiniGame) {
            //const resOutDirectory = state.localResPath
            const fontPath = `${buildParam.getBuildPlayerFullPath()}/webgl/StreamingAssets/Fonts`
            if (fs.pathExistsSync(fontPath)) {
                // 传字体
                await manager.addDirectoryVersion(
                    fontPath,
                    state.appVersion,
                    `${params.platform}_${CDN_VERSION_PREFIX.BUILD_FONT}`,
                    [],
                     true, // 字体文件因为没有版本号，也不太需要用版本号去控制下载最新的，所以每次强制替换掉
                    'StreamingAssets/Fonts'
                )
                // logs.push(`增加微信小游戏字体，版本号 1.0.0`)
            }
        }
    }

    if (params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_RES) {
        const buildResFolder = `${state.projectPath}/Bundles/${platformCamelize(state.platform)}/Main/Data${platformCamelize(state.platform)}`
        await manager.addDirectoryVersion(
            buildResFolder,
            state.resVersion,
            `${params.platform}_${CDN_VERSION_PREFIX.BUILD_RES}`,
            state.platform === PLATFORM.WeixinMiniGame ? ["OutputCache*", "BuildReport_.*\\.json", "PackageManifest_.*\\.zip", "PackageManifest_.*\\.json"] :
                ["OutputCache*", "BuildReport_.*\\.json", "PackageManifest_.*\\.json"],
            true,
            state.platform === PLATFORM.WeixinMiniGame ? 'StreamingAssets/BuildinFiles' : 'BuildinFiles'
        )
        logWithKey('[copybuildinfiles]', `copy buildinfiles from ${buildResFolder}, resVersion:${state.resVersion}`);
    }

    if (params.buildType === BUILD_TYPE.BUILD_ALL || params.buildType === BUILD_TYPE.BUILD_CONFIG) {
        const cfgPath = `${state.projectPath}/Bundles/Config`
        await manager.addDirectoryVersion(
            cfgPath,
            state.cfgVersion,
            `${params.platform}_${CDN_VERSION_PREFIX.BUILD_CONF}`,
            [],
            true,//配置的版本号可能是重复的,那么目前是替换
            state.platform === PLATFORM.WeixinMiniGame ? 'StreamingAssets/config_files' : 'config_files'
        )
        logWithKey('[copyConfig]', `copy config from ${cfgPath}, cfgVersion:${state.cfgVersion}`);
    }

    if (needGitCdn) {
        if (gitEnable) {
            logFunctionStart('git-save-build')
            await manager.saveBuildToGit(buildParam)
            logFunctionEnd('git-save-build')
        }
        await manager.showVersionMap();
    }
}

async function zipDirectory(sourceDir: string, outPath: string): Promise<void> {
    try {
        const absoluteSourceDir = path.resolve(sourceDir);
        const absoluteOutPath = path.resolve(outPath);

        console.log('Zip operation details:');
        console.log('- Source directory:', absoluteSourceDir);
        console.log('- Output file:', absoluteOutPath);

        // 检查源目录
        if (!fs.existsSync(absoluteSourceDir)) {
            throw new Error(`Source directory does not exist: ${absoluteSourceDir}`);
        }

        // 确保源目录不为空
        const sourceFiles = fs.readdirSync(absoluteSourceDir);
        if (sourceFiles.length === 0) {
            throw new Error(`Source directory is empty: ${absoluteSourceDir}`);
        }

        // 创建输出目录
        const outDir = path.dirname(absoluteOutPath);
        await fs.ensureDir(outDir);

        // 执行 zip 命令，添加更多的输出信息
        const result = await $`cd ${absoluteSourceDir} && zip -r -v ${absoluteOutPath} .`;
        console.log('Zip command output:', result.stdout);

        // 验证结果
        if (!fs.existsSync(absoluteOutPath)) {
            throw new Error('Zip file was not created');
        }

        const stats = fs.statSync(absoluteOutPath);
        console.log(`Successfully created zip file (${stats.size} bytes)`);

    } catch (err: any) {  // 明确指定 error 类型为 any
        console.error('Detailed zip error:', err);

        // 安全地访问 stdout 和 stderr
        const stdout = err?.stdout ? String(err.stdout) : '';
        const stderr = err?.stderr ? String(err.stderr) : '';

        if (stdout) console.error('Command stdout:', stdout);
        if (stderr) console.error('Command stderr:', stderr);

        throw new Error(`Failed to zip directory: ${err.message || err}`);
    }
}

/*
async function zipDirectory(sourceDir: string, outPath: string): Promise<void> {
    try {
        const outDir = path.dirname(outPath);
        await fs.ensureDir(outDir);
        await $`cd ${sourceDir} && zip -r -q -9 ${outPath} ./*`;
    } catch (error) {
        throw new Error(`Failed to zip directory: ${error}`);
    }
}
*/
interface WebGLFiles {
    dataFile: string;
    codeFile: string;
}

function findWebGLFiles(webglDir: string): WebGLFiles | null {
    try {
        const files = fs.readdirSync(webglDir);

        // 查找 data 文件
        const dataFile = files.find(file =>
            file.endsWith('.webgl.data.unityweb.bin.br')
        );

        // 查找 code 文件
        const codeFile = files.find(file =>
            file.endsWith('.webgl.wasm.code.unityweb.wasm.br')
        );

        if (!dataFile || !codeFile) {
            throw new Error('Required WebGL files not found');
        }

        return {
            dataFile,
            codeFile
        };
    } catch (error) {
        console.error('Failed to find WebGL files:', error);
        return null;
    }
}

// 找到 webgl 目录下的文件
// 1. 处理 data 文件
// 2. 处理 code 文件
// 3. 处理minigame的备份
// 清理临时文件
//
//         if (!webglFiles) {
//             throw new Error('Failed to locate WebGL files');
//         }
//
//         // 1. 处理 data 文件
//         // await manager.addFileVersion(
//         //     path.join(webglDir, webglFiles.dataFile),
//         //     version,
//         //     'weixinminigame_data',
//         //     true
//         // );
//
//         // 2. 处理 code 文件
//         // await manager.addFileVersion(
//         //     path.join(webglDir, webglFiles.codeFile),
//         //     version,
//         //     'weixinminigame_code',
//         //     true
//         // );
//
//         if (backupTotalApp) {
//             // 3. 处理minigame的备份
//             let minigameDirectory = `${weixinOutputDirectory}/minigame`;
//             const backupZipPath = path.join(minigameDirectory, `../backup_${version}.zip`);
//             await zipDirectory(minigameDirectory, backupZipPath);
//             await manager.addFileVersion(
//                 backupZipPath,
//                 version,
//                 `${PLATFORM.WeixinMiniGame}_${CDN_VERSION_PREFIX.BUILD_PROJECT}`,
//                 true
//             );
//             // 清理临时文件
//             await fs.remove(backupZipPath);
//         }
//
//         console.log(`Successfully registered version ${version} resources`);
//
//     } catch (error) {
//         console.error('Failed to register resources:', error);
//         throw error;
//     }
// }

// 运行前检查文件是否存在的工具函数
async function validateWeixinDirectory(directory: string): Promise<boolean> {
    const webglDir = path.join(directory, 'webgl');

    try {
        // 检查目录是否存在
        if (!fs.existsSync(webglDir)) {
            throw new Error(`WebGL directory not found${webglDir}`);
        }

        // 检查必要的文件
        const webglFiles = findWebGLFiles(webglDir);
        if (!webglFiles) {
            throw new Error('Required WebGL files not found');
        }

        return true;
    } catch (error) {
        console.error('Directory validation failed:', error);
        return false;
    }
}

export {saveBuildToGitCdn}


// const excludePatterns = ["OutputCache*","BuildReport_Main_.*\\.json","PackageManifest_.*\\.zip","PackageManifest_.*\\.json"];
// const excludeRegexes = excludePatterns
//     ? excludePatterns.map(pattern => new RegExp(pattern))
//     : null;
// const entries = ['BuildReport_Main_2.0.1330139.json','main_assets_flameres_asset_battleanimationcurveres_e3407aa0171b7cf40d65a99a849b6b29.bundle',
// 'PackageManifest_Main_2.0.1330139.bytes','PackageManifest_Main_2.0.1330139.hash','PackageManifest_Main_2.0.1330139.json','PackageManifest_Main_2.0.1330139.zip'];
// for (const entry of entries) {
//     if (excludeRegexes && excludeRegexes.some(regex => regex.test(entry))) {
//         console.log(`匹配：${entry}`)
//     }
// }

