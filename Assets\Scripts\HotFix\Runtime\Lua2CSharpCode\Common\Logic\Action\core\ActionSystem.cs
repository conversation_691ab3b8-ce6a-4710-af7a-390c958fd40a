// -----------------------------------------------
// [FILE] ActionSystem.cs
// [DATE] 2022-01-25
// [CODE] BY zengqingfgeng
// [MARK] 逻辑动作系统
// -----------------------------------------------

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Game.Common
{
    /// <summary>
    /// ActionSystem class
    /// </summary>
    public partial class ActionSystem
    {
        #region 常量

        private const int MAX_COUNT = 256; // 缓存的最大数量上线，超过的时候，自动清理所有 - 数量不大，只是以防意外导致一直上涨

        #endregion

        #region 变量

        private string[] _params;

        private delegate object FuncActionDelegate(ActionSystem instance, string[] configParams, object inputParams);

        private delegate void VoidActionDelegate(ActionSystem instance, string[] configParams, object inputParams);

        private static readonly Dictionary<string, Delegate> _delegateCache = new();

        #endregion

        #region 内部方法

        private static void CacheMethod(string methodName, MethodInfo method)
        {
            try
            {
                // 根据返回类型编译不同委托
                if (method.ReturnType == typeof(void))
                {
                    var handler =
                        (VoidActionDelegate)Delegate.CreateDelegate(typeof(VoidActionDelegate), null,
                            method); // 创建开放实例委托
                    _delegateCache[methodName] = handler;
                }
                else
                {
                    var handler =
                        (FuncActionDelegate)Delegate.CreateDelegate(typeof(FuncActionDelegate), null,
                            method); // 创建开放实例委托
                    _delegateCache[methodName] = handler;
                }
            }
            catch (ArgumentException ex)
            {
                Log.Logic.Error($"反射action中创建委托失败: {methodName}, 错误: {ex.Message}");
            }
        }

        private object ExecuteCachedMethod(string methodName, string[] configParams, object inputParams)
        {
            // 尝试从委托缓存获取
            if (_delegateCache.TryGetValue(methodName, out var cachedDelegate))
            {
                return ExecuteDelegate(cachedDelegate, configParams, inputParams, methodName);
            }

            // 如果没有，则判定是否需要清理下内存
            if (_delegateCache.Count > MAX_COUNT)
            {
                // 全部清理，重新缓存 -- 数量不多，性能消耗应该不大，只是防止内存无限占用
                _delegateCache.Clear();
                Common.Log.Logic.Info("反射action缓存数量过大，清理后重新缓存");
            }

            // 获取action的method
            var method = typeof(ActionSystem).GetMethod(methodName);
            if (method == null) return null;

            // 验证方法签名
            var parameters = method.GetParameters();
            if (parameters.Length != 2 ||
                parameters[0].ParameterType != typeof(string[]) ||
                parameters[1].ParameterType != typeof(object))
            {
                Log.Logic.Error($"方法 {methodName} 签名不匹配，无法缓存");
                return null;
            }

            // 成功后则缓存
            CacheMethod(methodName, method);

            return ExecuteDelegate(_delegateCache[methodName], configParams, inputParams, methodName);
        }


        private object ExecuteDelegate(Delegate handler, string[] configParams, object inputParams, string methodName)
        {
            // 打印当前_methodCache占用的内存大小
            // Util.ReflectCall.DebugMemoryUsage(_delegateCache);
            try
            {
                // 参数优先级：外部参数 > 配置参数
                if (inputParams is Dictionary<string, object> inputDict)
                {
                    configParams = inputDict.Values.Select(v => v.ToString()).ToArray();
                }

                // 根据委托类型分发执行
                switch (handler)
                {
                    case FuncActionDelegate funcDelegate:
                        return funcDelegate(this, configParams, inputParams);

                    case VoidActionDelegate voidDelegate:
                        voidDelegate(this, configParams, inputParams);
                        return null; // void方法返回null

                    default:
                        throw new InvalidOperationException("不支持的委托类型");
                }
            }
            catch (Exception ex)
            {
                Log.Logic.Error($"反射action中执行委托失败，方法名为{methodName}的内部处理有问题，错误内容: {ex.Message}");
                return null;
            }
        }

        private object ExecuteSingleAction<T>(string action, object input_params = null)
        {
            if (string.IsNullOrEmpty(action)) return null;

            // 处理本地化
            action = Localization.GetStrFunction(action);

            // 解析方法和参数
            var parts = action.Split(':');
            var methodName = parts[0];
            var configParams = parts.Length > 1 ? parts[1].Split(',') : Array.Empty<string>();

            return (T)ExecuteCachedMethod(methodName, configParams, input_params);
        }

        #endregion

        #region 外部调用

        public List<object> ExecuteAction<T>(string[] actions, Dictionary<string, object> input_params = null)
        {
            if (actions == null || actions.Length == 0) return null;

            var results = new List<object>();
            foreach (string action in actions)
            {
                var result = ExecuteSingleAction<T>(action, input_params);
                if (result != null) results.Add(result);
            }

            return results;
        }

        public List<object> ExecuteAction(string[] actions, Dictionary<string, object> input_params = null)
        {
            return ExecuteAction<object>(actions, input_params);
        }

        public object ExecuteAction(string action, Dictionary<string, object> input_params = null)
        {
            return ExecuteSingleAction<object>(action, input_params);
        }

        public object ExecuteAction<T>(string action, object input_params = null)
        {
            return ExecuteSingleAction<T>(action, input_params);
        }

        #endregion
    }
}
