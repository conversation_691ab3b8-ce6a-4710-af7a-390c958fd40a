<?xml version="1.0" encoding="utf-8"?>
<WwiseDocument Type="WorkUnit" ID="{76879537-3F31-4E24-B605-619A49BE6FE7}" SchemaVersion="119">
	<InteractiveMusic>
		<WorkUnit Name="Default Work Unit" ID="{76879537-3F31-4E24-B605-619A49BE6FE7}" PersistMode="Standalone">
			<ChildrenList>
				<MusicSwitchContainer Name="Music" ID="{93CA96D2-DF9C-4169-AA98-093B12F348A7}" ShortID="577311877">
					<PropertyList>
						<Property Name="EnableLoudnessNormalization" Type="bool" Value="True"/>
						<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
						<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
						<Property Name="MaxSoundPerInstance" Type="int16">
							<ValueList>
								<Value>1</Value>
							</ValueList>
						</Property>
					</PropertyList>
					<ReferenceList>
						<Reference Name="Conversion">
							<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
						</Reference>
						<Reference Name="OutputBus">
							<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
						</Reference>
						<Reference Name="TransitionRoot">
							<Custom>
								<MusicTransition Name="Root" ID="{7F2B4A41-8A96-473D-B41D-66451E37EE19}">
									<PropertyList>
										<Property Name="IsFolder" Type="bool" Value="True"/>
									</PropertyList>
									<ChildrenList>
										<MusicTransition Name="Transition" ID="{32FF66AB-DD61-4050-85FA-E3177536A251}">
											<PropertyList>
												<Property Name="EnableDestinationFadeIn" Type="bool" Value="True"/>
												<Property Name="EnableSourceFadeOut" Type="bool" Value="True"/>
												<Property Name="ExitSourceAt" Type="int16" Value="0"/>
											</PropertyList>
											<TransitionInfo>
												<SourceFadeOut>
													<MusicFade Name="Source Fade-out" ID="{C5204D30-BA6E-4917-805D-98458D37116F}">
														<PropertyList>
															<Property Name="FadeOffset" Type="Real64" Value="3"/>
															<Property Name="FadeTime" Type="Real64" Value="3"/>
															<Property Name="FadeType" Type="int16" Value="1"/>
														</PropertyList>
													</MusicFade>
												</SourceFadeOut>
												<DestinationFadeIn>
													<MusicFade Name="Destination Fade-in" ID="{94300261-2257-4098-B462-B43F850D428D}">
														<PropertyList>
															<Property Name="FadeTime" Type="Real64" Value="3"/>
														</PropertyList>
													</MusicFade>
												</DestinationFadeIn>
											</TransitionInfo>
										</MusicTransition>
									</ChildrenList>
									<TransitionInfo/>
								</MusicTransition>
							</Custom>
						</Reference>
					</ReferenceList>
					<ChildrenList>
						<MusicPlaylistContainer Name="music_rpg_forest" ID="{9336313A-5894-44C8-BD7A-E593E62060F9}" ShortID="1017077112">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{C0739C2F-78C2-4C27-A182-F3639D212EAD}" ShortID="708615906">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{D2E7855F-FE8F-4AE8-A97A-2FCC7B25CB00}" ShortID="103457014">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_rpg_forest_01" ID="{952B238B-1579-4AC1-A32B-9F1FF152A990}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
												<MusicPlaylistItem Name="" ID="{E955E1B6-F7EF-4187-AD36-F5D9B8D78823}" ShortID="204544054">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="silence" ID="{0637A5DA-6B54-4CC5-B856-83913D3DEAF0}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{A6622373-89B2-47AD-A3B0-F0AA026B5776}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{B9339DE3-C4BD-40F5-A162-70405EC6568A}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="silence" ID="{0637A5DA-6B54-4CC5-B856-83913D3DEAF0}" ShortID="239035191">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="59972.3949125312"/>
										<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
										<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
										<Property Name="MaxSoundPerInstance" Type="int16">
											<ValueList>
												<Value>1</Value>
											</ValueList>
										</Property>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{C433BD71-D1A7-4B0A-A89B-74AB9236E20C}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{6A0D6ADB-9F49-4B62-AE0B-2709CC5AF7DE}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="59972.3949125312"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
								<MusicSegment Name="music_rpg_forest_01" ID="{952B238B-1579-4AC1-A32B-9F1FF152A990}" ShortID="76916367">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="79499.9791666667"/>
										<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
										<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
										<Property Name="MaxSoundPerInstance" Type="int16">
											<ValueList>
												<Value>1</Value>
											</ValueList>
										</Property>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_rpg_forest_01" ID="{0269C193-4058-4799-A39B-94B8A74044DA}" ShortID="491372406">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{5A6C1DA7-F69C-4972-A0DF-983C541EEE02}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_rpg_forest_01" ID="{3E19EED1-F81D-4B51-A3E1-2295BFEB643A}">
													<Language>SFX</Language>
													<AudioFile>music_rpg_forest_01.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="487732415"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{9AFDB4CB-5959-4DD0-A349-1433EF16F50C}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_rpg_forest_01" ID="{36B7D7F7-D807-4A1B-B96C-65ED4DF2AF3A}">
																					<PropertyList>
																						<Property Name="BeginTrimOffset" Type="Real64" Value="1499.99960691824"/>
																						<Property Name="EndTrimOffset" Type="Real64" Value="76000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutDuration" Type="Real64" Value="30545.4465408805"/>
																						<Property Name="FadeOutShape" Type="int16" Value="1"/>
																						<Property Name="PlayAt" Type="Real64" Value="-1499.99960691824"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_rpg_forest_01" ID="{3E19EED1-F81D-4B51-A3E1-2295BFEB643A}"/>
																					<PropertyCurveList>
																						<PropertyCurve PropertyName="Highpass">
																							<Curve Name="" ID="{97C56F9B-D9D3-4B12-9BFF-1DD81A7F3FE2}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.5</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Lowpass">
																							<Curve Name="" ID="{5677FD44-C924-4F97-A0DB-0205D65D96E8}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.5</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Volume">
																							<Curve Name="" ID="{5F6EE746-CB53-43A7-99CA-0E568511BD73}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="3"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.5</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																					</PropertyCurveList>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{C272D114-C994-4373-AB01-4F81B82EF658}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{6D24D8E5-23DF-4222-AF8D-5CFC62B7AD32}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="76000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_rpg_cove" ID="{D66E376A-9938-4CF0-90AD-E1FF2C225D8E}" ShortID="412552840">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{662EEBA4-A38D-41F1-B797-A86C39B90706}" ShortID="1045483354">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{B113BE9E-1E65-40FB-9211-DC419DAB423A}" ShortID="573071513">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_rpg_cove" ID="{7AFE718F-33D8-4F7C-8A31-C5329FB809C9}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{07C572D7-FED7-426E-ABA3-495192081204}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{1D0A687A-26E2-41F9-9940-4D33CAB5653C}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="music_rpg_cove" ID="{7AFE718F-33D8-4F7C-8A31-C5329FB809C9}" ShortID="246197515">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="111882.99920637"/>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_rpg_cove" ID="{6B264E14-862E-4365-8BCB-114CE10C62BC}" ShortID="341664739">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{51E00DEF-0E25-45AC-9EC9-E58FA2C57E75}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_rpg_cove" ID="{8346D6AA-01F3-423F-B24F-7C811BDE7AEF}">
													<Language>SFX</Language>
													<AudioFile>music_rpg_cove.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="484913630"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{B26CBDE4-D622-48F1-AD8C-CEA1A4C329DD}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_rpg_cove" ID="{A9D7B0FB-E4D4-4327-B793-134285EF03E5}">
																					<PropertyList>
																						<Property Name="EndTrimOffset" Type="Real64" Value="108000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutDuration" Type="Real64" Value="16401.6962187348"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_rpg_cove" ID="{8346D6AA-01F3-423F-B24F-7C811BDE7AEF}"/>
																					<PropertyCurveList>
																						<PropertyCurve PropertyName="Highpass">
																							<Curve Name="" ID="{8B117DEC-D9D8-4BCB-9682-FDE548EDA560}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>108</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Lowpass">
																							<Curve Name="" ID="{8B18EE90-2ADD-4ABE-9DC4-C4C0FB3D8E3A}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>108</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Volume">
																							<Curve Name="" ID="{9F10E917-B05D-4544-B52C-ED4CAF1BB77E}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="3"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>108</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																					</PropertyCurveList>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{EFE95401-F522-4AF4-9AB3-2B68F51A4BF5}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{5BF7041B-4DD1-491B-8CF8-8E64F09315FF}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="108000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_SE_cove" ID="{94CCAE90-53AB-4698-9BAB-B158AC0A1DA0}" ShortID="26029522">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{6C5568C0-8512-47E0-B743-2B90F6DF7A21}" ShortID="443697272">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{E68405FA-4EEB-4855-837A-DF1E6A32EC98}" ShortID="992314335">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_se_forest" ID="{544FB10A-79BE-49BD-8704-1F4A49C79958}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{506B2516-20BF-43E1-A516-4AB91A26C7A8}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{348F8E78-741C-46EC-A1E8-822C575C94DA}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="music_se_forest" ID="{544FB10A-79BE-49BD-8704-1F4A49C79958}" ShortID="774145330">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="56000"/>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_se_forest" ID="{B22C7642-DC18-4714-98C4-227D9811A5B1}" ShortID="301051964">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{47BA1FA3-BBC5-4650-B069-2E57B66E55ED}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_se_forest" ID="{A8E1901D-8319-4159-BAB6-4AC3E9B9FE3F}">
													<Language>SFX</Language>
													<AudioFile>music_se_forest.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="593964798"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{A35871B8-1458-4789-9238-ADEF21C99D84}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_se_forest" ID="{9EDFE268-2445-4C2B-AB2C-904FD1C0CB84}">
																					<PropertyList>
																						<Property Name="EndTrimOffset" Type="Real64" Value="56000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutMode" Type="int16" Value="0"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_se_forest" ID="{A8E1901D-8319-4159-BAB6-4AC3E9B9FE3F}"/>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{C68DCCB3-2DF6-406D-9E76-F2FB7459098C}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{9F946E2E-F157-48F3-95C8-52A7B3595B67}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="56000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_SE_forest" ID="{02EFE722-E799-40C8-9A3E-6FB051373A0D}" ShortID="69131580">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{74EAFE3E-BD51-4935-88E8-46779082D90C}" ShortID="344773026">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{9C94B028-F32D-480F-A673-918BA850DB30}" ShortID="431973111">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_se_forest" ID="{1409306B-61DE-4A2A-B39E-A4DB993B8FB1}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{8FAF4CC3-6828-4937-98AA-13E383CFD8F1}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{023B924B-0E48-4610-897A-D6E96C5D0664}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="music_se_forest" ID="{1409306B-61DE-4A2A-B39E-A4DB993B8FB1}" ShortID="971890046">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="56000"/>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_se_forest" ID="{D125F3DB-2174-4F21-BA11-3E10641AC422}" ShortID="441862963">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{4C60E3E1-7E63-4C0F-9085-516DC757E34F}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_se_forest" ID="{9CF333E8-46F1-41B9-884D-3E1D286C644F}">
													<Language>SFX</Language>
													<AudioFile>music_se_forest.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="593964798"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{0D7FE49A-0B5A-427B-ACB1-1F518CCDDCF1}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_se_forest" ID="{4526A15F-1A6C-4F1F-A293-0A549A37179B}">
																					<PropertyList>
																						<Property Name="EndTrimOffset" Type="Real64" Value="56000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutMode" Type="int16" Value="0"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_se_forest" ID="{9CF333E8-46F1-41B9-884D-3E1D286C644F}"/>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{5E03C0B7-03B7-45D5-A036-CC1065F62A67}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{F267DC69-BA56-48CD-954B-7C45822DC314}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="56000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_SE_volcano" ID="{38C8BAA7-4444-4DB7-8EE8-2F444C89159D}" ShortID="533140024">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{0F287ED9-1D95-4C11-B68D-DE317BBBCCAE}" ShortID="517509028">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{403D2DA8-AB67-402F-85F2-F310AE8589AE}" ShortID="937037207">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_se_forest" ID="{115FEDA3-8886-455F-BE77-80913BD0A7D5}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{FA8927DA-1396-42C1-8BB7-7103DBB44EB1}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{2BD33973-77C1-4952-BE5C-875C7936F54D}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="music_se_forest" ID="{115FEDA3-8886-455F-BE77-80913BD0A7D5}" ShortID="831304598">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="56000"/>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_se_forest" ID="{F4626190-1442-4324-AF0E-1DCB5515831E}" ShortID="751673715">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{D4073349-49E3-4E2F-BE09-064F9997F853}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_se_forest" ID="{67796FAD-EDEB-4115-80ED-C8F1664F7AA0}">
													<Language>SFX</Language>
													<AudioFile>music_se_forest.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="593964798"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{E0A222A7-1359-46E9-8E23-F168B20F4611}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_se_forest" ID="{9ECD3D84-D427-4A74-9228-C532539DC45B}">
																					<PropertyList>
																						<Property Name="EndTrimOffset" Type="Real64" Value="56000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutMode" Type="int16" Value="0"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_se_forest" ID="{67796FAD-EDEB-4115-80ED-C8F1664F7AA0}"/>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{88AFF163-A097-4C04-8E1F-2DB9E44A855C}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{ADEABB01-68F4-462F-80E2-A30F5A0BFC37}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="56000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_city" ID="{F6CDAC29-29BC-482C-AC32-404B737815A8}" ShortID="307615258">
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{C38BD9E2-35E7-4099-8F24-783BD2C69018}" ShortID="207649208">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{A990FFDF-2955-4B46-91D0-25C7837C713B}" ShortID="19244492">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_rpg_forest_01" ID="{78418270-E0D7-4881-88FE-153D54CB28EB}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
												<MusicPlaylistItem Name="" ID="{0940CF9E-B701-47BD-BF95-7F5AD41ED10C}" ShortID="250606209">
													<PropertyList>
														<Property Name="LoopCount" Type="int16" Value="3"/>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="silence" ID="{BB312F38-29CD-4A75-997C-DB87AEFE2AFB}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{A8FB34CC-F2E6-44EE-9805-695ED61DD032}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{CCACD92D-C979-4C8D-B7DB-D848EDFCB421}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="silence" ID="{BB312F38-29CD-4A75-997C-DB87AEFE2AFB}" ShortID="181961992">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="59972.3949125312"/>
										<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
										<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
										<Property Name="MaxSoundPerInstance" Type="int16">
											<ValueList>
												<Value>1</Value>
											</ValueList>
										</Property>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{C707FAA6-91D3-4268-A15E-471729868048}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{E491343A-A382-44CD-8179-6C190A0F8C07}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="59972.3949125312"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
								<MusicSegment Name="music_rpg_forest_01" ID="{78418270-E0D7-4881-88FE-153D54CB28EB}" ShortID="274326966">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="80000"/>
										<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
										<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
										<Property Name="MaxSoundPerInstance" Type="int16">
											<ValueList>
												<Value>1</Value>
											</ValueList>
										</Property>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_rpg_forest_01" ID="{FF71025F-3F2D-402F-A0E4-419E7123B611}" ShortID="638425473">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{0B338764-4FC4-443C-9A83-ABF8C7DE004A}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_rpg_forest_01" ID="{60A80448-6C19-4C38-AA98-6E0C31B6160E}">
													<Language>SFX</Language>
													<AudioFile>music_rpg_forest_01.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="487732415"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{E15B1FCC-2B2C-43FF-8EF4-0B52B3CD6655}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_rpg_forest_01" ID="{A8E09DC2-7245-4F7C-8CD7-DDBD21F10807}">
																					<PropertyList>
																						<Property Name="BeginTrimOffset" Type="Real64" Value="1450.24837617468"/>
																						<Property Name="EndTrimOffset" Type="Real64" Value="76000"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutDuration" Type="Real64" Value="30059.693615257"/>
																						<Property Name="FadeOutShape" Type="int16" Value="1"/>
																						<Property Name="PlayAt" Type="Real64" Value="-1450.24837617468"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_rpg_forest_01" ID="{60A80448-6C19-4C38-AA98-6E0C31B6160E}"/>
																					<PropertyCurveList>
																						<PropertyCurve PropertyName="Highpass">
																							<Curve Name="" ID="{02DA212D-4F30-456B-BBD9-4CDB27FF7762}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.45025</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Lowpass">
																							<Curve Name="" ID="{141EE6D0-3FB1-4536-BF05-4FBC6900316C}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.45025</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Volume">
																							<Curve Name="" ID="{61980816-D672-4565-B448-A496AC717D89}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="3"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>1.45025</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>76</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																					</PropertyCurveList>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{D320EF44-4F7D-4884-9967-1C1A87549CA8}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{AA3A5BE8-2760-4B39-ABD3-2200A1A7EBF0}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="76000"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
						<MusicPlaylistContainer Name="music_rpg_volcano" ID="{DE121EE1-552B-46FE-AB1D-F62B8116BE85}" ShortID="428667060">
							<PropertyList>
								<Property Name="IsGlobalLimit" Type="int16" Value="1"/>
								<Property Name="MaxReachedBehavior" Type="int16" Value="1"/>
								<Property Name="MaxSoundPerInstance" Type="int16">
									<ValueList>
										<Value>1</Value>
									</ValueList>
								</Property>
							</PropertyList>
							<ReferenceList>
								<Reference Name="Conversion">
									<ObjectRef Name="Vorbis Auto Detect High" ID="{6F6A99BE-4FD1-49D3-89AF-54BE30CA1588}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
								</Reference>
								<Reference Name="OutputBus">
									<ObjectRef Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
								</Reference>
								<Reference Name="PlaylistRoot">
									<Custom>
										<MusicPlaylistItem Name="" ID="{34A34D42-761B-40A1-B496-E8419EAB2D49}" ShortID="346136531">
											<PropertyList>
												<Property Name="LoopCount" Type="int16" Value="0"/>
											</PropertyList>
											<ChildrenList>
												<MusicPlaylistItem Name="" ID="{2C074EBC-2E9A-4AC5-87A0-24F612953789}" ShortID="119012570">
													<PropertyList>
														<Property Name="PlaylistItemType" Type="int16" Value="1"/>
													</PropertyList>
													<ReferenceList>
														<Reference Name="Segment">
															<ObjectRef Name="music_rpg_volcano" ID="{638292DC-E99F-4F72-AC5C-3B9764ABD2F0}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
														</Reference>
													</ReferenceList>
												</MusicPlaylistItem>
											</ChildrenList>
										</MusicPlaylistItem>
									</Custom>
								</Reference>
								<Reference Name="TransitionRoot">
									<Custom>
										<MusicTransition Name="Root" ID="{9FE2FE38-E9DE-46AA-90AE-DF4C76940E69}">
											<PropertyList>
												<Property Name="IsFolder" Type="bool" Value="True"/>
											</PropertyList>
											<ChildrenList>
												<MusicTransition Name="Transition" ID="{627DC173-A841-4317-854B-EDB7411F20E6}">
													<TransitionInfo/>
												</MusicTransition>
											</ChildrenList>
											<TransitionInfo/>
										</MusicTransition>
									</Custom>
								</Reference>
							</ReferenceList>
							<ChildrenList>
								<MusicSegment Name="music_rpg_volcano" ID="{638292DC-E99F-4F72-AC5C-3B9764ABD2F0}" ShortID="84037218">
									<PropertyList>
										<Property Name="EndPosition" Type="Real64" Value="238944"/>
									</PropertyList>
									<ReferenceList>
										<Reference Name="Conversion">
											<ObjectRef Name="ADPCM Auto Detect High" ID="{6D51666E-9C7A-40E4-831D-C445512EBABD}" WorkUnitID="{F6B2880C-85E5-47FA-A126-645B5DFD9ACC}"/>
										</Reference>
										<Reference Name="OutputBus">
											<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
										</Reference>
									</ReferenceList>
									<ChildrenList>
										<MusicTrack Name="music_rpg_volcano" ID="{95B60A60-CF2A-4691-A746-A8E15DF350CC}" ShortID="377701131">
											<PropertyList>
												<Property Name="IsStreamingEnabled" Type="bool">
													<ValueList>
														<Value>True</Value>
													</ValueList>
												</Property>
											</PropertyList>
											<ReferenceList>
												<Reference Name="OutputBus">
													<ObjectRef Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}" WorkUnitID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}"/>
												</Reference>
												<Reference Name="TransitionRoot">
													<Custom>
														<MusicTransition Name="Root" ID="{96603790-84CB-497C-97E0-9EDC5AF8D232}">
															<PropertyList>
																<Property Name="IsFolder" Type="bool" Value="True"/>
															</PropertyList>
															<TransitionInfo/>
														</MusicTransition>
													</Custom>
												</Reference>
											</ReferenceList>
											<ChildrenList>
												<AudioFileSource Name="music_rpg_volcano" ID="{C752690B-09BD-4B7D-A683-085E0DA9A686}">
													<Language>SFX</Language>
													<AudioFile>music_rpg_volcano.wav</AudioFile>
													<MediaIDList>
														<MediaID ID="466308787"/>
													</MediaIDList>
												</AudioFileSource>
											</ChildrenList>
											<ObjectLists>
												<ObjectList Name="Sequences">
													<Reference>
														<Local>
															<MusicTrackSequence Name="" ID="{E7163A45-FD2D-4D18-8912-F934A7419F6F}">
																<ObjectLists>
																	<ObjectList Name="Clips">
																		<Reference>
																			<Local>
																				<MusicClip Name="music_rpg_volcano" ID="{C530A175-F389-48D9-8698-99BD2002ECE0}">
																					<PropertyList>
																						<Property Name="EndTrimOffset" Type="Real64" Value="238944"/>
																						<Property Name="FadeInMode" Type="int16" Value="0"/>
																						<Property Name="FadeOutDuration" Type="Real64" Value="44142.0219656434"/>
																					</PropertyList>
																					<AudioSourceRef Name="music_rpg_volcano" ID="{C752690B-09BD-4B7D-A683-085E0DA9A686}"/>
																					<PropertyCurveList>
																						<PropertyCurve PropertyName="Highpass">
																							<Curve Name="" ID="{9FEB1382-586F-4BC2-ABAC-C2C8C1AFF96A}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>238.944</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Lowpass">
																							<Curve Name="" ID="{A7A35DFE-8D79-4280-9044-586DAD926CE3}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="1"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>238.944</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																						<PropertyCurve PropertyName="Volume">
																							<Curve Name="" ID="{89E4D485-B05F-489A-8C7D-D1C394548220}">
																								<PropertyList>
																									<Property Name="Flags" Type="int32" Value="3"/>
																								</PropertyList>
																								<PointList>
																									<Point>
																										<XPos>0</XPos>
																										<YPos>0</YPos>
																										<Flags>5</Flags>
																									</Point>
																									<Point>
																										<XPos>238.944</XPos>
																										<YPos>0</YPos>
																										<Flags>37</Flags>
																									</Point>
																								</PointList>
																							</Curve>
																						</PropertyCurve>
																					</PropertyCurveList>
																				</MusicClip>
																			</Local>
																		</Reference>
																	</ObjectList>
																</ObjectLists>
															</MusicTrackSequence>
														</Local>
													</Reference>
												</ObjectList>
											</ObjectLists>
										</MusicTrack>
									</ChildrenList>
									<ObjectLists>
										<ObjectList Name="Cues">
											<Reference>
												<Local>
													<MusicCue Name="Entry Cue" ID="{3E8ADD21-1BD9-4018-964C-B3F6C7239319}"/>
												</Local>
											</Reference>
											<Reference>
												<Local>
													<MusicCue Name="Exit Cue" ID="{336D1A8D-4D94-47A0-952D-A3FB6001607B}">
														<PropertyList>
															<Property Name="CueType" Type="int16" Value="1"/>
															<Property Name="TimeMs" Type="Real64" Value="238944"/>
														</PropertyList>
													</MusicCue>
												</Local>
											</Reference>
										</ObjectList>
									</ObjectLists>
								</MusicSegment>
							</ChildrenList>
						</MusicPlaylistContainer>
					</ChildrenList>
					<ObjectLists>
						<ObjectList Name="Arguments">
							<Reference>
								<ObjectRef Name="Scene" ID="{A86778D8-E7C2-4E65-ABF0-A2CE86C4E398}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
							</Reference>
						</ObjectList>
						<ObjectList Name="Entries">
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{0A8EB755-E814-4606-B556-44FC8BE36AC8}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_rpg_cove" ID="{D66E376A-9938-4CF0-90AD-E1FF2C225D8E}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="RPG_Cove" ID="{AC272DED-BB9E-470A-B95B-E6BDCA1AF812}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{8CB370A5-B795-44BA-9F49-CA5647ACA40D}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_rpg_forest" ID="{9336313A-5894-44C8-BD7A-E593E62060F9}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="RPG_Forest" ID="{D11EDC0F-3C13-4641-A633-B4A6A0113271}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{0CD7DAA0-5018-497A-AF9F-3175D7BD0AAF}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_rpg_volcano" ID="{DE121EE1-552B-46FE-AB1D-F62B8116BE85}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="RPG_Volcano" ID="{CF4ADE9D-E4D3-486A-BAF5-F9D73707FAA6}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{863C91DC-0219-4EB9-AFC9-0513097DFE4D}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_SE_cove" ID="{94CCAE90-53AB-4698-9BAB-B158AC0A1DA0}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="SE_Cove" ID="{B89C69D8-D750-4E84-A5E0-6419AA839497}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{4C30A08D-8514-4B08-8054-B1C3FA9CFC37}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_SE_forest" ID="{02EFE722-E799-40C8-9A3E-6FB051373A0D}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="SE_Forest" ID="{D40C59E3-6255-46A3-BDC9-8CB318F97DFB}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{6E85971C-6903-46BC-B7CD-1ECA1092585D}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_SE_volcano" ID="{38C8BAA7-4444-4DB7-8EE8-2F444C89159D}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="SE_Volcano" ID="{ED783BC2-5ECD-4618-989D-79AEC9D0CABB}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
							<Reference>
								<Local>
									<MultiSwitchEntry Name="" ID="{F909A4CA-A136-4E3B-92E9-1313A3887669}">
										<ReferenceList>
											<Reference Name="AudioNode">
												<ObjectRef Name="music_city" ID="{F6CDAC29-29BC-482C-AC32-404B737815A8}" WorkUnitID="{76879537-3F31-4E24-B605-619A49BE6FE7}"/>
											</Reference>
										</ReferenceList>
										<ObjectLists>
											<ObjectList Name="EntryPath">
												<Reference>
													<ObjectRef Name="City" ID="{643A1D2F-7EA3-40D9-B72E-2AA3BA4C974F}" WorkUnitID="{C7876E74-6D6F-40ED-B9EF-5F6FB83B75D9}"/>
												</Reference>
											</ObjectList>
										</ObjectLists>
									</MultiSwitchEntry>
								</Local>
							</Reference>
						</ObjectList>
						<ObjectList Name="Stingers">
							<Reference>
								<Local>
									<MusicStinger Name="" ID="{27FAFE16-D40B-49F3-9E9C-034B77D260F7}"/>
								</Local>
							</Reference>
						</ObjectList>
					</ObjectLists>
				</MusicSwitchContainer>
			</ChildrenList>
		</WorkUnit>
	</InteractiveMusic>
</WwiseDocument>
