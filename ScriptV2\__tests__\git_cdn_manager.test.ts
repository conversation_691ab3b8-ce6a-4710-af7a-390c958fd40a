﻿import {GitCdnManager} from "../src/git_cdn_manager";
import {BUILD_TYPE, PLATFORM} from "../src/const";
import {BuildParam} from "../src/build_params";
import {$} from "zx";

describe('git cdn manager', () => {
    test('test git ', async() => {
        //注意，windows下路径分隔符
        const cdnRoot = 'F:/cdn';
        const git = 'F:/testgit'
        const param = new BuildParam();
        param.updateParams({"buildType": BUILD_TYPE.BUILD_ALL,'platform': PLATFORM.WeixinMiniGame});

        param.updateState('resVersion','2.0.1261108')
        param.updateState('cfgVersion','2.0.1261108')
        param.updateState('envName','weixinminigame_lishengjie_optimize-config-v2')


        let gitcdnmanager = new GitCdnManager(cdnRoot, git, {
            platform: param.getParams().platform,
            env: param.getState().envName, gitBranch: 'main1', safeModel: false,gitEnable :true
        })

        await gitcdnmanager.saveBuildToGit(param)
        expect(true);
    });
});
