%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!47 &1
QualitySettings:
  m_ObjectHideFlags: 0
  serializedVersion: 5
  m_CurrentQuality: 3
  m_QualitySettings:
  - serializedVersion: 3
    name: HighLevel
    pixelLightCount: 4
    shadows: 2
    shadowResolution: 2
    shadowProjection: 0
    shadowCascades: 1
    shadowDistance: 120
    shadowNearPlaneOffset: 2
    shadowCascade2Split: 0.33333334
    shadowCascade4Split: {x: 0.06666667, y: 0.19999999, z: 0.46666664}
    shadowmaskMode: 0
    skinWeights: 4
    globalTextureMipmapLimit: 0
    textureMipmapLimitSettings: []
    anisotropicTextures: 0
    antiAliasing: 0
    softParticles: 1
    softVegetation: 1
    realtimeReflectionProbes: 0
    billboardsFaceCameraPosition: 0
    useLegacyDetailDistribution: 1
    vSyncCount: 0
    realtimeGICPUUsage: 25
    lodBias: 1
    maximumLODLevel: 0
    enableLODCrossFade: 1
    streamingMipmapsActive: 0
    streamingMipmapsAddAllCameras: 1
    streamingMipmapsMemoryBudget: 512
    streamingMipmapsRenderersPerFrame: 512
    streamingMipmapsMaxLevelReduction: 2
    streamingMipmapsMaxFileIORequests: 1024
    particleRaycastBudget: 4
    asyncUploadTimeSlice: 2
    asyncUploadBufferSize: 4
    asyncUploadPersistentBuffer: 1
    resolutionScalingFixedDPIFactor: 1
    customRenderPipeline: {fileID: 11400000, guid: a5b41844b014bc746879a6d387bb0611,
      type: 2}
    terrainQualityOverrides: 0
    terrainPixelError: 1
    terrainDetailDensityScale: 1
    terrainBasemapDistance: 1000
    terrainDetailDistance: 80
    terrainTreeDistance: 5000
    terrainBillboardStart: 50
    terrainFadeLength: 5
    terrainMaxTrees: 50
    excludedTargetPlatforms: []
  - serializedVersion: 3
    name: MediumLevel
    pixelLightCount: 4
    shadows: 1
    shadowResolution: 1
    shadowProjection: 0
    shadowCascades: 1
    shadowDistance: 120
    shadowNearPlaneOffset: 2
    shadowCascade2Split: 0.33333334
    shadowCascade4Split: {x: 0.06666667, y: 0.2, z: 0.46666667}
    shadowmaskMode: 0
    skinWeights: 2
    globalTextureMipmapLimit: 0
    textureMipmapLimitSettings: []
    anisotropicTextures: 0
    antiAliasing: 0
    softParticles: 1
    softVegetation: 1
    realtimeReflectionProbes: 0
    billboardsFaceCameraPosition: 0
    useLegacyDetailDistribution: 1
    vSyncCount: 0
    realtimeGICPUUsage: 25
    lodBias: 0.5
    maximumLODLevel: 0
    enableLODCrossFade: 0
    streamingMipmapsActive: 0
    streamingMipmapsAddAllCameras: 1
    streamingMipmapsMemoryBudget: 512
    streamingMipmapsRenderersPerFrame: 512
    streamingMipmapsMaxLevelReduction: 2
    streamingMipmapsMaxFileIORequests: 1024
    particleRaycastBudget: 1
    asyncUploadTimeSlice: 2
    asyncUploadBufferSize: 4
    asyncUploadPersistentBuffer: 1
    resolutionScalingFixedDPIFactor: 1
    customRenderPipeline: {fileID: 11400000, guid: ccb8354f3bd57b64c94c5cb1da5425a0,
      type: 2}
    terrainQualityOverrides: 0
    terrainPixelError: 1
    terrainDetailDensityScale: 1
    terrainBasemapDistance: 1000
    terrainDetailDistance: 80
    terrainTreeDistance: 5000
    terrainBillboardStart: 50
    terrainFadeLength: 5
    terrainMaxTrees: 50
    excludedTargetPlatforms: []
  - serializedVersion: 3
    name: LowLevel
    pixelLightCount: 4
    shadows: 1
    shadowResolution: 0
    shadowProjection: 0
    shadowCascades: 1
    shadowDistance: 10
    shadowNearPlaneOffset: 2
    shadowCascade2Split: 0.33333334
    shadowCascade4Split: {x: 0.06666667, y: 0.2, z: 0.46666667}
    shadowmaskMode: 0
    skinWeights: 1
    globalTextureMipmapLimit: 1
    textureMipmapLimitSettings: []
    anisotropicTextures: 0
    antiAliasing: 0
    softParticles: 0
    softVegetation: 1
    realtimeReflectionProbes: 0
    billboardsFaceCameraPosition: 0
    useLegacyDetailDistribution: 1
    vSyncCount: 0
    realtimeGICPUUsage: 25
    lodBias: 0.1
    maximumLODLevel: 0
    enableLODCrossFade: 0
    streamingMipmapsActive: 0
    streamingMipmapsAddAllCameras: 1
    streamingMipmapsMemoryBudget: 512
    streamingMipmapsRenderersPerFrame: 512
    streamingMipmapsMaxLevelReduction: 2
    streamingMipmapsMaxFileIORequests: 1024
    particleRaycastBudget: 1
    asyncUploadTimeSlice: 2
    asyncUploadBufferSize: 4
    asyncUploadPersistentBuffer: 1
    resolutionScalingFixedDPIFactor: 1
    customRenderPipeline: {fileID: 11400000, guid: 25a0d3df833a7474a9c8a647ba6c6ab7,
      type: 2}
    terrainQualityOverrides: 0
    terrainPixelError: 1
    terrainDetailDensityScale: 1
    terrainBasemapDistance: 1000
    terrainDetailDistance: 80
    terrainTreeDistance: 5000
    terrainBillboardStart: 50
    terrainFadeLength: 5
    terrainMaxTrees: 50
    excludedTargetPlatforms: []
  - serializedVersion: 3
    name: VeryLowLevel
    pixelLightCount: 4
    shadows: 0
    shadowResolution: 0
    shadowProjection: 0
    shadowCascades: 1
    shadowDistance: 12
    shadowNearPlaneOffset: 2
    shadowCascade2Split: 0.33333334
    shadowCascade4Split: {x: 0.06666667, y: 0.2, z: 0.46666667}
    shadowmaskMode: 1
    skinWeights: 1
    globalTextureMipmapLimit: 2
    textureMipmapLimitSettings: []
    anisotropicTextures: 0
    antiAliasing: 0
    softParticles: 0
    softVegetation: 1
    realtimeReflectionProbes: 0
    billboardsFaceCameraPosition: 0
    useLegacyDetailDistribution: 1
    vSyncCount: 0
    realtimeGICPUUsage: 50
    lodBias: 0.01
    maximumLODLevel: 0
    enableLODCrossFade: 0
    streamingMipmapsActive: 0
    streamingMipmapsAddAllCameras: 1
    streamingMipmapsMemoryBudget: 512
    streamingMipmapsRenderersPerFrame: 512
    streamingMipmapsMaxLevelReduction: 2
    streamingMipmapsMaxFileIORequests: 1024
    particleRaycastBudget: 0
    asyncUploadTimeSlice: 2
    asyncUploadBufferSize: 4
    asyncUploadPersistentBuffer: 1
    resolutionScalingFixedDPIFactor: 1
    customRenderPipeline: {fileID: 11400000, guid: 25a0d3df833a7474a9c8a647ba6c6ab7,
      type: 2}
    terrainQualityOverrides: 0
    terrainPixelError: 1
    terrainDetailDensityScale: 1
    terrainBasemapDistance: 1000
    terrainDetailDistance: 80
    terrainTreeDistance: 5000
    terrainBillboardStart: 50
    terrainFadeLength: 5
    terrainMaxTrees: 50
    excludedTargetPlatforms: []
  m_TextureMipmapLimitGroupNames: []
  m_PerPlatformDefaultQuality:
    Android: 0
    Server: 0
    Standalone: 0
    iPhone: 0
