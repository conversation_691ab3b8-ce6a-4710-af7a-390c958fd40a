import {LOG_TAG} from "./const.ts";
import {format} from "date-fns";
import os from "os";


export function logFunctionStart(func: string, info: any = '') {
    console.log(`\n[FUNC_START][${func}][⌛️${getFormatTimeShow()}]${info}`)
}

export function logFunctionEnd(func: string, info: any = '') {
    console.log(`\n[FUNC_END][${func}][⌛️${getFormatTimeShow()}]${info}`)
}

export function logWithKey(key: any, info: any) {
    console.log(`\n[${key}][⌛️${getFormatTimeShow()}]${info}`)
}


export function log(info: any) {
    console.log(`\n${info}`)
}

/**
 * 不合法的字符
 * @param str
 */
export function isInvalidStr(str: string | null | undefined): boolean {
    return !str || str.trim() === "";
}

export function isValidStr(str: string | null | undefined): boolean {
    return !isInvalidStr(str);
}

/**
 * 工具函数：将字符串转换为数字，空字符串返回0
 * @param value 要转换的字符串
 * @returns 转换后的数字
 */
export function stringToNumber(value: string | undefined | null): number {
    if (!value || value.trim() === '') {
        return 0;
    }
    const num = Number(value);
    return isNaN(num) ? 0 : num;
}

export function safeConvert(value: string | undefined | null): number | string {
    if (!value || value.trim() === '') {
        return '';
    }
    const num = Number(value);
    return isNaN(num) ? '' : num;
}


export function parseInt32(value: string) {
    const int32Max = 2147483647;
    const int32Min = -2147483648;

    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
        throw new Error("Invalid number format");
    }
    if (parsed > int32Max) {
        console.log("exceed maximum number format");
    }

    if (parsed < int32Min) {
        console.log("exceed min number format");
    }
    // 限制数字范围
    return Math.min(Math.max(parsed, int32Min), int32Max);
}

export function getFormatTime(){
    let now = format(new Date(), 'yyyyMMddHHmm');
    return now;
}

export function getFormatTimeShow(){
    const now = new Date();
    const localizedTimeCN = now.toLocaleString('zh-CN'); // 中国时间格式
    return localizedTimeCN;
}

/**
 * url拼接函数
 * @param base
 * @param paths
 */
export function urlJoin(base: string, ...paths: string[]): string {
    // 去除多余的 '/'，并安全拼接路径
    const sanitizedPaths = paths.map(path => path.replace(/^\/+|\/+$/g, '')); // 去除开头和末尾的斜杠
    const url = [base.replace(/\/+$/g, ''), ...sanitizedPaths].join('/'); // 确保 base 后只有一个 `/`
    return url;
}

export const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function getLocalIPAddress(): string | null {
    // 获取所有网络接口信息
    const networkInterfaces = os.networkInterfaces();

    // 遍历所有接口
    for (const interfaceName in networkInterfaces) {
        const networkInterface = networkInterfaces[interfaceName];
        if (!networkInterface) continue; // 如果网络接口为空直接跳过

        for (const iface of networkInterface) {
            // IPv4 且没有内部地址（不是本地回环 127.0.0.1）
            if (iface.family === 'IPv4' && !iface.internal) {
                return iface.address; // 返回第一个有效的 IP 地址
            }
        }
    }

    return null; // 如果找不到 IP 地址
}

export function humanReadableSize(bytes: number): string {
    if (bytes < 0) {
        throw new Error("Size must be a non-negative number");
    }

    if (bytes === 0) {
        return "0B"; // 特殊处理 0 的情况
    }

    const units = ["B", "K", "M", "G", "T", "P"]; // 字节单位：字节、千字节、兆字节、千兆字节、太字节、拍字节
    const base = 1024; // 每个单位之间的换算基数是 1024

    let unitIndex = 0; // 对应当前单位的索引
    let readableSize = bytes;

    // 持续除以 1024，直到找到合适的单位
    while (readableSize >= base && unitIndex < units.length - 1) {
        readableSize /= base;
        unitIndex++;
    }

    // 保留两位小数，格式化为字符串
    return `${readableSize.toFixed(2)}${units[unitIndex]}`;
}
