using System.Collections.Generic;
using Game.Common;
using Game.RPGExploration;
using ParadoxNotion.Design;

namespace Game.CityEventSystem
{
    [Name("修改事件完成监听器")]
    public class ChangeCityEventFinishListener : ActionBase
    {
        [Header("目标ID")]
        public string TargetId;

        [Header("监听事件完成列表"), OdinTree]
        public List<CityEventFinishActionData> CityEventFinishesActionDatas;

        protected override void OnExecute()
        {
            if (string.IsNullOrEmpty(TargetId))
            {
                Log.Game.Error($"修改事件完成监听器目标ID为空");
                EndAction(false);
                return;
            }

            var target = ActionBridge.CurrentBridge.GetCityEventFinishListener(TargetId);
            if (target == null)
            {
                Log.Game.Error($"修改事件完成监听器目标为空，目标ID：{TargetId}");
                EndAction(false);
                return;
            }

            target.CityEventFinishListener.ReplaceActionDatas(CityEventFinishesActionDatas);
            EndAction(true);
        }
    }
}