using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Game.Battle;
namespace GAS.Runtime
{
    public class CatchAreaBox : CatchArea
    {
        public Vector3 box = Vector3.one;          // 半径
        public ValueOrTableIdSelector boxX;
        public ValueOrTableIdSelector boxZ;
        public bool isBottom;           // 是否为底边
        public float offsetAngle = 0f;       // 偏转角度 
        public OffsetType offsetType;        //偏移类型
        public Vector2 centerOffset;         // 中心点偏移
        private static readonly Collider[] colliders = new Collider[32];

        protected override void CatchTargetsNonAlloc(AbilitySystemComponent mainTarget, List<AbilitySystemComponent> results)
        {

            InitializeCache();
            
            try
            {
                if (!(boxX.dataSourceType == DataSourceType.Value && boxX.fixedValue == 0 && boxZ.dataSourceType == DataSourceType.Value && boxZ.fixedValue == 0))
                {
                    box = new Vector3(boxX.GetFloatValue(this.Owner), 3, boxZ.GetFloatValue(this.Owner));
                }
                // 计算检测位置
                Vector3 pos;
                Transform baseTransform;
                if (mainTarget != null)
                {
                    baseTransform = offsetType == OffsetType.Self ? this.Owner.transform : mainTarget.transform;
                }
                else
                {
                    baseTransform = this.Owner.transform;
                }
                pos = baseTransform.position;

                // 应用偏移和旋转
                Quaternion baseRotation = baseTransform.rotation;
                Quaternion finalRotation = baseRotation * Quaternion.Euler(0, offsetAngle, 0);
                pos += finalRotation * new Vector3(centerOffset.x, 0, centerOffset.y);
                Vector3 rotatedForward = finalRotation * Vector3.forward;

                // 底部偏移
                if (isBottom)
                {
                    pos += rotatedForward * box.z/2;
                }

                UnicornCharacter character = this.Owner.GetComponent<UnicornCharacter>();
                // 计算检测位置和旋转
                Vector3 eulerAngles = this.Owner.transform.eulerAngles;
                Quaternion rotation = Quaternion.Euler(eulerAngles.x, eulerAngles.y + offsetAngle, eulerAngles.z);
                int count = Physics.OverlapBoxNonAlloc(pos, box/2, colliders, finalRotation);

                for (int i = 0; i < count; i++)
                {
                    Collider c = colliders[i];
                    AbilitySystemComponent ac = c.GetComponent<AbilitySystemComponent>();
                    if (ac != null && IsValidTarget(ac))
                    {
                        results.Add(ac);
                    }
                }

                AddSelfIfNeeded(results);
                PostProcessResults(results);

#if UNITY_EDITOR
                OnEditorPreview(this.Owner.gameObject);
#endif
            }
            finally
            {
          
                ClearCache();
            }
        }
#if UNITY_EDITOR
        public override void OnEditorPreview(GameObject obj)
        {
            base.OnEditorPreview(obj);
            
            // 1. 获取基础位置
            Vector3 pos = obj.transform.position;
            Quaternion baseRotation = obj.transform.rotation;

            // 2. 计算中心点偏移
            Vector3 offset = baseRotation * new Vector3(centerOffset.x, 0, centerOffset.y);
            
            // 3. 计算旋转后的前向方向（用于底部偏移）
            Quaternion finalRotation = baseRotation * Quaternion.Euler(0, offsetAngle, 0);
            Vector3 rotatedForward = finalRotation * Vector3.forward;

            // 4. 应用中心点偏移和底部偏移
            Vector3 center = pos + offset;
            if (isBottom)
            {
                center += rotatedForward * box.z/2;
            }

            Vector3 size = box * 0.5f;

            // 计算8个顶点
            Vector3[] points = new Vector3[8];
            points[0] = center + finalRotation * new Vector3(-size.x, -size.y, -size.z);
            points[1] = center + finalRotation * new Vector3(size.x, -size.y, -size.z);
            points[2] = center + finalRotation * new Vector3(size.x, -size.y, size.z);
            points[3] = center + finalRotation * new Vector3(-size.x, -size.y, size.z);
            points[4] = center + finalRotation * new Vector3(-size.x, size.y, -size.z);
            points[5] = center + finalRotation * new Vector3(size.x, size.y, -size.z);
            points[6] = center + finalRotation * new Vector3(size.x, size.y, size.z);
            points[7] = center + finalRotation * new Vector3(-size.x, size.y, size.z);

            // 绘制底部四条线
            Debug.DrawLine(points[0], points[1], Color.green,1);
            Debug.DrawLine(points[1], points[2], Color.green, 1);
            Debug.DrawLine(points[2], points[3], Color.green, 1);
            Debug.DrawLine(points[3], points[0], Color.green, 1);

            // 绘制顶部四条线
            Debug.DrawLine(points[4], points[5], Color.green, 1);
            Debug.DrawLine(points[5], points[6], Color.green, 1);
            Debug.DrawLine(points[6], points[7], Color.green, 1);
            Debug.DrawLine(points[7], points[4], Color.green, 1);

            // 绘制连接顶部和底部的四条线
            Debug.DrawLine(points[0], points[4], Color.green, 1);
            Debug.DrawLine(points[1], points[5], Color.green, 1);
            Debug.DrawLine(points[2], points[6], Color.green, 1);
            Debug.DrawLine(points[3], points[7], Color.green, 1);

            // 绘制前向方向（用于调试）
            Debug.DrawRay(pos, rotatedForward * 2f, Color.blue, 1);
        }
#endif
    }
}
