import {<PERSON><PERSON><PERSON>aram} from "./build_params.ts";
import {ClientBaseConfig, ClientCentralControlManager} from "./client_central_control.ts";
import {BUILD_TYPE, CENTRAL_CONTRAL_OPS_KEY, CENTRAL_CONTRAL_PROJECT_ID, CENTRAL_CONTRAL_WEB_URL} from "./const.ts";
import {logFunctionEnd, logFunctionStart, logWithKey} from "./utility.ts";

//开发阶段自动更新总控
async function updateCentralControl(buildParam: BuildParam) {
    logFunctionStart("updateCentralControl")
    let params= buildParam.getParams();
    let state= buildParam.getState();

    // if(state.platform !== PLATFORM.WeixinMiniGame && state.platform !== PLATFORM.ANDROID){
    //     return;
    // }

    // let content= await buildParam.getBuildNotifyMessage()
    if(params.autoUpdateRemoteVersion === false){
        // await sendEnhancedCard({
        //     title: "请人工更改总控文件！！！",
        //     content: content,
        //     template: "red"
        // });
        logWithKey('updateCentralControl', `autoUpdateRemoteVersion:${params.autoUpdateRemoteVersion}`)
        return;      
    }
   
  
    let updateData: Partial<ClientBaseConfig> = {};
    let update= false;
    let buildType= params.buildType;
    if(buildType === BUILD_TYPE.BUILD_ALL){
        update= true;
        updateData.recommendAppVersion= state.appVersion
        updateData.confVersion= state.cfgVersion
        updateData.resVersion= state.resVersion
    } else if (buildType === BUILD_TYPE.BUILD_RES) {
        update= true;
        updateData.resVersion= state.resVersion
    } else if (buildType === BUILD_TYPE.BUILD_CONFIG) {
        update= true;
        updateData.confVersion= state.cfgVersion
    } else if (buildType === BUILD_TYPE.BUILD_APP) {
        update= true;
        updateData.recommendAppVersion= state.appVersion
    }
    if(update){
        const configManager = new ClientCentralControlManager(
            CENTRAL_CONTRAL_WEB_URL,
            CENTRAL_CONTRAL_OPS_KEY,
            CENTRAL_CONTRAL_PROJECT_ID
        );
        // 查询配置
        logWithKey('updateCentralControl', `before update Querying config...`)
        const queryResultBefore = await configManager.queryConfig();
        logWithKey('updateCentralControl', `Query result:${JSON.stringify(queryResultBefore, null, 2)}`)

        // 更新配置
        const success = await configManager.updateEnvConfig(
            state.envName,
            updateData
        );
        logWithKey('updateCentralControl', `Update result:${success ? 'Success' : 'Failed'}`)
       
        if(success){
            // 查询配置
            //     title: "更新内网总控文件成功",
            //     content: content,
            //     template: "green"
            // });
            // 查询配置
            logWithKey('updateCentralControl', `after update Querying config...`)
            const queryResultAfter = await configManager.queryConfig();
            logWithKey('updateCentralControl', `Query result:${JSON.stringify(queryResultAfter, null, 2)}`)
        } else {
            // await sendEnhancedCard({
            //     title: "更新内网总控文件失败",
            //     content: content,
            //     template: "red"
            // });
            // process.exit(1);
            throw new Error("自动更新总控失败");
        }
    }
    logFunctionEnd("updateCentralControl")
}
export {updateCentralControl}
