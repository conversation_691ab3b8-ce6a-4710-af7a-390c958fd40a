using ParadoxNotion.Design;
using Game.Common;
using ELEX.Config;
using Game.RPGExploration;
using NodeCanvas.Framework;

namespace Game.CityEventSystem
{
    [Name("物品飞出")]
    public class ItemFly : ActionBase
    {
        [Header("交互物ID")]
        public string InteractionId;

        protected override void OnExecute()
        {
            var cityEventId = CityEventTree.blackboard.GetVariable<int>(ConstString.Id).value;
            var rewardId = ConfHelper.GetConfUnicornRpgMission(cityEventId).reward;
            if (rewardId <= 0)
            {
                Log.Rpg.Warning($"ItemFly cityEventId : {cityEventId} rewardId is less than 0");
                EndAction(true);
                return;
            }

            var interaction = ActionBridge.CurrentBridge.GetTargetObject(InteractionId);
            if (interaction == null)
            {
                Log.Rpg.Error($"ItemFly interaction with id : {InteractionId} not found");
                EndAction(false);
                return;
            }

            var rewards = MODULE.Item.ParseRewardConf(rewardId);
            if (rewards == null || rewards.Count == 0)
            {
                Log.Rpg.Warning($"ItemFly cityEventId : {cityEventId} rewards is null or empty");
                EndAction(true);
                return;
            }

            ItemFlyToPlayerModule.Instance.ItemFlyToPlayer(rewards, interaction.transform.position, EndAction);
        }
    }
}
