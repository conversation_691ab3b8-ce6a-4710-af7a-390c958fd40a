using System;
using System.Collections.Generic;
using Game.Common;
using Game.Common.Logic.Event;
using Game.View;
using LuaFramework;
using ObservableCollections;
using ParadoxNotion.Design;
using Game.Common;


namespace Game.CityEventSystem
{
    [Name("timeline黑边")]
    public class BlankOutline : ActionBase
    {
        [ParadoxNotion.Design.Header("打开/关闭")]
        public bool IsShow;

        protected override void OnExecute()
        {
            if (IsShow)
            {
                VIEW.OpenUIEx(UIDefine.ComFilmScreenPanel, EmUILayer.CameraLayer);
                EndAction(true);
            }
            else
            {
                if (VIEW.IsPanelVisible(UIDefine.ComFilmScreenPanel))
                {
                    Event.Instance.Broadcast<Action>(EventDefine.BANKL_OUTLINE_COLSE, () => EndAction(true));
                }
                else
                {
                    EndAction(true);
                }
            }
        }
    }
}
