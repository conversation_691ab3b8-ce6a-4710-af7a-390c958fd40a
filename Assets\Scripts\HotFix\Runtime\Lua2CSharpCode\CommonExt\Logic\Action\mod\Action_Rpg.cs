﻿using Game.Battle;
using Game.RPGExploration;
using UnityEngine;

namespace Game.Common
{
    public partial class ActionSystem
    {
        /// <summary>
        /// 显示导航箭头
        /// </summary>
        /// <param name="parameters">params[0]:箭头导航目标类型，params[1]:目标id</param>
        /// <param name="inputParameters"></param>
        public void ShowNavArrow(string[] parameters, object inputParameters)
        {
            if (parameters == null || parameters.Length < 4)
            {
                Log.Game.Error("ShowNavArrow params error");
                return;
            }

            var targetType = (NavArrowTargetType)parameters[0].SafeToInt();
            var targetId = parameters[1];
            var closeType = (NavArrowCloseType)parameters[2].SafeToInt();
            var closeId = parameters[3];
            NavArrowModule.Instance.ShowNavArrow(targetType, targetId, closeType, closeId);
        }

        public void PauseBattle(string[] parameters, object inputParameters)
        {
            UnicornPveControl.GetInstance().Pause = true;
        }
        
        public void ResumeBattle(string[] parameters, object inputParameters)
        {
            UnicornPveControl.GetInstance().Pause = false;
        }
    }
}