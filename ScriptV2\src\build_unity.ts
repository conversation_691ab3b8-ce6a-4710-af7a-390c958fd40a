import {$} from 'zx';
import fs from 'fs-extra';
import {BuildParam} from './build_params.js';
import {clearProject} from './clear_library.js';
import {modifyProjectSettingMacro} from './modify_project_setting_macro.js';
import {replaceManifest} from './replace_manifest.js';
import {delay, logFunctionEnd, logFunctionStart, logWithKey, safeConvert} from "./utility.ts";
import path from "path";
import {Tail} from "tail";
import {Timer} from "./timer.ts";
import {BUILD_UNITY_TIMER} from "./const.ts";


/**
 * 主构建函数
 */
async function buildUnity(buildParam: BuildParam) {
    logFunctionStart('BuildUnity')
    try {
        Timer.start(BUILD_UNITY_TIMER)
        const params = buildParam.getParams();
        const state = buildParam.getState();

        // 处理keepGuest配置
        if (params.keepGuest) {
            await buildParam.changeJsonAttr(
                "Assets/Plugins/Android/main-resource.androidlib/src/main/assets/SDKConfig_Android.json",
                "base.keepGuest",
                `${params.keepGuest}`,
                "Bool"
            );
        }

        // 检查Unity路径
        if (!await fs.pathExists(params.unityPath)) {
            throw new Error(`Unity不存在${params.unityPath}`)
            return;
        }

        // 处理Library清理
        if (params.clearLibrary === true) {
            await clearProject(buildParam);
        }

        // 设置ProjectSetting.asset的宏定义
        await modifyProjectSettingMacro(buildParam);

        // 替换manifest文件
        await replaceManifest(buildParam);

        // 构建参数
        const customArgs = [
            `appVersion=${state.appVersion}`,
            `versionCode=${state.versionCode}`,
            `defineSymbols=${params.defineSymbols}`,
            `platform=${state.platform}`,
            `debug=${params.debug}`,
            `scriptDebug=${params.scriptDebug}`,
            `resVersion=${state.resVersion}`,
            `cfgVersion=${state.cfgVersion}`,
            `protoVersion=${state.protoVersion}`,
            `webServerUrl=${params.webServerUrl}`,
            `buildType=${params.buildType}`,
            `testSDKPay=${params.testSDKPay}`,
            `gateway=${params.gateway}`,
            `gateWayWebSocketPort=${params.gateWayWebSocketPort}`,
            `gateWayTcpPort=${params.gateWayTcpPort}`,
            `serverId=${safeConvert(params.serverId)}`,
            `envName=${state.envName}`,
            `channel=${params.channel}`,
            `productName=${params.productName}`,
            `netType=${params.netType}`,
            `applicationIdentifier=${params.applicationIdentifier}`,
            `envAndroid=${params.envAndroid}`,
            `buildTime=${state.buildTime}`,
            `appDownloadAddress=${params.appDownloadAddress}`,
            `resDownloadAddress=${params.resDownloadAddress}`,
            `confDownloadAddress=${params.confDownloadAddress}`,
            `isGoogleAAB=${params.isGoogleAAB}`,
            `copyBuildinFileOption=${params.copyBuildinFileOption}`,
            `exportProject=${params.exportProject}`,
            `createSymbols=${params.createSymbols}`,
        ].join('*');

        logWithKey('BuildUnity', `customArgs:${customArgs}`);
        //记录可能存在的错误
        let errlogs = new Set<String>();
        let scriptMayBeError = false;
        let logFilePath = path.join(params.projectPath, 'build.log')
        // 创建空文件
        fs.writeFileSync(logFilePath, '', {encoding: 'utf8'});
        const unityProcess = $.spawn(`${params.unityPath}`, [
            '-quit',
            '-batchmode',
            '-projectPath', state.projectPath,
            '-logFile', logFilePath,
            '-executeMethod', 'PackAssetBundle.BuildFromJenkins',
            `-customArgs:${customArgs}`
        ], {
            stdio: 'pipe', // 强制创建流，用于读取 stdout 和 stderr 日志
        });

        // 创建 Tail 实例
        const tail = new Tail(logFilePath, {
            follow: true,      // 持续跟踪文件变化
            fromBeginning: true, // 只读取新增内容，不读取已有内容
            flushAtEOF: true,  // 当到达文件末尾时刷新内容
            useWatchFile: true, // 启用轮询模式
            fsWatchOptions: {
                interval: 100 // 设置轮询间隔（单位：毫秒）
            }
        });

        const errorTags = ['scripts had compiler errors','System.Exception'];
        const errorFilters = [
            'scripts had compiler errors',
            'System.Exception',
            'Building AssetBundle failed',
            'Problem detected while opening the Scene file',
            'Prefab instance problem.',
            'is missing or no valid script is attached',
            'Build task failed : TaskBuilding',
            'IncrementalBuild pipeline build failed',
            'Building AssetBundle failed',
            'does not exist in the current context',
            'are you missing a using directive or an assembly reference',
            'are you missing an assembly reference',
            'Failed to process scene before export:',
        ];
        const errorRegexs = [
            /error CS\d{4}:/g, // 匹配完整的错误信息
        ];


        // 监听新行
        tail.on('line', (line) => {
            logWithKey('Unity Log', `${line}`); // 只输出新增的行
            //处理一些常规的错误
            if (typeof line == 'string') {
                let lineStr = line as string;
                if(errorTags.some((value, index, array)=>lineStr.indexOf(value) >= 0)){
                    scriptMayBeError = true;
                }
                if(errorFilters.some((value, index, array)=>lineStr.indexOf(value) >= 0)){
                    errlogs.add(lineStr);
                }
                // 遍历每个正则表达式
                for (const regex of errorRegexs) {
                    const match = regex.exec(lineStr); // 只匹配一次
                    if (match) {
                        errlogs.add(lineStr);
                    }
                }
            }
        });

        // 监听错误
        tail.on('error', (err) => {
            logWithKey('Unity Log', `❌${err}`);
        });

        // 实时读取 stdout
        unityProcess.stdout.on('data', (data) => {
            const logChunk = data.toString();
            errlogs.add(logChunk);
            logWithKey('unityProcess.stdout', `${logChunk}`);
        });

        // 实时读取 stderr
        unityProcess.stderr.on('data', (data) => {
            const errorChunk = data.toString();
            errlogs.add(errorChunk);
            logWithKey('unityProcess.stderr', `❌${errorChunk}`);
        });

        // 等待子进程结束
        const exitCode = await new Promise<number>((resolve) => {
            unityProcess.on('close', resolve);
        });
        logWithKey('Unity Log', `⏹️⏹️⏹️UnityProcess close!!!`);
        await delay(10000);
        tail.unwatch();
        logWithKey('Unity Log', `🔴🔴🔴️unwatch log file!!!`);
        let logs = Array.from(errlogs);
        if (exitCode === 0) {
            if (scriptMayBeError) {
                throw new Error(`❌ Unity 构建失败\n脚本编译错误！\n${logs.join('\n')}\n`);
            }
            logWithKey('BuildUnity', '✅ Unity 构建成功！');
        } else {
            throw new Error(`❌ Unity 构建失败\n退出码：${exitCode}\n${logs.join('\n')}\n`);
        }
    } catch (error) {
        logWithKey('BuildUnity', `❌${error}`);
        throw error;
    } finally {
        let time = Timer.formatDuration(Timer.end(BUILD_UNITY_TIMER))
        logFunctionEnd('BuildUnity', `duration:${time}`)
    }
}


export {buildUnity};

// async function test()  {
// //测试
//     let buildParam = new BuildParam();
//     let param: Partial<IJenkinsUnityBuildParams> = {
//         'unityPath':'C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.25f1c1\\Editor\\Unity.exe',
//         'projectPath':'D:\\unicorn-u3dclient-csharp',
//         'buildType':BUILD_TYPE.BUILD_APP,
//     }
//     buildParam.updateParams(param);
//     buildParam.updateState('projectPath','D:\\unicorn-u3dclient-csharp')
//     // await buildParam.initState();
//     // buildParam.ShowInfo();
//     console.log(`build start`);
//     try {
//         await buildUnity(buildParam);
//     }catch (e){
//         console.error(`error------:${e}`);
//     }
//     console.log(`build end`);
//
// }
//
// test();
