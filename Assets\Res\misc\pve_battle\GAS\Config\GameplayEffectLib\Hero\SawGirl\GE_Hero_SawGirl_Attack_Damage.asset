%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b9395a0be3e547f48bd1c8320edc6c58, type: 3}
  m_Name: GE_Hero_SawGirl_Attack_Damage
  m_EditorClassIdentifier: 
  Description: 
  DurationPolicy: 1
  Duration: 0
  Period: 0
  PeriodExecution: {fileID: 0}
  DurationFormulaId: 0
  Stacking:
    stackingType: 0
    stackingCodeName: 
    limitCount: 0
    durationRefreshPolicy: 0
    stackDurationHandlingPolicy: 0
    periodResetPolicy: 0
    expirationPolicy: 0
    denyOverflowApplication: 0
    clearStackOnOverflow: 0
    overflowEffects: []
  vFXConfig:
    VfxPrefab: {fileID: 8037895175722604556, guid: c8f83be565e046240bd10b5b6287fb36,
      type: 3}
    IsAttachToTarget: 1
    AttachedPath: dummy1
    Offset: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0}
    Scale: {x: 1, y: 1, z: 1}
    IsGlobalScale: 1
    ActiveWhenAdded: 1
    DestroyTime: 1.5
    RequiredTags: []
    ImmunityTags: []
  GrantedAbilities: []
  AssetTags: []
  GrantedTags: []
  ApplicationRequiredTags:
  - _name: State.IsAlive
    _hashCode: -825449696
    _shortName: IsAlive
    _ancestorHashCodes: 4b461665
    _ancestorNames:
    - State
  OngoingRequiredTags: []
  RemoveGameplayEffectsWithTags: []
  ApplicationImmunityTags: []
  CueOnExecute: []
  CueDurational: []
  CueOnAdd: []
  CueOnRemove: []
  CueOnActivate: []
  CueOnDeactivate: []
  FlowCanvasAssetName: 
  CounterValue: 0
  AttributeCounter: AS_Fight.Poise
  Modifiers:
  - AttributeName: AS_Fight.HP
    AttributeSetName: AS_Fight
    AttributeShortName: HP
    ModiferMagnitude: 0
    Operation: 3
    MMC: {fileID: 11400000, guid: fd6b5b85411eaaa45b430b2e0641f286, type: 2}
  ModifierParametersAttribute: AS_Fight.ATK
  DamageRateSourceType: 1
  FixedDamageSourceType: 0
  DamageRateValue: 10700101
  FixedDamage: 0
  ReleaseRate: 0.2
  ArmorDamageSourceType: 0
  ArmorDamage: 250
  CritRateSourceType: 0
  CritRateOperation: 0
  CritRateFixNum: 0
  CritDamageSourceType: 0
  CritDamageOperation: 0
  CritDamageFixNum: 0
  DefPercentSourceType: 0
  DefPercentFixNum: 0
  TriggerCompareType: 0
  TriggerAttributeName: 
  TriggerPercentAttributeName: 
  than: 0
  thanValue: 0
  SourceThanExecution: []
  OwnerThanExecution: []
  SourceThanAbility: []
  OwnerThanAbility: []
