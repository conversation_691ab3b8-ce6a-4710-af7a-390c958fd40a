declare module 'java-caller' {
    interface JavaCallerOptions {
        jar?: string;
        classPath?: string;
        mainClass?: string;
        rootPath?: string;
    }

    interface JavaCallerResult {
        status: number;
        stdout: string;
        stderr: string;
    }

    export class JavaCaller {
        constructor(options: JavaCallerOptions);
        run(args?: string[]): Promise<JavaCallerResult>;
    }
} 