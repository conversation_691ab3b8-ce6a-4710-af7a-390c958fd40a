import * as fs from 'fs';
//飞书文档
// https://open.feishu.cn/document/feishu-cards/card-components/content-components/rich-text

export interface FeiShuConfig {
    webhook_url: string;
    app_id: string;
    app_secret: string;
}

/**
 * 富文本内容接口
 */
export interface RichTextContent {
    text: string;
    href?: string;
    user_id?: string;
    user_name?: string;
}

/**
 * 卡片按钮接口
 */
export interface CardButton {
    text: string;
    url?: string;
    type?: 'default' | 'primary' | 'danger';
}

/**
 * 卡片模板颜色类型
 */
export type CardTemplate =
    | 'blue' | 'wathet' | 'turquoise'
    | 'green' | 'yellow' | 'orange'
    | 'red' | 'purple' | 'grey';

/**
 * 基础响应接口
 */
export interface FeiShuBaseResponse {
    code: number;
    msg: string;
}

/**
 * Token响应接口
 */
export interface FeiShuTokenResponse extends FeiShuBaseResponse {
    tenant_access_token: string;
    expire: number;
}

/**
 * 图片上传响应接口
 */
export interface FeiShuImageResponse extends FeiShuBaseResponse {
    data: {
        image_key: string;
    }
}

/**
 * 消息发送响应接口
 */
export interface FeiShuMessageResponse extends FeiShuBaseResponse {
    data?: {
        message_id: string;
    }
}

/**
 * 默认的飞书配置
 */
const FEISHU_CONFIG: FeiShuConfig = {
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/8c22f5fc-2eb6-4854-859b-e2ec5f993cd7", // 替换成你的webhook地址
    app_id: "cli_a760205baf3dd01c",      // 替换成你的app_id
    app_secret: "3SWjVAkkUmhYgDCOYQArucedfGMxpFO1"       // 替换成你的app_secret
};

/**
 * 飞书Webhook实现类 (单例模式)
 */
export class FeiShuWebhook {
    private static instance: FeiShuWebhook;
    private readonly config: FeiShuConfig;
    private token: string = '';

    private constructor() {
        this.config = FEISHU_CONFIG;
    }

    /**
     * 获取实例
     */
    private static getInstance(): FeiShuWebhook {
        if (!FeiShuWebhook.instance) {
            FeiShuWebhook.instance = new FeiShuWebhook();
        }
        return FeiShuWebhook.instance;
    }

    /**
     * 获取访问令牌
     * @returns Promise<string> 访问令牌
     */
    private async getAccessToken(): Promise<string> {
        try {
            const response = await fetch(
                'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        app_id: this.config.app_id,
                        app_secret: this.config.app_secret
                    })
                }
            );

            const result = await response.json() as FeiShuTokenResponse;
            if (result.code !== 0) {
                throw new Error(`获取Token失败: ${result.msg}`);
            }

            this.token = result.tenant_access_token;
            return this.token;

        } catch (error) {
            console.error('获取访问令牌失败:', error);
            throw error;
        }
    }

    /**
     * 上传图片
     * @param imagePath 图片路径
     * @returns Promise<string> 图片key
     */
    private async uploadImage(imagePath: string): Promise<string> {
        try {

            if (!fs.existsSync(imagePath)) {
                throw new Error(`图片不存在: ${imagePath}`);
            }

            const token = await this.getAccessToken();

            const formData = new FormData();
            const imageFile = new Blob([fs.readFileSync(imagePath)]);
            formData.append('image', imageFile, 'image.png');
            formData.append('image_type', 'message');

            const response = await fetch(
                'https://open.feishu.cn/open-apis/im/v1/images',
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                }
            );

            const result = await response.json() as FeiShuImageResponse;
            if (result.code !== 0) {
                throw new Error(`上传图片失败: ${result.msg}`);
            }

            return result.data.image_key;
        } catch (error) {
            console.error('上传图片失败:', error);
            throw error;
        }
    }

    /**
     * 发送消息的基础方法
     * @param message 消息内容
     */
    private async sendMessage(message: any): Promise<void> {
        try {
            const response = await fetch(this.config.webhook_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(message)
            });

            const result = await response.json() as FeiShuMessageResponse;
            if (result.code !== 0) {
                throw new Error(`发送失败: ${result.msg}`);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        }
    }

    /**
     * 发送纯文本消息
     * @param text 文本内容
     */
    public static async sendText(text: string): Promise<void> {
        const instance = FeiShuWebhook.getInstance();
        const message = {
            msg_type: "text",
            content: {
                text: text
            }
        };
        await instance.sendMessage(message);
    }

    /**
     * 发送图文消息
     * @param text 文本内容
     * @param imagePath 图片路径
     */
    public static async sendTextWithImage(text: string, imagePath: string): Promise<void> {
        const instance = FeiShuWebhook.getInstance();
        const imageKey = await instance.uploadImage(imagePath);

        const message = {
            msg_type: "post",
            content: {
                post: {
                    zh_cn: {
                        title: "",
                        content: [
                            [{
                                tag: "text",
                                text: text
                            }],
                            [{
                                tag: "img",
                                image_key: imageKey
                            }]
                        ]
                    }
                }
            }
        };

        await instance.sendMessage(message);
    }

    /**
     * 发送富文本消息
     * @param title 标题
     * @param contents 富文本内容数组
     */
    public static async sendRichText(title: string, contents: RichTextContent[]): Promise<void> {
        const instance = FeiShuWebhook.getInstance();
        const richTextElements = contents.map(content => {
            if (content.href) {
                return {
                    tag: "a",
                    text: content.text,
                    href: content.href
                };
            }
            if (content.user_id) {
                return {
                    tag: "at",
                    user_id: content.user_id,
                    user_name: content.user_name || ''
                };
            }
            return {
                tag: "text",
                text: content.text
            };
        });

        const message = {
            msg_type: "post",
            content: {
                post: {
                    zh_cn: {
                        title,
                        content: [richTextElements]
                    }
                }
            }
        };

        await instance.sendMessage(message);
    }
    public static async sendEnhancedCard(params: EnhancedCardMessage): Promise<void> {
        const instance = FeiShuWebhook.getInstance();
        const elements: any[] = [];
    
        // 先添加图片元素（如果有）
        if (params.imagePath) {
            try {
                const imageKey = await instance.uploadImage(params.imagePath);
                elements.push({
                    tag: "img",
                    img_key: imageKey,
                    preview: true,
                    scale_type: "fit_horizontal",
                    alt: {
                        tag: "plain_text",
                        content: ""
                    }
                });
            } catch (error) {
                console.error('Failed to upload image:', error);
            }
        }

        params.contents.forEach((value, index, array)=>{
            // 然后添加文字内容
            elements.push({
                tag: "markdown",
                content: value,
                text_align: "left",
                text_size: "normal"
            });
        });

        const message = {
            msg_type: "interactive",
            card: {
                "schema": "2.0",
                "body": { // 新增 body 字段，elements 属性放置在 body 层级下。
                    "elements": elements // 不再支持 i18n_elements 字段
                },
                "config": {
                    "update_multi": true  // 默认值为 false。
                },
                header: params.title ? {
                    title: {
                        content: params.title,
                        tag: "plain_text"
                    },
                    template: params.template? params.template:"blue", // 可以是 'blue', 'wathet', 'turquoise', 'green', 'yellow', 'orange', 'red', 'purple', 'grey'
                } : undefined
            }
        };
    
        await instance.sendMessage(message);
    }
}
/**
* 增强的卡片消息接口
*/
export interface EnhancedCardMessage {
    title?: string;
    contents: string[];
    imagePath?: string;
    template?: string;
}
// 导出便捷的发送方法
export const sendText = FeiShuWebhook.sendText;
export const sendTextWithImage = FeiShuWebhook.sendTextWithImage;
export const sendRichText = FeiShuWebhook.sendRichText;
export const sendEnhancedCard = FeiShuWebhook.sendEnhancedCard;
