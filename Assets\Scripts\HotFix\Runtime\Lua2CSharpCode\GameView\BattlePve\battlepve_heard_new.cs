

// Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
// Created by elex.
// DateTime: 2024/9/12 16:52

using ELEX.Config;
using Game.Data;
using UnityEditor;
using UnityEngine;

namespace Game.View
{
    public partial class battlepve_heard_new : ViewNode
    {
        private HeroItemData Data;
        private Components Comp;
        
        private class Components
        {
            public hero_star_item_list hero_star_item_list;
        }
        
        public battlepve_heard_new(int id, ViewNode parent) : base(id)
        {
            //var ui = this.UI;
            this.__AutoInitUI();
            OnAttach(parent);
            Comp = new Components();
            var comp = this.Comp;
            // comp.star_list = new hero_star_item_list(this, this.ui.hero_star_item_list_obj, this.ui.hero_star_item_list);
            comp.hero_star_item_list = new hero_star_item_list(this, this.ui.hero_star_item_list_obj, this.ui.hero_star_item_list);
            this.Data = null;
        }

        public override void Dispose()
        {
            base.Dispose();
            this.Data = null;
        }

        public void SetHeroItemData(HeroItemData data, int stars = 0, int level = 1, ConfUnicornHero _cfg = null)
        {
            this.Data = data;
            _cfg = _cfg ?? new ConfUnicornHero();
            var cfg = (data != null && data.Cfg != null) ? data.Cfg : _cfg;

            // 设置头像
            MODULE.Hero.UISetHeroSmallHeadIcon(cfg, this.ui.icon);
            MODULE.Hero.UISetHeroTypeIcon(cfg.type, this.ui.img_type);
            
            // var s = AssetDatabase.LoadAssetAtPath<Sprite>("ui_hero_head_explorer");
            // this.uiObjs.icon.texture = s.texture;
            
            // 设置阵营
            // MODULE.Hero.UISetHeroCampIconMin((cfg as dynamic).camp, this.ui.img_camp);
            
            // 设置品质
            // MODULE.HeroEquip.UISetEquipQualityBg((cfg as dynamic).quality, this.ui.bg);
            MODULE.Hero.UISetHeroQualityBg(cfg.quality, this.ui.bg);
                
            var lv = (data != null) ? data.Level : level;
            
            // 设置等级
            // this.SetTextValue(this.ui.txt_level, string.Format("Lv.{0}", lv));
            // this.uiObjs.txt_level.SetText($"Lv.{lv}");
            SetTextValueTMP(this.ui.txt_level, $"Lv.{lv}");
            
            var star = (data != null) ? data.StarLv : stars;
            // 设置星数
            // (this.Comp.star_list as dynamic).SetStarLevel(star);
            // (this.Comp.hero_star_item_list as dynamic).SetStarLevel(star);
            Comp.hero_star_item_list.SetStarLevel(star);
            
            SetSpecialIconActive(false); // 默认应该隐藏
        }

        /// <summary>
        /// 设置头像置灰
        /// </summary>
        public void SetHeroIconGrey(bool isGrey)
        {
            SetDynTexGray(this.ui.icon, isGrey);
        }
        
        public void SetStarActive(bool isActive)
        {
            if (this.Comp != null && this.Comp.hero_star_item_list != null)
            {
                SetActive(ui.start, isActive);
            }
        }
        
        public void SetLvActive(bool isActive)
        {
            if (this.Comp != null && this.Comp.hero_star_item_list != null)
            {
                SetActive(ui.txt_bg, isActive);
            }
        }
        
        /// <summary>
        /// 设定特殊图标 - 助战英雄的标识等
        /// </summary>
        /// <param name="isActive"></param>
        /// <param name="iconPath"></param>
        public void SetSpecialIconActive(bool isActive, string iconPath = null)
        {
            SetActive(this.ui.support_bg, isActive);
        }
        
    }
}