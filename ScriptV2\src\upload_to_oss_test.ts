import OSS from 'ali-oss';

// 请替换为你的 OSS 配置信息
const client = new OSS({
  region: '<你的-region>',
  accessKeyId: '<你的-accessKeyId>',
  accessKeySecret: '<你的-accessKeySecret>',
  bucket: '<你的-bucket>',
});

// 上传文件（增）
async function uploadFile(objectKey: string, localFilePath: string) {
  try {
    const result = await client.put(objectKey, localFilePath);
    console.log('上传成功:', result.url);
    return result;
  } catch (err) {
    console.error('上传失败:', err);
    throw err;
  }
}

// 删除文件（删）
async function deleteFile(objectKey: string) {
  try {
    const result = await client.delete(objectKey);
    console.log('删除成功:', result);
    return result;
  } catch (err) {
    console.error('删除失败:', err);
    throw err;
  }
}

// 查询文件（查）- 这里以列举 bucket 下的文件为例
async function listFiles(prefix: string = '', maxKeys: number = 10) {
  try {
    const result = await client.list({ prefix, 'max-keys': maxKeys }, {});
    console.log('文件列表:', result.objects);
    return result.objects;
  } catch (err) {
    console.error('查询失败:', err);
    throw err;
  }
}

// 示例调用
(async () => {
  // 上传示例
  // await uploadFile('test-folder/hello.txt', './local/path/to/hello.txt');

  // 删除示例
  // await deleteFile('test-folder/hello.txt');

  // 查询示例
  // await listFiles('test-folder/', 20);
})();

// 导出函数，便于单元测试或外部调用
export { uploadFile, deleteFile, listFiles };
