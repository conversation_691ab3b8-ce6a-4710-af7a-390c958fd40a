using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using ELEX.Config;
using faster.pb.impl;
using Game.Battle;
using Game.CityEventSystem;
using Game.Common;
using Game.FrameWork;
using Game.Net;
using Game.Player;
using Game.Procedure;
using Game.Resource;
using Game.RPGExploration;
using Game.View;
using GAS.Runtime;
using UnityEngine;
using Time = UnityEngine.Time;

#if !DISABLE_SRDEBUGGER
[System.ComponentModel.DisplayName("==默认选项==")]
[SRDebugger.Sort(int.MinValue)]
public partial class SROptions
{
    [Category("玩家信息"), DisplayName("user id"), Sort(0)]
    public string Guid => DATA.User == null ? string.Empty : DATA.User.UserId; 
    
    private string _gm = "gm";

    [Category("GM命令"), DisplayName("输入GM指令参数"), Sort(10)]
    public string GMStr
    {
        get => _gm;
        set => _gm = value;
    }

    [Category("GM命令"), DisplayName("选择GM命令"), Sort(9)]
    public GmCmd GmType { get; set; } = GmCmd.ADD_GOLD;

    [Category("GM命令"), DisplayName("发送GM指令"), Sort(11)]
    public void SendGM()
    {
        List<string> gmStr = GMStr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        CsGMCmd cmd = new CsGMCmd();
        cmd.cmdData = gmStr;
        cmd.cmd = GmType;
        Game.Net.Gm.SendGMCmd(cmd);

        Debug.Log($"发送GM指令：{cmd.cmd}, param{GMStr}");
    }

    #region 道具

    [Category("道具"), DisplayName("资源ID"), Sort(-1)]
    public UResourceType ResType { get; set; } = UResourceType.Berry;

    [Category("道具"), DisplayName("资源数量"), Sort(0)]
    public int ResCount { get; set; } = 0;

    [Category("道具"), DisplayName("添加资源"), Sort(1)]
    public void AddRes()
    {
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = (int)ResType, num = ResCount });
    }

    [Category("道具"), DisplayName("添加所有资源"), Sort(2)]
    public void AddAllRes()
    {
        foreach (UResourceType resType in Enum.GetValues(typeof(UResourceType)))
        {
            Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = (int)resType, num = 10000 });
        }
    }

    [Category("道具"), DisplayName("道具ID"), Sort(100)]
    public int ItemId { get; set; } = 0;

    [Category("道具"), DisplayName("道具数量"), Sort(101)]
    public int ItemCount { get; set; } = 1;

    [Category("道具"), DisplayName("添加道具"), Sort(102)]
    public void AddItem()
    {
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = ItemId, num = ItemCount });
    }

    [Category("道具"), DisplayName("添加测试道具"), Sort(200)]
    public void AddAllItem()
    {
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100201, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100202, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100203, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100204, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100205, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100206, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100207, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100208, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100209, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100301, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100302, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100303, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100304, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100305, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100306, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100307, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100308, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100309, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100401, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100402, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100403, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100404, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100405, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100406, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100407, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100408, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 100409, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102001, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102002, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102003, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102004, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102005, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102006, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102007, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102008, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102009, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102101, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102102, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102103, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102104, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102105, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102106, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102107, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102108, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102301, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102302, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102303, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102304, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102305, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102306, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102307, num = 99 });
        Game.Net.Item.SendTestAddItem(new CsTestAddItem() { specId = 102308, num = 99 });
    }

    #endregion

    #region RPG场景

    [Category("RPG场景"), DisplayName("RPG场景ID"), Sort(100)]
    public int RPGSceneId { get; set; } = 0;
    
    [Category("RPG场景"), DisplayName("进入RPG场景"), Sort(101)]
    public void EnterRPGScene()
    {
        ProcedureHelper.EnterExploration(RPGSceneId);
    }

    #endregion

    #region 事件

    [Category("事件"), DisplayName("事件ID"), Sort(-1)]
    public int CityEventId { get; set; } = 0;

    [Category("事件"), DisplayName("完成事件")]
    public void SendAchieveCityEvent()
    {
        Game.Net.RpgMission.SendAchieveMission(new CsAchieveMission() { missionId = CityEventId });
    }
    
    [Category("事件"), DisplayName("执行事件")]
    public void ExecuteCityEvent()
    {
        CityEventModule.Instance.ExecuteCityEvent(CityEventId, null, false);
    }

    [Category("事件"), DisplayName("开启http服务")]
    public void StartCityEventHttpService()
    {
        CityEventModule.Instance.StartCityEventTreeHttpService();
    }

    [Category("事件"), DisplayName("停止http服务")]
    public void StopCityEventHttpService()
    {
        CityEventModule.Instance.StopCityEventTreeHttpService();
    }

    #endregion

    #region Timeline

    [Category("Timeline"), DisplayName("Timeline Resource"), Sort(200)]
    public string TimelineRes { get; set; } = null;
    
    [Category("Timeline"), DisplayName("Timeline Id"), Sort(201)]
    public string TimelineId { get; set; } = null;

    [Category("Timeline"), DisplayName("isTest"), Sort(202)]
    public bool IsTest { get; set; } = false;

    [Category("Timeline"), DisplayName("播放剧情")]
    public void ShowTimeline()
    {
       // ExplorationModule.Instance.ShowNavArrow(NavArrowTargetType.Building,"905002");
        TimeLineManager.GetInstance().PlayTimeline(TimelineRes, TimelineId, Vector3.zero, (isLoad, timelineUnit) =>
        {
            Debug.Log($"播放剧情：{isLoad}, {timelineUnit.name}");
        },
        () =>
        {
            Debug.Log($"播放剧情结束TimelineRes {TimelineRes} TimelineId {TimelineId}");
        },IsTest);
    }
    [Category("Timeline"), DisplayName("播放特殊剧情")]
    public void ShowSpecialTimeline()
    {
       // ExplorationModule.Instance.ShowNavArrow(NavArrowTargetType.Building,"905002");
        TimeLineManager.GetInstance().PlayTimeline("timeline_se01_3","se01_3", Vector3.zero, (isLoad, timelineUnit) =>
        {
            Debug.Log($"播放剧情：{isLoad}, {timelineUnit.name}");
        },
        () =>
        {
            Debug.Log($"播放剧情结束TimelineRes {TimelineRes} TimelineId {TimelineId}");
        },true);
    }
    #endregion
    #region 声音

    

    [Category("声音"), DisplayName("关闭背景音乐")]
    public void CloseBgm()
    {
      AudioKit.GetInstance().enable_music=false;   
    }
    [Category("声音"), DisplayName("打开背景音乐")]
    public void OpenBgm()
    {
        AudioKit.GetInstance().enable_music=true;   
    }
    [Category("声音"), DisplayName("关闭音效")]
    public void CloseSound()
    {
        AudioKit.GetInstance().enable_sound=false;   
    }
    [Category("声音"), DisplayName("打开音效")]
    public void OpenSound()
    {
        AudioKit.GetInstance().enable_sound=true;   
    }
    
    [Category("声音"), DisplayName("音频event")]
    public string AudioEvent { get; set; } = null;
    
    [Category("声音"), DisplayName("播放音频")]
    public void PlayAudio()
    {
        AudioKit.GetInstance().PlayUISound(AudioEvent);
    }

    #endregion
    #region 语言
    [Category("语言"), DisplayName("语言"), Sort(300)]
    public ELanuage Lanuage { get; set; } = ELanuage.zh_cn;
    
    [Category("语言"), DisplayName("切换语言"), Sort(301)]
    public void SetLanuage()
    {
        Localization.SetLanguage(Lanuage);
    }
    #endregion
    
    #region 新手
    [Category("新手"), DisplayName("id"), Sort(300)]
    public int GuideId { get; set; } = 0;
    
    [Category("新手"), DisplayName("开始引导"), Sort(301)]
    public void StartGuide()
    {
        MODULE.Guide.StartGuide(GuideId, (_isFinish =>
        {
            Log.Test.Info($"guide finish id={GuideId},{_isFinish}");
        }));
    }

    [Category("新手"), DisplayName("停止引导"), Sort(302)]
    public void StopGuide()
    {
        MODULE.Guide.EndGuide();
    }
    
    [Category("新手"), DisplayName("任务id"), Sort(303)]
    public int QuestId { get; set; } = 0;
    
    [Category("新手"), DisplayName("开始任务goto"), Sort(304)]
    public void StartQuestGoto()
    {
        var chapterCfg = ELEX.Config.ConfHelper.GetConfUnicornChapterQuest(QuestId);
        if (chapterCfg == null)
        {
            Log.Game.Error($"GetConfUnicornChapterQuest failed, id{QuestId}");
            return;
        }
            
        Game.Data.Quest.QuestSystem.Instance.OnQuestGoto(QuestId, chapterCfg.questMain, (int)QuestType.ChapterQuest);
    }
    
    [Category("新手"), DisplayName("运行倍速"), Sort(305)]
    public int TimeSpeed { get; set; } = 1;
    
    [Category("新手"), DisplayName("设定运行倍速"), Sort(306)]
    public void SetRunTimeSpeed()
    {
        Time.timeScale = TimeSpeed;
    }
    
    private CancellationTokenSource _cts;
    [Category("新手"), DisplayName("设定运行倍速 - 循环设置"), Sort(307)]
    public void SetRunTimeSpeedContinuous()
    {
        StopRunTimeSpeed();
        _cts = new CancellationTokenSource();
        UniTask.Void(async () =>
        {
            while (!_cts.Token.IsCancellationRequested)
            {
                SetRunTimeSpeed();
                await UniTask.WaitForSeconds(1, true, cancellationToken: _cts.Token);
            }
        });
    }
    
    [Category("新手"), DisplayName("取消设定运行倍速"), Sort(308)]
    public void StopRunTimeSpeed()
    {
        _cts?.Cancel();
    }
    #endregion
    
    #region 迷雾解锁
    [Category("迷雾"), DisplayName("解锁迷雾数量"), Sort(-1)]
    public int UnLockFogNum { get; set; }

    [Category("迷雾"), DisplayName("解锁迷雾")]
    public void UnlockAllFog()
    {
        UnLockFogNum = Mathf.Max(0, UnLockFogNum);
        for (int i = 0; i < UnLockFogNum; i++)
        {
            MODULE.CityFog.SendFogIdToServer(i);
        }
    }
    #endregion

    #region 地块解锁
    [Category("地块"), DisplayName("解锁地块数量"), Sort(-1)]
    public int UnLockAreaNum { get; set; }

    [Category("地块"), DisplayName("解锁地块")]
    public void UnlockAllArea()
    {
        UnLockAreaNum = Mathf.Max(0, UnLockAreaNum);
        var ls = ConfHelper.GetConfBattleFieldNpcList().list;
        for (int i = 0; i < UnLockAreaNum; i++)
        {
            var conf = ls.Find(x => x.area == i);
            if (conf != null)
            {
                Area.SendUnAreaUnlock(new CsUnAreaUnlock { npcId = conf.id });
            }
        }
    }
    #endregion

    #region 角色

    [Category("角色"), DisplayName("玩家位置x"), Sort(302)]
    public float PlayerPositionx { get; set; }

    [Category("角色"), DisplayName("玩家位置y"), Sort(302)]
    public float PlayerPositiony { get; set; }

    [Category("角色"), DisplayName("玩家位置z"), Sort(302)]
    public float PlayerPositionz { get; set; }

    [Category("角色"), DisplayName("设置位置"), Sort(303)]
    public void SetPlayerPosition()
    {
        PlayerModule.Instance.PlayerController.CharacterMotor.SetPosition(new Vector3(PlayerPositionx, PlayerPositiony, PlayerPositionz));
    }

    #endregion
    #region 战斗
    [Category("战斗"), DisplayName("战斗场景Id"), Sort(303)]
    public int PveSceneId { get; set; } = 1;
    [Category("战斗"), DisplayName("开始战斗"), Sort(304)]
    public void BattleStart()
    {
        ProcedureHelper.EnterBattlePve(PveSceneId);
    }
    [Category("战斗"), DisplayName("开启战斗日志"), Sort(304)]
    public bool BattleLog { 
        get {
            if (PlayerPrefs.HasKey("battle_log"))
            {
                GASLog.IsLog = PlayerPrefs.GetInt("battle_log") == 1 ? true : false;
            }
            return GASLog.IsLog;
        }
        set { 
            GASLog.IsLog = value;
            PlayerPrefs.SetInt("battle_log", GASLog.IsLog ? 1 : 0);
        }
    }
    [Category("战斗"), DisplayName("胜利方式结束战斗"), Sort(305)]
    public void BattleEnd()
    {
        UnicornPveControl.GetInstance().BattleStartSlomo(true);
    }
    #endregion
    
    #region UI

    private const string UIOperationKey = "UI操作";
    
    [Category(UIOperationKey), DisplayName("UI名称"), Sort(100)]
    public string UIName { get; set; } = "shop_main_panel";
    // public string UIName { get; set; } = "pet_show_up_panel";
    
    [Category(UIOperationKey), DisplayName("UI参数，逗号(英文)隔开"), Sort(101)]
    // public string UIParam { get; set; } = "1001,1";
    public string UIParam { get; set; } = "";
    
    [Category(UIOperationKey), DisplayName("打开UI"), Sort(102)]
    public void OpenCustomUI()
    {
        VIEW.OpenUICover(UIName, UIParam);
    }
    
    [Category(UIOperationKey), DisplayName("关闭UI"), Sort(103)]
    public void CloseCustomUI()
    {
        VIEW.CloseUI(UIName);
    }

    #endregion

#if ELEX_SDK
    #region EPSDK

    [Category("EPSDK"), DisplayName("EPSDK删除账号"), Sort(400)]
    public void DeleteEPSDKTest()
    {
        // 测试删除EPSDK
        MODULE.Login.DeleteAccountFromSDK();
    }
    
    [Category("EPSDK"), DisplayName("EPSDK恢复账号"), Sort(401)]
    public void RevokeEPSDKTest()
    {
        // 测试撤销EPSDK
        MODULE.Login.RecoverAccountFromSDK();
    }
    
    [Category("EPSDK"), DisplayName("重置游客账号"), Sort(402)]
    public void ResetEPAccount()
    {
        // 测试重置游客账号
        SDKtoLua.ResetAccount();
    }
    

    #endregion
#endif
    
}

#endif
