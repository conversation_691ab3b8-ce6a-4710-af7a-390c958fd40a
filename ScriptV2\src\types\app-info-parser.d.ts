declare module 'app-info-parser' {
    export default class AppInfoParser {
        constructor(filePath: string);
        parse(): Promise<{
            CFBundleShortVersionString?: string;
            CFBundleIdentifier?: string;
            versionName?: string;
            versionCode?: string;
            packageName?: string;
            icon?: string;
            label?: string;
            [key: string]: any;
        }>;
    }
}

declare module 'app-info-parser/src/ipa' {
    export = AppInfoParser;
    class AppInfoParser {
        constructor(filePath: string);
        parse(): Promise<{
            CFBundleShortVersionString?: string;
            CFBundleIdentifier?: string;
            [key: string]: any;
        }>;
    }
} 