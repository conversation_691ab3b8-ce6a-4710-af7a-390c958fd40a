using R3;
using Sirenix.OdinInspector;
using UnityEngine;

public class ExploreStatisticData
{
    /// <summary>
    /// 统计ID
    /// </summary>
    [SerializeField, LabelText("统计ID")]
    public int StatisticId;

    /// <summary>
    /// 统计总数
    /// </summary>
    [SerializeField, LabelText("统计总数")]
    public double StatisticTotalCount;

    /// <summary>
    /// 图标名称
    /// </summary>
    public string IconName { get; set; }
    
    /// <summary>
    /// 统计完成是否隐藏
    /// </summary>
    [SerializeField, LabelText("统计完成是否隐藏")]
    public bool IsHideWhenCompleted = true;

    /// <summary>
    /// 当前数量
    /// </summary>
    [HideInInspector]
    public readonly ReactiveProperty<double> CurrentCount = new ReactiveProperty<double>();
}

