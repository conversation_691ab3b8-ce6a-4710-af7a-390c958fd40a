using System.Collections;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;

namespace Game.CityEventSystem
{
    [Name("调试/打印")]
    public class LogInfo : ActionBase
    {
        [ParadoxNotion.Design.Header("打印内容")]
        public string LogContent;
        
        protected override void OnExecute()
        {
            Game.Common.Log.EventSystem.Info(LogContent);
            EndAction(true);
        }
    }
}