export const LOG_TAG = "[PACK]"

export const J<PERSON><PERSON><PERSON>_BUILD_TIMER = "JEN<PERSON><PERSON>_BUILD_TIMER"
export const BUILD_UNITY_TIMER = "BUILD_UNITY_TIMER"
//总控uri
export const CENTRAL_CONTRAL_WEB_URL = `http://test-tech-web.elex-tech.net`
//修改总控的项目ID
export const CENTRAL_CONTRAL_PROJECT_ID= `10000010`
//修改总控的项目key
export const CENTRAL_CONTRAL_OPS_KEY=`123456`

/**
 * 平台常量
 */
export const PLATFORM = {
    PC: 'pc',
    MAC: 'mac',
    ANDROID: 'android',
    IOS: 'ios',
    WEBGL: 'webgl',
    WeixinMiniGame: 'weixinminigame'
}

/**
 * 将平台转成camel形式
 * @param input
 */
export const platformCamelize = (input: string): string => {
    if(input === PLATFORM.ANDROID){
        return 'Android'
    }
    if(input === PLATFORM.IOS){
        return 'iOS'
    }
    if(input === PLATFORM.WEBGL){
        return 'WebGL'
    }
    if(input === PLATFORM.WeixinMiniGame){
        return 'WeixinMiniGame'
    }
    if(input === PLATFORM.MAC){
        return 'StandaloneOSX'
    }
    // else if(input === PLATFORM.IOS){
    //
    // }
    return input;
};

/**
 * 构建类型常量
 */
export const BUILD_TYPE = {
    BUILD_ALL: 'BuildAll',
    BUILD_RES: `BuildRes`,
    BUILD_APP: 'BuildApp',
    BUILD_CONFIG: 'BuildConfig',
    BUILD_LUA: 'BuildLua',
    BUILD_HYBRID_CLR: 'BuildHybridCLR'
}

/**
 * 定义cdn保存版本的前缀
 */
export const CDN_VERSION_PREFIX = {
    BUILD_EXE : 'exe',
    BUILD_PROJECT : 'project',
    BUILD_CONF : 'conf',
    BUILD_RES : 'res',
    BUILD_FONT : 'font',
}

/**
 * Jenkins用户名到飞书用户ID的映射表
 */
type JenkinsToFeishuMap = Record<string, string>;
export const JENKINS_TO_FEISHU_USER_MAPPING:JenkinsToFeishuMap =  {
    'zhuyanncai': '7408837868092227587', // 替换为实际的飞书用户ID
    // 添加更多用户映射...
};
