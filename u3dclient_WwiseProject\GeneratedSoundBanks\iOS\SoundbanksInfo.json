{"SoundBanksInfo": {"Platform": "iOS", "BasePlatform": "iOS", "SchemaVersion": "16", "SoundBankVersion": "150", "RootPaths": {"ProjectRoot": "../..", "SourceFilesRoot": "../../.cache/iOS", "SoundBanksRoot": ".", "ExternalSourcesInputFile": "", "ExternalSourcesOutputRoot": "."}, "DialogueEvents": [], "SoundBanks": [{"Id": "*********", "Type": "User", "Language": "SFX", "Hash": "{1CC3CC9D-0861-8DA7-0CF8-40FDC518213D}", "ShortName": "CommonBank", "Path": "CommonBank.bnk", "Media": [{"Id": "6103003", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_clilmbing_ladder.wav", "CachePath": "SFX/sfx_rpg_man_clilmbing_ladder_A477CA7B.wem"}, {"Id": "7275739", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_water_01.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_water_01_A477CA7B.wem"}, {"Id": "9574061", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_attack_1_03.wav", "CachePath": "SFX/sfx_hero_rowen_attack_1_03_A477CA7B.wem"}, {"Id": "********", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_attack_1_01.wav", "CachePath": "SFX/sfx_hero_rowen_attack_1_01_A477CA7B.wem"}, {"Id": "********", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_fire_spread_burn.wav", "CachePath": "SFX/sfx_cutScene_fire_spread_burn_A477CA7B.wem"}, {"Id": "29505024", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_pet_blow_start.wav", "CachePath": "SFX/sfx_rpg_pet_blow_start_A477CA7B.wem"}, {"Id": "29833038", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_01.wav", "CachePath": "SFX/sfx_hero_annie_attack_01_A477CA7B.wem"}, {"Id": "33249628", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_skill.wav", "CachePath": "SFX/sfx_hero_lina_skill_A477CA7B.wem"}, {"Id": "33596105", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_fire_small_burn.wav", "CachePath": "SFX/sfx_cutScene_fire_small_burn_A50E84B9.wem"}, {"Id": "38087952", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_grass_03.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_grass_03_A477CA7B.wem"}, {"Id": "44283607", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_hit_03.wav", "CachePath": "SFX/sfx_hero_lina_hit_03_A477CA7B.wem"}, {"Id": "47347581", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_vulture_skill.wav", "CachePath": "SFX/sfx_monster_vulture_skill_A477CA7B.wem"}, {"Id": "52040472", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_si<PERSON>ra_attack_hit_03.wav", "CachePath": "SFX/sfx_hero_sierra_attack_hit_03_A477CA7B.wem"}, {"Id": "52282061", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_attack_01.wav", "CachePath": "SFX/sfx_hero_lina_attack_01_A477CA7B.wem"}, {"Id": "54921207", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_2_03.wav", "CachePath": "SFX/sfx_hero_vivi_attack_2_03_A477CA7B.wem"}, {"Id": "55185885", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_attack.wav", "CachePath": "SFX/sfx_cutScene_bear_attack_A477CA7B.wem"}, {"Id": "55433167", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_snakeking_skill.wav", "CachePath": "SFX/sfx_monster_snakeking_skill_A477CA7B.wem"}, {"Id": "56135970", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabe<PERSON>_attack_hit_02.wav", "CachePath": "SFX/sfx_hero_isabe<PERSON>_attack_hit_02_A477CA7B.wem"}, {"Id": "57899251", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_groud_collapse.wav", "CachePath": "SFX/sfx_cutScene_groud_collapse_A477CA7B.wem"}, {"Id": "60291331", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_hit.wav", "CachePath": "SFX/sfx_cutScene_orang_hit_A477CA7B.wem"}, {"Id": "64961667", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bat_skill.wav", "CachePath": "SFX/sfx_monster_bat_skill_A477CA7B.wem"}, {"Id": "69039270", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_mushroom_02.wav", "CachePath": "SFX/sfx_rpg_man_collect_mushroom_02_A477CA7B.wem"}, {"Id": "71166277", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_water_03.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_water_03_A477CA7B.wem"}, {"Id": "75375983", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_skill_attack.wav", "CachePath": "SFX/sfx_hero_isabella_skill_attack_A477CA7B.wem"}, {"Id": "75547551", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_open_bigchest.wav", "CachePath": "SFX/sfx_rpg_man_open_bigchest_A477CA7B.wem"}, {"Id": "80439985", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_open_luxurychest.wav", "CachePath": "SFX/sfx_rpg_man_open_luxurychest_A477CA7B.wem"}, {"Id": "81238408", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_bombexplode.wav", "CachePath": "SFX/sfx_hero_annie_attack_bombexplode_A477CA7B.wem"}, {"Id": "81450364", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_pay_succeed_token.wav", "CachePath": "SFX/sfx_notice_pay_succeed_token_A477CA7B.wem"}, {"Id": "82143151", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabe<PERSON>_attack_hit_01.wav", "CachePath": "SFX/sfx_hero_isabe<PERSON>_attack_hit_01_A477CA7B.wem"}, {"Id": "83847796", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_labelSwitch.wav", "CachePath": "SFX/sfx_ui_panel_labelSwitch_A477CA7B.wem"}, {"Id": "89726666", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_push_turtle_loop.wav", "CachePath": "SFX/sfx_rpg_man_push_turtle_loop_C925BEA4.wem"}, {"Id": "96894957", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_hit_02.wav", "CachePath": "SFX/sfx_hero_lina_hit_02_A477CA7B.wem"}, {"Id": "97636415", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bee_laugh.wav", "CachePath": "SFX/sfx_cutScene_bee_laugh_A477CA7B.wem"}, {"Id": "100042163", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_positive.wav", "CachePath": "SFX/sfx_notice_positive_A477CA7B.wem"}, {"Id": "102780360", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_1_03.wav", "CachePath": "SFX/sfx_hero_vivi_attack_1_03_A477CA7B.wem"}, {"Id": "102837247", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_tree_spirit_jump_03.wav", "CachePath": "SFX/sfx_cutScene_tree_spirit_jump_03_A477CA7B.wem"}, {"Id": "103152751", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_bombset.wav", "CachePath": "SFX/sfx_hero_annie_attack_bombset_A477CA7B.wem"}, {"Id": "108228392", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_crystal_charging.wav", "CachePath": "SFX/sfx_cutScene_crystal_charging_A50E84B9.wem"}, {"Id": "112896267", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_construct_building.wav", "CachePath": "SFX/sfx_rpg_man_construct_building_A477CA7B.wem"}, {"Id": "115438071", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolf_attack_03.wav", "CachePath": "SFX/sfx_monster_wolf_attack_03_00FF4926.wem"}, {"Id": "120352083", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_attack.wav", "CachePath": "SFX/sfx_monster_gorilla_attack_A477CA7B.wem"}, {"Id": "126656759", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_2_01.wav", "CachePath": "SFX/sfx_hero_dante_attack_2_01_A477CA7B.wem"}, {"Id": "127217317", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_boat_sailing_loop.wav", "CachePath": "SFX/sfx_rpg_boat_sailing_loop_A477CA7B.wem"}, {"Id": "132961897", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spiderelite_skillstart.wav", "CachePath": "SFX/sfx_monster_spiderelite_skillstart_A477CA7B.wem"}, {"Id": "136167884", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_skill2_2.wav", "CachePath": "SFX/sfx_hero_lilith_skill2_2_A477CA7B.wem"}, {"Id": "136448081", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_2_02.wav", "CachePath": "SFX/sfx_hero_dante_attack_2_02_A477CA7B.wem"}, {"Id": "141779687", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_hit_02.wav", "CachePath": "SFX/sfx_hero_annie_attack_hit_02_A477CA7B.wem"}, {"Id": "141796949", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_cutTree_01.wav", "CachePath": "SFX/sfx_rpg_man_cutTree_01_A477CA7B.wem"}, {"Id": "142707063", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_pet_squirtWater.wav", "CachePath": "SFX/sfx_rpg_pet_squirtWater_A477CA7B.wem"}, {"Id": "144733970", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_break_airplane.wav", "CachePath": "SFX/sfx_rpg_break_airplane_A477CA7B.wem"}, {"Id": "147788772", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_chicken_catched_01.wav", "CachePath": "SFX/sfx_animation_chicken_catched_01_A477CA7B.wem"}, {"Id": "152830642", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_animation_rpg_items_fly.wav", "CachePath": "SFX/sfx_ui_animation_rpg_items_fly_A477CA7B.wem"}, {"Id": "155182812", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_conch.wav", "CachePath": "SFX/sfx_rpg_man_collect_conch_A477CA7B.wem"}, {"Id": "156869024", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_run_01.wav", "CachePath": "SFX/sfx_cutScene_orang_run_01_A477CA7B.wem"}, {"Id": "157206279", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_rpg_getCrystalShards_01.wav", "CachePath": "SFX/sfx_notice_rpg_getCrystalShards_01_A477CA7B.wem"}, {"Id": "160779747", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_se_addHero.wav", "CachePath": "SFX/sfx_notice_se_addHero_A477CA7B.wem"}, {"Id": "161913924", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_run_05.wav", "CachePath": "SFX/sfx_cutScene_orang_run_05_A477CA7B.wem"}, {"Id": "162975851", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_attack_03.wav", "CachePath": "SFX/sfx_hero_isabella_attack_03_A477CA7B.wem"}, {"Id": "163887531", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_clilmbing_vine.wav", "CachePath": "SFX/sfx_rpg_man_clilmbing_vine_A477CA7B.wem"}, {"Id": "169066047", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_mushroom_03.wav", "CachePath": "SFX/sfx_rpg_man_collect_mushroom_03_A477CA7B.wem"}, {"Id": "171862592", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_1_02.wav", "CachePath": "SFX/sfx_hero_dante_attack_1_02_A477CA7B.wem"}, {"Id": "178917646", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_common_battle_lose.wav", "CachePath": "SFX/sfx_notice_common_battle_lose_A477CA7B.wem"}, {"Id": "181648324", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_common_battle_win.wav", "CachePath": "SFX/sfx_notice_common_battle_win_A477CA7B.wem"}, {"Id": "182384876", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_small_close.wav", "CachePath": "SFX/sfx_ui_panel_small_close_A477CA7B.wem"}, {"Id": "188205898", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_water_02.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_water_02_A477CA7B.wem"}, {"Id": "189609105", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_walk_01.wav", "CachePath": "SFX/sfx_cutScene_bear_walk_01_A477CA7B.wem"}, {"Id": "193839726", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bear_attack.wav", "CachePath": "SFX/sfx_monster_bear_attack_A477CA7B.wem"}, {"Id": "194350357", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_wolf_roar.wav", "CachePath": "SFX/sfx_rpg_wolf_roar_A477CA7B.wem"}, {"Id": "201196212", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_bee_scare.wav", "CachePath": "SFX/sfx_rpg_bee_scare_A477CA7B.wem"}, {"Id": "203736238", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_pve_missionAccomplished.wav", "CachePath": "SFX/sfx_notice_pve_missionAccomplished_A477CA7B.wem"}, {"Id": "210469521", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_roar.wav", "CachePath": "SFX/sfx_cutScene_bear_roar_A477CA7B.wem"}, {"Id": "210851714", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_pve_skill.wav", "CachePath": "SFX/sfx_ui_button_pve_skill_A477CA7B.wem"}, {"Id": "215082828", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_run_04.wav", "CachePath": "SFX/sfx_cutScene_bear_run_04_A477CA7B.wem"}, {"Id": "218059986", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_vulture_angry_call.wav", "CachePath": "SFX/sfx_rpg_vulture_angry_call_A477CA7B.wem"}, {"Id": "218610223", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_dialog_next.wav", "CachePath": "SFX/sfx_ui_panel_dialog_next_A477CA7B.wem"}, {"Id": "219512163", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spider_attack.wav", "CachePath": "SFX/sfx_monster_spider_attack_A477CA7B.wem"}, {"Id": "225689011", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_rpg_getCrystalShards_03.wav", "CachePath": "SFX/sfx_notice_rpg_getCrystalShards_03_A477CA7B.wem"}, {"Id": "230320031", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_ore_complete.wav", "CachePath": "SFX/sfx_rpg_man_collect_ore_complete_A477CA7B.wem"}, {"Id": "234377884", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_shoot_ground.wav", "CachePath": "SFX/sfx_cutScene_bear_shoot_ground_A477CA7B.wem"}, {"Id": "235978478", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_troop_attack.wav", "CachePath": "SFX/sfx_cutScene_troop_attack_A477CA7B.wem"}, {"Id": "237541992", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_skill2.wav", "CachePath": "SFX/sfx_hero_isabella_skill2_A477CA7B.wem"}, {"Id": "239523656", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_attack_02.wav", "CachePath": "SFX/sfx_hero_lina_attack_02_A477CA7B.wem"}, {"Id": "242385067", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_run_02.wav", "CachePath": "SFX/sfx_cutScene_orang_run_02_A477CA7B.wem"}, {"Id": "245856841", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_getReward_open.wav", "CachePath": "SFX/sfx_ui_panel_getReward_open_A477CA7B.wem"}, {"Id": "249875023", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_berries_02.wav", "CachePath": "SFX/sfx_rpg_man_collect_berries_02_A477CA7B.wem"}, {"Id": "257672328", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_attacksuccess.wav", "CachePath": "SFX/sfx_monster_gorilla_attacksuccess_A477CA7B.wem"}, {"Id": "260546809", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_rage.wav", "CachePath": "SFX/sfx_monster_gorilla_rage_A477CA7B.wem"}, {"Id": "263012307", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_open_luxurychest_Instant.wav", "CachePath": "SFX/sfx_rpg_man_open_luxurychest_Instant_A477CA7B.wem"}, {"Id": "263408997", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_slate_shakes.wav", "CachePath": "SFX/sfx_cutScene_slate_shakes_A477CA7B.wem"}, {"Id": "278319306", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_3_03.wav", "CachePath": "SFX/sfx_hero_zoe_attack_3_03_A477CA7B.wem"}, {"Id": "281681615", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_skill_end.wav", "CachePath": "SFX/sfx_hero_isabella_skill_end_A477CA7B.wem"}, {"Id": "284047166", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolfking_attack_02.wav", "CachePath": "SFX/sfx_monster_wolfking_attack_02_A477CA7B.wem"}, {"Id": "284611539", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_ranged.wav", "CachePath": "SFX/sfx_monster_gorilla_ranged_A477CA7B.wem"}, {"Id": "284730522", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_ladder_break.wav", "CachePath": "SFX/sfx_cutScene_ladder_break_A477CA7B.wem"}, {"Id": "287995186", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_scene_ele_rpg_plantGrowing.wav", "CachePath": "SFX/sfx_scene_ele_rpg_plantGrowing_A477CA7B.wem"}, {"Id": "299002274", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bee_stung.wav", "CachePath": "SFX/sfx_cutScene_bee_stung_A477CA7B.wem"}, {"Id": "302271482", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_skill_1.wav", "CachePath": "SFX/sfx_hero_vivi_skill_1_A477CA7B.wem"}, {"Id": "307271184", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_treasureBox_open.wav", "CachePath": "SFX/sfx_notice_treasureBox_open_A477CA7B.wem"}, {"Id": "309117333", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_run_01.wav", "CachePath": "SFX/sfx_cutScene_bear_run_01_A477CA7B.wem"}, {"Id": "312218708", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_plank_01.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_plank_01_A477CA7B.wem"}, {"Id": "312913805", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_getReward_close.wav", "CachePath": "SFX/sfx_ui_panel_getReward_close_A477CA7B.wem"}, {"Id": "313318960", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_fecruit_fire.wav", "CachePath": "SFX/sfx_ui_fecruit_fire_A477CA7B.wem"}, {"Id": "314056286", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_volcano_eruption.wav", "CachePath": "SFX/sfx_cutScene_volcano_eruption_A50E84B9.wem"}, {"Id": "318329169", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_stone_drop_in_lava.wav", "CachePath": "SFX/sfx_cutScene_stone_drop_in_lava_A477CA7B.wem"}, {"Id": "320111903", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_death.wav", "CachePath": "SFX/sfx_hero_vivi_death_A477CA7B.wem"}, {"Id": "323837174", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_skill.wav", "CachePath": "SFX/sfx_hero_rowen_skill_A477CA7B.wem"}, {"Id": "325962657", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_push_stone.wav", "CachePath": "SFX/sfx_cutScene_push_stone_A477CA7B.wem"}, {"Id": "329169851", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_hyena_fall_down.wav", "CachePath": "SFX/sfx_cutScene_hyena_fall_down_A477CA7B.wem"}, {"Id": "332039537", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_rpg_getPet.wav", "CachePath": "SFX/sfx_notice_rpg_getPet_A477CA7B.wem"}, {"Id": "351522230", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dragon_breathing_fireball.wav", "CachePath": "SFX/sfx_cutScene_dragon_breathing_fireball_A50E84B9.wem"}, {"Id": "351812292", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spiderelite_skill_success.wav", "CachePath": "SFX/sfx_monster_spiderelite_skill_success_A477CA7B.wem"}, {"Id": "353340842", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_skill.wav", "CachePath": "SFX/sfx_hero_lilith_skill_A477CA7B.wem"}, {"Id": "354328960", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_beatcheast.wav", "CachePath": "SFX/sfx_cutScene_orang_beatcheast_A477CA7B.wem"}, {"Id": "358900373", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolfking_skill1.wav", "CachePath": "SFX/sfx_monster_wolfking_skill1_A477CA7B.wem"}, {"Id": "360941261", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_tree_spirit_jump_04.wav", "CachePath": "SFX/sfx_cutScene_tree_spirit_jump_04_A477CA7B.wem"}, {"Id": "363657281", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dragon_roar.wav", "CachePath": "SFX/sfx_cutScene_dragon_roar_A477CA7B.wem"}, {"Id": "372647210", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_common.wav", "CachePath": "SFX/sfx_ui_button_common_A477CA7B.wem"}, {"Id": "374378373", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_tree_spirit_jump_02.wav", "CachePath": "SFX/sfx_cutScene_tree_spirit_jump_02_A477CA7B.wem"}, {"Id": "382013667", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_1_03.wav", "CachePath": "SFX/sfx_hero_zoe_attack_1_03_A477CA7B.wem"}, {"Id": "383342016", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_vulture_normal_call.wav", "CachePath": "SFX/sfx_rpg_vulture_normal_call_A477CA7B.wem"}, {"Id": "383910358", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_pve_hero_add.wav", "CachePath": "SFX/sfx_ui_button_pve_hero_add_A477CA7B.wem"}, {"Id": "386869349", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_common_open.wav", "CachePath": "SFX/sfx_ui_panel_common_open_A477CA7B.wem"}, {"Id": "387319348", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_se_startGame.wav", "CachePath": "SFX/sfx_notice_se_startGame_A477CA7B.wem"}, {"Id": "389514283", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_3_02.wav", "CachePath": "SFX/sfx_hero_zoe_attack_3_02_A477CA7B.wem"}, {"Id": "389786303", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_fire_extinguish.wav", "CachePath": "SFX/sfx_cutScene_fire_extinguish_A477CA7B.wem"}, {"Id": "390802556", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bear_skill1.wav", "CachePath": "SFX/sfx_monster_bear_skill1_A477CA7B.wem"}, {"Id": "391729555", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_spider_web_burn.wav", "CachePath": "SFX/sfx_animation_spider_web_burn_A50E84B9.wem"}, {"Id": "397250989", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_attack_1_02.wav", "CachePath": "SFX/sfx_hero_rowen_attack_1_02_A477CA7B.wem"}, {"Id": "399993586", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_common_close.wav", "CachePath": "SFX/sfx_ui_panel_common_close_A477CA7B.wem"}, {"Id": "402184142", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_landing.wav", "CachePath": "SFX/sfx_cutScene_orang_landing_A477CA7B.wem"}, {"Id": "405084286", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_skill_summon.wav", "CachePath": "SFX/sfx_hero_annie_skill_summon_A477CA7B.wem"}, {"Id": "408157936", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_run_05.wav", "CachePath": "SFX/sfx_cutScene_bear_run_05_A477CA7B.wem"}, {"Id": "413081790", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_si<PERSON>ra_attack_hit_01.wav", "CachePath": "SFX/sfx_hero_sierra_attack_hit_01_A477CA7B.wem"}, {"Id": "421882104", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_fence_attacked.wav", "CachePath": "SFX/sfx_cutScene_fence_attacked_A477CA7B.wem"}, {"Id": "425800599", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_skill2.wav", "CachePath": "SFX/sfx_hero_rowen_skill2_A477CA7B.wem"}, {"Id": "434179014", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_1_01.wav", "CachePath": "SFX/sfx_hero_vivi_attack_1_01_A477CA7B.wem"}, {"Id": "435433257", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_pve_hero_remove.wav", "CachePath": "SFX/sfx_ui_button_pve_hero_remove_A477CA7B.wem"}, {"Id": "435542606", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_vulture_skillsuccess.wav", "CachePath": "SFX/sfx_monster_vulture_skillsuccess_A477CA7B.wem"}, {"Id": "449236370", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_attack_1_03.wav", "CachePath": "SFX/sfx_hero_lilith_attack_1_03_A477CA7B.wem"}, {"Id": "455155676", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_hit_03.wav", "CachePath": "SFX/sfx_hero_annie_attack_hit_03_A477CA7B.wem"}, {"Id": "456848626", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_rpg_click_bubble.wav", "CachePath": "SFX/sfx_ui_button_rpg_click_bubble_A477CA7B.wem"}, {"Id": "457821505", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_monkey_attacksuccess.wav", "CachePath": "SFX/sfx_monster_monkey_attacksuccess_A477CA7B.wem"}, {"Id": "464547566", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_tree_spirit_walk.wav", "CachePath": "SFX/sfx_animation_tree_spirit_walk_A50E84B9.wem"}, {"Id": "466308787", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "music_rpg_volcano.wav", "Path": "Media/466308787.wem", "CachePath": "SFX/music_rpg_volcano_E8CC8B21.wem"}, {"Id": "467723753", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bat_skill_fail.wav", "CachePath": "SFX/sfx_monster_bat_skill_fail_A477CA7B.wem"}, {"Id": "474527836", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_fecruit_box.wav", "CachePath": "SFX/sfx_ui_fecruit_box_A477CA7B.wem"}, {"Id": "478002122", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_plank_03.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_plank_03_A477CA7B.wem"}, {"Id": "481169991", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_hit_02.wav", "CachePath": "SFX/sfx_hero_dante_attack_hit_02_A477CA7B.wem"}, {"Id": "481465004", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_skill_2.wav", "CachePath": "SFX/sfx_hero_dante_skill_2_A477CA7B.wem"}, {"Id": "483527323", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_flap_02.wav", "CachePath": "SFX/sfx_cutScene_paimon_flap_02_A477CA7B.wem"}, {"Id": "484719282", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_pve_missionStart.wav", "CachePath": "SFX/sfx_notice_pve_missionStart_A477CA7B.wem"}, {"Id": "484913630", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "music_rpg_cove.wav", "Path": "Media/484913630.wem", "CachePath": "SFX/music_rpg_cove_E8CC8B21.wem"}, {"Id": "487354823", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_rage_attack.wav", "CachePath": "SFX/sfx_monster_gorilla_rage_attack_A477CA7B.wem"}, {"Id": "487732415", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "music_rpg_forest_01.wav", "Path": "Media/487732415.wem", "CachePath": "SFX/music_rpg_forest_01_E8CC8B21.wem"}, {"Id": "488961693", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_sand_01.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_sand_01_A477CA7B.wem"}, {"Id": "492981583", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_hit_01.wav", "CachePath": "SFX/sfx_hero_lina_hit_01_A477CA7B.wem"}, {"Id": "493093430", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_collect_iron_ore_complete.wav", "CachePath": "SFX/sfx_notice_collect_iron_ore_complete_A477CA7B.wem"}, {"Id": "502017925", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_rpg_blowGas.wav", "CachePath": "SFX/sfx_animation_rpg_blowGas_A477CA7B.wem"}, {"Id": "502420198", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_attack_02.wav", "CachePath": "SFX/sfx_hero_isabella_attack_02_A477CA7B.wem"}, {"Id": "502605230", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_chicken_catched_03.wav", "CachePath": "SFX/sfx_animation_chicken_catched_03_A477CA7B.wem"}, {"Id": "508755055", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolfking_skill2.wav", "CachePath": "SFX/sfx_monster_wolfking_skill2_A477CA7B.wem"}, {"Id": "509373921", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_landing.wav", "CachePath": "SFX/sfx_cutScene_landing_A477CA7B.wem"}, {"Id": "517660813", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bee_flying.wav", "CachePath": "SFX/sfx_cutScene_bee_flying_A50E84B9.wem"}, {"Id": "521174348", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_skill2_3.wav", "CachePath": "SFX/sfx_hero_lilith_skill2_3_A477CA7B.wem"}, {"Id": "521260712", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_button_se_startGame.wav", "CachePath": "SFX/sfx_ui_button_se_startGame_A477CA7B.wem"}, {"Id": "522465123", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_skill2.wav", "CachePath": "SFX/sfx_hero_dante_skill2_A477CA7B.wem"}, {"Id": "526805455", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_collect_iron_ore_02.wav", "CachePath": "SFX/sfx_animation_collect_iron_ore_02_A477CA7B.wem"}, {"Id": "533044395", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_rage_skill.wav", "CachePath": "SFX/sfx_monster_gorilla_rage_skill_A477CA7B.wem"}, {"Id": "544145033", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_boat_puffUp.wav", "CachePath": "SFX/sfx_rpg_boat_puffUp_A477CA7B.wem"}, {"Id": "549215730", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_collect_iron_ore_01.wav", "CachePath": "SFX/sfx_animation_collect_iron_ore_01_A477CA7B.wem"}, {"Id": "552950655", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_1_03.wav", "CachePath": "SFX/sfx_hero_dante_attack_1_03_A477CA7B.wem"}, {"Id": "553602955", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_negative.wav", "CachePath": "SFX/sfx_notice_negative_A477CA7B.wem"}, {"Id": "556446041", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_skill_start.wav", "CachePath": "SFX/sfx_hero_isabella_skill_start_A477CA7B.wem"}, {"Id": "562810313", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_scene_build_bridge.wav", "CachePath": "SFX/sfx_scene_build_bridge_A477CA7B.wem"}, {"Id": "566913848", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_si<PERSON>ra_attack_hit_02.wav", "CachePath": "SFX/sfx_hero_sierra_attack_hit_02_A477CA7B.wem"}, {"Id": "567515101", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_halloa.wav", "CachePath": "SFX/sfx_cutScene_paimon_halloa_A477CA7B.wem"}, {"Id": "574380731", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_grass_02.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_grass_02_A477CA7B.wem"}, {"Id": "574485244", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_skill_1.wav", "CachePath": "SFX/sfx_hero_dante_skill_1_A477CA7B.wem"}, {"Id": "584186618", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_1_02.wav", "CachePath": "SFX/sfx_hero_vivi_attack_1_02_A477CA7B.wem"}, {"Id": "586476684", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_attack_1_01.wav", "CachePath": "SFX/sfx_hero_lilith_attack_1_01_A477CA7B.wem"}, {"Id": "589136421", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_walk_03.wav", "CachePath": "SFX/sfx_cutScene_bear_walk_03_A477CA7B.wem"}, {"Id": "590201357", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dog_eat.wav", "CachePath": "SFX/sfx_cutScene_dog_eat_A477CA7B.wem"}, {"Id": "591317335", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "sfx_amb_rpg_cave.wav", "Path": "Media/591317335.wem", "CachePath": "SFX/sfx_amb_rpg_cave_A50E84B9.wem"}, {"Id": "592222445", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_gorilla_eat_crystal.wav", "CachePath": "SFX/sfx_rpg_gorilla_eat_crystal_A477CA7B.wem"}, {"Id": "593955212", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_cutTree_02.wav", "CachePath": "SFX/sfx_rpg_man_cutTree_02_A477CA7B.wem"}, {"Id": "593964798", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "music_se_forest.wav", "Path": "Media/593964798.wem", "CachePath": "SFX/music_se_forest_E8CC8B21.wem"}, {"Id": "594121427", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_skill_break.wav", "CachePath": "SFX/sfx_monster_gorilla_skill_break_A477CA7B.wem"}, {"Id": "599058672", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bear_skill_break.wav", "CachePath": "SFX/sfx_monster_bear_skill_break_A477CA7B.wem"}, {"Id": "604157030", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_run_03.wav", "CachePath": "SFX/sfx_cutScene_bear_run_03_A477CA7B.wem"}, {"Id": "604867087", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_hit_03.wav", "CachePath": "SFX/sfx_hero_dante_attack_hit_03_A477CA7B.wem"}, {"Id": "607100907", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_character_common.wav", "CachePath": "SFX/sfx_ui_character_common_A477CA7B.wem"}, {"Id": "609615974", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_pve_skillUseable.wav", "CachePath": "SFX/sfx_notice_pve_skillUseable_A477CA7B.wem"}, {"Id": "613520920", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_crystal_burst.wav", "CachePath": "SFX/sfx_cutScene_crystal_burst_A477CA7B.wem"}, {"Id": "616916900", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "voice_male_test.wav", "CachePath": "SFX/voice_male_test_3796D5AC.wem"}, {"Id": "617891247", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_flap_01.wav", "CachePath": "SFX/sfx_cutScene_paimon_flap_01_A477CA7B.wem"}, {"Id": "621033449", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_collect_iron_ore_03.wav", "CachePath": "SFX/sfx_animation_collect_iron_ore_03_A477CA7B.wem"}, {"Id": "621988094", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_berries_01.wav", "CachePath": "SFX/sfx_rpg_man_collect_berries_01_A477CA7B.wem"}, {"Id": "630020883", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_hit_01.wav", "CachePath": "SFX/sfx_hero_annie_attack_hit_01_A477CA7B.wem"}, {"Id": "630200909", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_confirm.wav", "CachePath": "SFX/sfx_notice_confirm_A477CA7B.wem"}, {"Id": "632483673", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_pet_blow_loop.wav", "CachePath": "SFX/sfx_rpg_pet_blow_loop_A50E84B9.wem"}, {"Id": "632651498", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolfking_attack_01.wav", "CachePath": "SFX/sfx_monster_wolfking_attack_01_A477CA7B.wem"}, {"Id": "636235407", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_dogbark.wav", "CachePath": "SFX/sfx_hero_annie_attack_dogbark_A477CA7B.wem"}, {"Id": "638372973", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_hyena_bark.wav", "CachePath": "SFX/sfx_cutScene_hyena_bark_A477CA7B.wem"}, {"Id": "642017840", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bat_flying.wav", "CachePath": "SFX/sfx_cutScene_bat_flying_A477CA7B.wem"}, {"Id": "642608268", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_skill_attack_hit.wav", "CachePath": "SFX/sfx_hero_isabella_skill_attack_hit_A477CA7B.wem"}, {"Id": "643534566", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_flap_03.wav", "CachePath": "SFX/sfx_cutScene_paimon_flap_03_A477CA7B.wem"}, {"Id": "644506405", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bee_attack.wav", "CachePath": "SFX/sfx_cutScene_bee_attack_A477CA7B.wem"}, {"Id": "652545392", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_dialog_close.wav", "CachePath": "SFX/sfx_ui_panel_dialog_close_A477CA7B.wem"}, {"Id": "653497506", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spiderelite_skill_fail.wav", "CachePath": "SFX/sfx_monster_spiderelite_skill_fail_A477CA7B.wem"}, {"Id": "660259301", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_skill_hit.wav", "CachePath": "SFX/sfx_hero_zoe_skill_hit_A477CA7B.wem"}, {"Id": "662045469", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_attack_01.wav", "CachePath": "SFX/sfx_hero_isabella_attack_01_A477CA7B.wem"}, {"Id": "663533879", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_2_02.wav", "CachePath": "SFX/sfx_hero_vivi_attack_2_02_A477CA7B.wem"}, {"Id": "665143121", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_death.wav", "CachePath": "SFX/sfx_hero_dante_death_A477CA7B.wem"}, {"Id": "667945588", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_attack.wav", "CachePath": "SFX/sfx_hero_sierra_attack_A477CA7B.wem"}, {"Id": "672229353", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "voice_famale_test.wav", "CachePath": "SFX/voice_famale_test_3796D5AC.wem"}, {"Id": "673124368", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolfking_attack_03.wav", "CachePath": "SFX/sfx_monster_wolfking_attack_03_A477CA7B.wem"}, {"Id": "676954604", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_death.wav", "CachePath": "SFX/sfx_hero_lina_death_A477CA7B.wem"}, {"Id": "688481423", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_formation_common.wav", "CachePath": "SFX/sfx_ui_formation_common_A477CA7B.wem"}, {"Id": "695815029", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_dialog_open.wav", "CachePath": "SFX/sfx_ui_panel_dialog_open_A477CA7B.wem"}, {"Id": "701033755", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabe<PERSON>_attack_hit_03.wav", "CachePath": "SFX/sfx_hero_isabe<PERSON>_attack_hit_03_A477CA7B.wem"}, {"Id": "704048172", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_push_turtle_loop.wav", "CachePath": "SFX/sfx_rpg_man_push_turtle_loop_323838B4.wem"}, {"Id": "706532936", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_snake_attack.wav", "CachePath": "SFX/sfx_monster_snake_attack_A477CA7B.wem"}, {"Id": "708414995", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_man_frightened.wav", "CachePath": "SFX/sfx_cutScene_man_frightened_A477CA7B.wem"}, {"Id": "715126969", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_lava_solidifying.wav", "CachePath": "SFX/sfx_animation_lava_solidifying_A50E84B9.wem"}, {"Id": "722188082", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_death.wav", "CachePath": "SFX/sfx_hero_lilith_death_A477CA7B.wem"}, {"Id": "725561387", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dragon_attack.wav", "CachePath": "SFX/sfx_cutScene_dragon_attack_A477CA7B.wem"}, {"Id": "726133731", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_rpg_mission_accomplished.wav", "CachePath": "SFX/sfx_notice_rpg_mission_accomplished_A477CA7B.wem"}, {"Id": "727547958", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_monkey_call.wav", "CachePath": "SFX/sfx_rpg_monkey_call_A477CA7B.wem"}, {"Id": "736636372", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "sfx_amb_rpg_volcano.wav", "Path": "Media/736636372.wem", "CachePath": "SFX/sfx_amb_rpg_volcano_A50E84B9.wem"}, {"Id": "736925174", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spiderelite_skill.wav", "CachePath": "SFX/sfx_monster_spiderelite_skill_A477CA7B.wem"}, {"Id": "742220051", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_monkey_attack.wav", "CachePath": "SFX/sfx_monster_monkey_attack_A477CA7B.wem"}, {"Id": "750169564", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_death.wav", "CachePath": "SFX/sfx_hero_annie_death_A477CA7B.wem"}, {"Id": "752670150", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_1_02.wav", "CachePath": "SFX/sfx_hero_zoe_attack_1_02_A477CA7B.wem"}, {"Id": "755714586", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_flying.wav", "CachePath": "SFX/sfx_cutScene_paimon_flying_A477CA7B.wem"}, {"Id": "759720742", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_snake_hiss.wav", "CachePath": "SFX/sfx_cutScene_snake_hiss_A50E84B9.wem"}, {"Id": "760043406", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_attack_2_01.wav", "CachePath": "SFX/sfx_hero_vivi_attack_2_01_A477CA7B.wem"}, {"Id": "760215675", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_pet_appear.wav", "CachePath": "SFX/sfx_rpg_pet_appear_A477CA7B.wem"}, {"Id": "765096628", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolf_attack_01.wav", "CachePath": "SFX/sfx_monster_wolf_attack_01_A477CA7B.wem"}, {"Id": "766902835", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_openChest.wav", "CachePath": "SFX/sfx_rpg_man_openChest_A477CA7B.wem"}, {"Id": "769759363", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_vulture_attack.wav", "CachePath": "SFX/sfx_monster_vulture_attack_A477CA7B.wem"}, {"Id": "769978538", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_stones_03.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_stones_03_A477CA7B.wem"}, {"Id": "773801030", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_skill.wav", "CachePath": "SFX/sfx_hero_zoe_skill_A477CA7B.wem"}, {"Id": "774460902", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_paimon_sleeping.wav", "CachePath": "SFX/sfx_cutScene_paimon_sleeping_A477CA7B.wem"}, {"Id": "778413402", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_skill_3.wav", "CachePath": "SFX/sfx_hero_dante_skill_3_A477CA7B.wem"}, {"Id": "787740001", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_ore.wav", "CachePath": "SFX/sfx_rpg_man_collect_ore_A477CA7B.wem"}, {"Id": "791141614", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_bombbeep.wav", "CachePath": "SFX/sfx_hero_annie_attack_bombbeep_A477CA7B.wem"}, {"Id": "791468363", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_grass_01.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_grass_01_A477CA7B.wem"}, {"Id": "797261951", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_fecruit_landing.wav", "CachePath": "SFX/sfx_ui_fecruit_landing_A477CA7B.wem"}, {"Id": "814508586", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_plank_02.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_plank_02_A477CA7B.wem"}, {"Id": "822144417", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_attack_1_02.wav", "CachePath": "SFX/sfx_hero_lilith_attack_1_02_A477CA7B.wem"}, {"Id": "822620424", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_run_04.wav", "CachePath": "SFX/sfx_cutScene_orang_run_04_A477CA7B.wem"}, {"Id": "823004320", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_02.wav", "CachePath": "SFX/sfx_hero_annie_attack_02_A477CA7B.wem"}, {"Id": "826289354", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_scene_ele_bonfire_loop.wav", "CachePath": "SFX/sfx_scene_ele_bonfire_loop_A477CA7B.wem"}, {"Id": "827127572", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_run_02.wav", "CachePath": "SFX/sfx_cutScene_bear_run_02_A477CA7B.wem"}, {"Id": "833192473", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_orang_run_03.wav", "CachePath": "SFX/sfx_cutScene_orang_run_03_A477CA7B.wem"}, {"Id": "835295918", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_skill2_hit.wav", "CachePath": "SFX/sfx_hero_lina_skill2_hit_A477CA7B.wem"}, {"Id": "836088732", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bee_help.wav", "CachePath": "SFX/sfx_cutScene_bee_help_A477CA7B.wem"}, {"Id": "838746950", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_neutral.wav", "CachePath": "SFX/sfx_notice_neutral_A477CA7B.wem"}, {"Id": "845003763", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_spider_web_stretch.wav", "CachePath": "SFX/sfx_cutScene_spider_web_stretch_A477CA7B.wem"}, {"Id": "845836426", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_spider_silk.wav", "CachePath": "SFX/sfx_rpg_spider_silk_A477CA7B.wem"}, {"Id": "851863018", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_1_01.wav", "CachePath": "SFX/sfx_hero_zoe_attack_1_01_A477CA7B.wem"}, {"Id": "852698159", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_dog_happy.wav", "CachePath": "SFX/sfx_animation_dog_happy_A477CA7B.wem"}, {"Id": "857822037", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_dog_attack.wav", "CachePath": "SFX/sfx_monster_dog_attack_A477CA7B.wem"}, {"Id": "859511538", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dog_hit_wall.wav", "CachePath": "SFX/sfx_cutScene_dog_hit_wall_A477CA7B.wem"}, {"Id": "865101134", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_annie_attack_03.wav", "CachePath": "SFX/sfx_hero_annie_attack_03_A477CA7B.wem"}, {"Id": "866356243", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_vine.wav", "CachePath": "SFX/sfx_rpg_man_collect_vine_A477CA7B.wem"}, {"Id": "867993667", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_snake_drifting.wav", "CachePath": "SFX/sfx_cutScene_snake_drifting_A477CA7B.wem"}, {"Id": "870206804", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_woods_commotion.wav", "CachePath": "SFX/sfx_cutScene_woods_commotion_A477CA7B.wem"}, {"Id": "886113566", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_rowen_death.wav", "CachePath": "SFX/sfx_hero_rowen_death_A477CA7B.wem"}, {"Id": "888959723", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_bear_walk_02.wav", "CachePath": "SFX/sfx_cutScene_bear_walk_02_A477CA7B.wem"}, {"Id": "890169225", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_gorilla_sleep.wav", "CachePath": "SFX/sfx_rpg_gorilla_sleep_A477CA7B.wem"}, {"Id": "892317253", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_sand_03.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_sand_03_A477CA7B.wem"}, {"Id": "895320919", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_open_bigchest_Instant.wav", "CachePath": "SFX/sfx_rpg_man_open_bigchest_Instant_A477CA7B.wem"}, {"Id": "896808524", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_berries_03.wav", "CachePath": "SFX/sfx_rpg_man_collect_berries_03_A477CA7B.wem"}, {"Id": "904786911", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_hit_01.wav", "CachePath": "SFX/sfx_hero_dante_attack_hit_01_A477CA7B.wem"}, {"Id": "908609484", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_skill2.wav", "CachePath": "SFX/sfx_hero_sierra_skill2_A477CA7B.wem"}, {"Id": "911739863", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_collect_mushroom_01.wav", "CachePath": "SFX/sfx_rpg_man_collect_mushroom_01_A477CA7B.wem"}, {"Id": "914226319", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_waterturtle_scared.wav", "CachePath": "SFX/sfx_cutScene_waterturtle_scared_A477CA7B.wem"}, {"Id": "917792699", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_isabella_death.wav", "CachePath": "SFX/sfx_hero_isabella_death_A477CA7B.wem"}, {"Id": "918477639", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_scene_unfold_ladder.wav", "CachePath": "SFX/sfx_scene_unfold_ladder_A477CA7B.wem"}, {"Id": "920736361", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_wolf_jump.wav", "CachePath": "SFX/sfx_rpg_wolf_jump_A477CA7B.wem"}, {"Id": "924289523", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_skill_hit.wav", "CachePath": "SFX/sfx_hero_sierra_skill_hit_A477CA7B.wem"}, {"Id": "926714882", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_attack_03.wav", "CachePath": "SFX/sfx_hero_lina_attack_03_A477CA7B.wem"}, {"Id": "928078625", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_skill_2.wav", "CachePath": "SFX/sfx_hero_vivi_skill_2_A477CA7B.wem"}, {"Id": "928171570", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_death.wav", "CachePath": "SFX/sfx_hero_sierra_death_A477CA7B.wem"}, {"Id": "928296001", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_death.wav", "CachePath": "SFX/sfx_hero_zoe_death_A477CA7B.wem"}, {"Id": "939358324", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_1_01.wav", "CachePath": "SFX/sfx_hero_dante_attack_1_01_A477CA7B.wem"}, {"Id": "942930113", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_tree_spirit_jump_01.wav", "CachePath": "SFX/sfx_cutScene_tree_spirit_jump_01_A477CA7B.wem"}, {"Id": "943654622", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_levelUp.wav", "CachePath": "SFX/sfx_notice_levelUp_A477CA7B.wem"}, {"Id": "949553456", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_notice_rpg_getCrystalShards_02.wav", "CachePath": "SFX/sfx_notice_rpg_getCrystalShards_02_A477CA7B.wem"}, {"Id": "954983680", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_chicken_catched_04.wav", "CachePath": "SFX/sfx_animation_chicken_catched_04_A477CA7B.wem"}, {"Id": "957414585", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_dante_attack_2_03.wav", "CachePath": "SFX/sfx_hero_dante_attack_2_03_A477CA7B.wem"}, {"Id": "957640449", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_iron_bridge_build.wav", "CachePath": "SFX/sfx_animation_iron_bridge_build_A477CA7B.wem"}, {"Id": "960238724", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_building_common.wav", "CachePath": "SFX/sfx_ui_building_common_A477CA7B.wem"}, {"Id": "963824576", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_vulture_scare_call.wav", "CachePath": "SFX/sfx_rpg_vulture_scare_call_A477CA7B.wem"}, {"Id": "966599810", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_women_straining.wav", "CachePath": "SFX/sfx_animation_women_straining_A477CA7B.wem"}, {"Id": "967943406", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_man_straining.wav", "CachePath": "SFX/sfx_animation_man_straining_A477CA7B.wem"}, {"Id": "968385269", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_skill2_hit.wav", "CachePath": "SFX/sfx_hero_sierra_skill2_hit_A477CA7B.wem"}, {"Id": "973613541", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lilith_skill2_1.wav", "CachePath": "SFX/sfx_hero_lilith_skill2_1_A477CA7B.wem"}, {"Id": "977361644", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_openChest_Instant.wav", "CachePath": "SFX/sfx_rpg_man_openChest_Instant_A477CA7B.wem"}, {"Id": "983502686", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_scene_ele_rpg_waterfall.wav", "CachePath": "SFX/sfx_scene_ele_rpg_waterfall_A477CA7B.wem"}, {"Id": "984088565", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_wolf_attack_02.wav", "CachePath": "SFX/sfx_monster_wolf_attack_02_4B1C540E.wem"}, {"Id": "986648724", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_lina_skill_hit.wav", "CachePath": "SFX/sfx_hero_lina_skill_hit_A477CA7B.wem"}, {"Id": "991027540", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_pet_breathing_fire.wav", "CachePath": "SFX/sfx_animation_pet_breathing_fire_A50E84B9.wem"}, {"Id": "997781802", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_gorilla_rage_attacksuccess.wav", "CachePath": "SFX/sfx_monster_gorilla_rage_attacksuccess_A477CA7B.wem"}, {"Id": "998435409", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_spiderelite_attack.wav", "CachePath": "SFX/sfx_monster_spiderelite_attack_A477CA7B.wem"}, {"Id": "998492363", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_porcupine_angry.wav", "CachePath": "SFX/sfx_cutScene_porcupine_angry_A477CA7B.wem"}, {"Id": "1002950913", "Language": "SFX", "Streaming": "true", "Location": "Loose", "ShortName": "sfx_amb_rpg_forest.wav", "Path": "Media/1002950913.wem", "CachePath": "SFX/sfx_amb_rpg_forest_A50E84B9.wem"}, {"Id": "1004677208", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_sierra_skill.wav", "CachePath": "SFX/sfx_hero_sierra_skill_A477CA7B.wem"}, {"Id": "1013798872", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_panel_small_open.wav", "CachePath": "SFX/sfx_ui_panel_small_open_A477CA7B.wem"}, {"Id": "1017476771", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_zoe_attack_3_01.wav", "CachePath": "SFX/sfx_hero_zoe_attack_3_01_A477CA7B.wem"}, {"Id": "1025901802", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_dragon_shadow_flying.wav", "CachePath": "SFX/sfx_cutScene_dragon_shadow_flying_A50E84B9.wem"}, {"Id": "1025983463", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_ui_tick.wav", "CachePath": "SFX/sfx_ui_tick_A477CA7B.wem"}, {"Id": "1026991295", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_cutTree_03.wav", "CachePath": "SFX/sfx_rpg_man_cutTree_03_A477CA7B.wem"}, {"Id": "1027918914", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_dog_bark.wav", "CachePath": "SFX/sfx_animation_dog_bark_A50E84B9.wem"}, {"Id": "1030124390", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_push_turtle_loop.wav", "CachePath": "SFX/sfx_rpg_man_push_turtle_loop_7A9C1D3A.wem"}, {"Id": "1043368750", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_hero_vivi_skill_5.wav", "CachePath": "SFX/sfx_hero_vivi_skill_5_A477CA7B.wem"}, {"Id": "1048602592", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_mush_born.wav", "CachePath": "SFX/sfx_rpg_mush_born_A477CA7B.wem"}, {"Id": "1051034241", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_monster_bat_skillstart.wav", "CachePath": "SFX/sfx_monster_bat_skillstart_A477CA7B.wem"}, {"Id": "1057282303", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_cut_thorns.wav", "CachePath": "SFX/sfx_rpg_man_cut_thorns_A477CA7B.wem"}, {"Id": "1057537891", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_stones_02.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_stones_02_A477CA7B.wem"}, {"Id": "1059442337", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_fire_big_burn.wav", "CachePath": "SFX/sfx_cutScene_fire_big_burn_A50E84B9.wem"}, {"Id": "1060725018", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_waterturtle_escape.wav", "CachePath": "SFX/sfx_cutScene_waterturtle_escape_A477CA7B.wem"}, {"Id": "1065930103", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_cutScene_common_watchful_famale.wav", "CachePath": "SFX/sfx_cutScene_common_watchful_famale_A477CA7B.wem"}, {"Id": "1067128043", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_sand_02.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_sand_02_A477CA7B.wem"}, {"Id": "1067908762", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_rpg_man_footsteps_stones_01.wav", "CachePath": "SFX/sfx_rpg_man_footsteps_stones_01_A477CA7B.wem"}, {"Id": "1069617190", "Language": "SFX", "Streaming": "false", "Location": "Memory", "ShortName": "sfx_animation_chicken_catched_02.wav", "CachePath": "SFX/sfx_animation_chicken_catched_02_A477CA7B.wem"}], "Plugins": {"Custom": [{"Id": "347332099", "Name": "Wwise Silence", "LibName": "Wwise Silence", "LibId": "6619138"}]}, "Events": [{"Id": "2694548895", "Name": "Control_cg_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1007799484", "Name": "Control_cg_start", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3309329799", "Name": "Control_cg_timeline_01_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "744229748", "Name": "Control_cg_timeline_01_start", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "333055903", "Name": "Control_cg_timeline_bat_flying_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "987092668", "Name": "Control_cg_timeline_bat_flying_start", "DurationType": "OneShot", "DurationMin": "2.74825", "DurationMax": "2.74825", "MediaRefs": [{"Id": "642017840"}]}, {"Id": "4014692711", "Name": "Control_cg_timeline_bear_attack_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2661763156", "Name": "Control_cg_timeline_bear_attack_start", "DurationType": "OneShot", "DurationMin": "1.4471042", "DurationMax": "1.4471042", "MediaRefs": [{"Id": "55185885"}]}, {"Id": "1606855123", "Name": "Control_cg_timeline_bear_roar_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3811770984", "Name": "Control_cg_timeline_bear_roar_start", "DurationType": "OneShot", "DurationMin": "1.4275209", "DurationMax": "1.4275209", "MediaRefs": [{"Id": "210469521"}]}, {"Id": "1870138326", "Name": "Control_cg_timeline_bear_run_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "474258877", "Name": "Control_cg_timeline_bear_run_start", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3168699822", "Name": "Control_cg_timeline_bear_shoot_ground_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1729366469", "Name": "Control_cg_timeline_bear_shoot_ground_start", "DurationType": "OneShot", "DurationMin": "1.3807917", "DurationMax": "1.3807917", "MediaRefs": [{"Id": "234377884"}]}, {"Id": "3458590540", "Name": "Control_cg_timeline_bear_walk_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "49132463", "Name": "Control_cg_timeline_bear_walk_start", "DurationType": "OneShot", "DurationMin": "0.32308334", "DurationMax": "0.32308334", "MediaRefs": [{"Id": "189609105"}, {"Id": "589136421"}, {"Id": "888959723"}]}, {"Id": "3871501177", "Name": "Control_cg_timeline_common_watchful_famale_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "982270618", "Name": "Control_cg_timeline_common_watchful_famale_start", "DurationType": "OneShot", "DurationMin": "0.95860416", "DurationMax": "0.95860416", "MediaRefs": [{"Id": "1065930103"}]}, {"Id": "1966855715", "Name": "Control_cg_timeline_fence_attacked_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3271335416", "Name": "Control_cg_timeline_fence_attacked_start", "DurationType": "OneShot", "DurationMin": "1.0813125", "DurationMax": "1.0813125", "MediaRefs": [{"Id": "421882104"}]}, {"Id": "3960261563", "Name": "Control_cg_timeline_groud_collapse_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1614905568", "Name": "Control_cg_timeline_groud_collapse_start", "DurationType": "OneShot", "DurationMin": "5.107167", "DurationMax": "5.107167", "MediaRefs": [{"Id": "57899251"}]}, {"Id": "2022553840", "Name": "Control_cg_timeline_hyena_bark_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1198574731", "Name": "Control_cg_timeline_hyena_bark_start", "DurationType": "OneShot", "DurationMin": "0.8748958", "DurationMax": "0.8748958", "MediaRefs": [{"Id": "638372973"}]}, {"Id": "3223033688", "Name": "Control_cg_timeline_hyena_fall_down_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1497530627", "Name": "Control_cg_timeline_hyena_fall_down_start", "DurationType": "OneShot", "DurationMin": "0.9383125", "DurationMax": "0.9383125", "MediaRefs": [{"Id": "329169851"}]}, {"Id": "3684644815", "Name": "Control_cg_timeline_man_frightened_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2257011244", "Name": "Control_cg_timeline_man_frightened_start", "DurationType": "OneShot", "DurationMin": "0.47335416", "DurationMax": "0.47335416", "MediaRefs": [{"Id": "708414995"}]}, {"Id": "1754836484", "Name": "Control_cg_timeline_orang_beatcheast_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1550575799", "Name": "Control_cg_timeline_orang_beatcheast_start", "DurationType": "OneShot", "DurationMin": "4.2424793", "DurationMax": "4.2424793", "MediaRefs": [{"Id": "354328960"}]}, {"Id": "3272779137", "Name": "Control_cg_timeline_orang_hit_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "124982898", "Name": "Control_cg_timeline_orang_hit_start", "DurationType": "OneShot", "DurationMin": "1.5114375", "DurationMax": "1.5114375", "MediaRefs": [{"Id": "60291331"}]}, {"Id": "3498975555", "Name": "Control_cg_timeline_orang_landing_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3967704344", "Name": "Control_cg_timeline_orang_landing_start", "DurationType": "OneShot", "DurationMin": "1.0392292", "DurationMax": "1.0392292", "MediaRefs": [{"Id": "402184142"}]}, {"Id": "3239755601", "Name": "Control_cg_timeline_orang_run_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "670633986", "Name": "Control_cg_timeline_orang_run_start", "DurationType": "OneShot", "DurationMin": "0.3404375", "DurationMax": "0.3404375", "MediaRefs": [{"Id": "156869024"}, {"Id": "161913924"}, {"Id": "242385067"}, {"Id": "822620424"}, {"Id": "833192473"}]}, {"Id": "2489802992", "Name": "Control_cg_timeline_paimon_flap_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3183916171", "Name": "Control_cg_timeline_paimon_flap_start", "DurationType": "OneShot", "DurationMin": "0.64435416", "DurationMax": "1.0026667", "MediaRefs": [{"Id": "483527323"}, {"Id": "617891247"}, {"Id": "643534566"}]}, {"Id": "3682304672", "Name": "Control_cg_timeline_paimon_flying_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3736451451", "Name": "Control_cg_timeline_paimon_flying_start", "DurationType": "OneShot", "DurationMin": "6.004229", "DurationMax": "6.004229", "MediaRefs": [{"Id": "755714586"}]}, {"Id": "2637194060", "Name": "Control_cg_timeline_paimon_halloa_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3847393199", "Name": "Control_cg_timeline_paimon_halloa_start", "DurationType": "OneShot", "DurationMin": "0.96172917", "DurationMax": "0.96172917", "MediaRefs": [{"Id": "567515101"}]}, {"Id": "3461012582", "Name": "Control_cg_timeline_paimon_sleeping_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "4221467981", "Name": "Control_cg_timeline_paimon_sleeping_start", "DurationType": "OneShot", "DurationMin": "3.684729", "DurationMax": "3.684729", "MediaRefs": [{"Id": "774460902"}]}, {"Id": "685809425", "Name": "Control_cg_timeline_slate_shakes_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3334272450", "Name": "Control_cg_timeline_slate_shakes_start", "DurationType": "OneShot", "DurationMin": "1.4553334", "DurationMax": "1.4553334", "MediaRefs": [{"Id": "263408997"}]}, {"Id": "2268203305", "Name": "Control_cg_timeline_waterturtle_escape_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1463281994", "Name": "Control_cg_timeline_waterturtle_escape_start", "DurationType": "OneShot", "DurationMin": "2.8055418", "DurationMax": "2.8055418", "MediaRefs": [{"Id": "1060725018"}]}, {"Id": "85762300", "Name": "Control_cg_timeline_waterturtle_scared_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2966175135", "Name": "Control_cg_timeline_waterturtle_scared_start", "DurationType": "OneShot", "DurationMin": "0.48472917", "DurationMax": "0.48472917", "MediaRefs": [{"Id": "914226319"}]}, {"Id": "876724996", "Name": "Control_cg_timeline_woods_commotion_end", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2730984887", "Name": "Control_cg_timeline_woods_commotion_start", "DurationType": "OneShot", "DurationMin": "2.95", "DurationMax": "2.95", "MediaRefs": [{"Id": "870206804"}]}, {"Id": "2700496103", "Name": "Initializer", "DurationType": "Infinite", "SwitchContainers": [{"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "3137033164"}, "MediaRefs": [{"Id": "484913630"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "1808625698"}, "MediaRefs": [{"Id": "487732415"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "1976664129"}, "MediaRefs": [{"Id": "466308787"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "204962941"}, "MediaRefs": [{"Id": "593964798"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "888797855"}, "MediaRefs": [{"Id": "593964798"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "3953081786"}, "MediaRefs": [{"Id": "593964798"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "3888786832"}, "MediaRefs": [{"Id": "487732415"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "1808625698"}, "MediaRefs": [{"Id": "1002950913"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "1976664129"}, "MediaRefs": [{"Id": "736636372"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "3953081786"}, "MediaRefs": [{"Id": "736636372"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "204962941"}, "MediaRefs": [{"Id": "591317335"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "888797855"}, "MediaRefs": [{"Id": "1002950913"}]}, {"SwitchValue": {"GroupType": "State", "GroupId": "1926883983", "Id": "3137033164"}, "MediaRefs": [{"Id": "591317335"}]}]}, {"Id": "3391871571", "Name": "Play_sfx_animation_chicken_catched", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3583333", "DurationMax": "1.3583333", "MediaRefs": [{"Id": "147788772"}, {"Id": "502605230"}, {"Id": "954983680"}, {"Id": "1069617190"}]}, {"Id": "54040925", "Name": "Play_sfx_animation_collect_iron_ore", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.9", "DurationMax": "1.0170833", "MediaRefs": [{"Id": "526805455"}, {"Id": "549215730"}, {"Id": "621033449"}]}, {"Id": "1769156192", "Name": "Play_sfx_animation_dog_bark", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "1027918914"}]}, {"Id": "3561045076", "Name": "Play_sfx_animation_dog_happy", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0316458", "DurationMax": "2.0316458", "MediaRefs": [{"Id": "852698159"}]}, {"Id": "873103526", "Name": "Play_sfx_animation_iron_bridge_build", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.45", "DurationMax": "1.45", "MediaRefs": [{"Id": "957640449"}]}, {"Id": "178805343", "Name": "Play_sfx_animation_lava_solidifying", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "715126969"}]}, {"Id": "533619153", "Name": "Play_sfx_animation_man_straining", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.65", "DurationMax": "0.65", "MediaRefs": [{"Id": "967943406"}]}, {"Id": "3471440032", "Name": "Play_sfx_animation_pet_breathing_fire", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "991027540"}]}, {"Id": "2518926114", "Name": "Play_sfx_animation_rpg_blowGas", "DurationType": "OneShot", "DurationMin": "3.387625", "DurationMax": "3.387625", "MediaRefs": [{"Id": "502017925"}]}, {"Id": "4005472809", "Name": "Play_sfx_animation_spider_web_burn", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "391729555"}]}, {"Id": "2611778259", "Name": "Play_sfx_animation_tree_spirit_walk", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "464547566"}]}, {"Id": "2479658343", "Name": "Play_sfx_animation_women_straining", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.43510416", "DurationMax": "0.43510416", "MediaRefs": [{"Id": "966599810"}]}, {"Id": "1614893586", "Name": "Play_sfx_cutScene_bee_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.7666667", "DurationMax": "2.7666667", "MediaRefs": [{"Id": "644506405"}]}, {"Id": "970819831", "Name": "Play_sfx_cutScene_bee_flying", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "517660813"}]}, {"Id": "3476295695", "Name": "Play_sfx_cutScene_bee_help", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.8233333", "DurationMax": "0.8233333", "MediaRefs": [{"Id": "836088732"}]}, {"Id": "92705169", "Name": "Play_sfx_cutScene_bee_laugh", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.2296457", "DurationMax": "2.2296457", "MediaRefs": [{"Id": "97636415"}]}, {"Id": "2902690517", "Name": "Play_sfx_cutScene_bee_stung", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1333333", "DurationMax": "1.1333333", "MediaRefs": [{"Id": "299002274"}]}, {"Id": "3758930870", "Name": "Play_sfx_cutScene_crystal_burst", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3583333", "DurationMax": "1.3583333", "MediaRefs": [{"Id": "613520920"}]}, {"Id": "1505569433", "Name": "Play_sfx_cutScene_crystal_charging", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "108228392"}]}, {"Id": "87872190", "Name": "Play_sfx_cutScene_dog_eat", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.9166666", "DurationMax": "1.9166666", "MediaRefs": [{"Id": "590201357"}]}, {"Id": "1721844170", "Name": "Play_sfx_cutScene_dog_hit_wall", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.51766664", "DurationMax": "0.51766664", "MediaRefs": [{"Id": "859511538"}]}, {"Id": "585543783", "Name": "Play_sfx_cutScene_dragon_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3583333", "DurationMax": "1.3583333", "MediaRefs": [{"Id": "725561387"}]}, {"Id": "3211079709", "Name": "Play_sfx_cutScene_dragon_breathing_fireball", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "351522230"}]}, {"Id": "1848102663", "Name": "Play_sfx_cutScene_dragon_roar", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.0166667", "DurationMax": "3.0166667", "MediaRefs": [{"Id": "363657281"}]}, {"Id": "4020507039", "Name": "Play_sfx_cutScene_dragon_shadow_flying", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "1025901802"}]}, {"Id": "2017830423", "Name": "Play_sfx_cutScene_ele_bear_attack", "DurationType": "OneShot", "DurationMin": "1.4471042", "DurationMax": "1.4471042", "MediaRefs": [{"Id": "55185885"}]}, {"Id": "663364471", "Name": "Play_sfx_cutScene_ele_bear_roar", "DurationType": "OneShot", "DurationMin": "1.4275209", "DurationMax": "1.4275209", "MediaRefs": [{"Id": "210469521"}]}, {"Id": "402434918", "Name": "Play_sfx_cutScene_ele_bear_run", "DurationType": "OneShot", "DurationMin": "0.3404375", "DurationMax": "0.3404375", "MediaRefs": [{"Id": "215082828"}, {"Id": "309117333"}, {"Id": "408157936"}, {"Id": "604157030"}, {"Id": "827127572"}]}, {"Id": "135416387", "Name": "Play_sfx_cutScene_ele_fence_attacked", "DurationType": "OneShot", "DurationMin": "1.0813125", "DurationMax": "1.0813125", "MediaRefs": [{"Id": "421882104"}]}, {"Id": "3216598527", "Name": "Play_sfx_cutScene_ele_groud_collapse", "DurationType": "OneShot", "DurationMin": "5.107167", "DurationMax": "5.107167", "MediaRefs": [{"Id": "57899251"}]}, {"Id": "3671734472", "Name": "Play_sfx_cutScene_ele_hyena_bark", "DurationType": "OneShot", "DurationMin": "0.8748958", "DurationMax": "0.8748958", "MediaRefs": [{"Id": "638372973"}]}, {"Id": "3098058524", "Name": "Play_sfx_cutScene_ele_hyena_fall_down", "DurationType": "OneShot", "DurationMin": "0.9383125", "DurationMax": "0.9383125", "MediaRefs": [{"Id": "329169851"}]}, {"Id": "1849445790", "Name": "Play_sfx_cutScene_ele_ladder_break", "DurationType": "OneShot", "DurationMin": "2.2506666", "DurationMax": "2.2506666", "MediaRefs": [{"Id": "284730522"}]}, {"Id": "1311527879", "Name": "Play_sfx_cutScene_ele_landing", "DurationType": "OneShot", "DurationMin": "1.6140833", "DurationMax": "1.6140833", "MediaRefs": [{"Id": "509373921"}]}, {"Id": "1829034864", "Name": "Play_sfx_cutScene_ele_orang_beatcheast", "DurationType": "OneShot", "DurationMin": "4.2424793", "DurationMax": "4.2424793", "MediaRefs": [{"Id": "354328960"}]}, {"Id": "1811644497", "Name": "Play_sfx_cutScene_ele_orang_hit", "DurationType": "OneShot", "DurationMin": "1.5114375", "DurationMax": "1.5114375", "MediaRefs": [{"Id": "60291331"}]}, {"Id": "2973675855", "Name": "Play_sfx_cutScene_ele_orang_landing", "DurationType": "OneShot", "DurationMin": "1.0392292", "DurationMax": "1.0392292", "MediaRefs": [{"Id": "402184142"}]}, {"Id": "1807407265", "Name": "Play_sfx_cutScene_ele_orang_run", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3143612920", "Name": "Play_sfx_cutScene_ele_paimon_flap", "DurationType": "OneShot", "DurationMin": "0.64435416", "DurationMax": "1.0026667", "MediaRefs": [{"Id": "483527323"}, {"Id": "617891247"}, {"Id": "643534566"}]}, {"Id": "635742104", "Name": "Play_sfx_cutScene_ele_paimon_flying", "DurationType": "OneShot", "DurationMin": "6.004229", "DurationMax": "6.004229", "MediaRefs": [{"Id": "755714586"}]}, {"Id": "418135060", "Name": "Play_sfx_cutScene_ele_paimon_halloa", "DurationType": "OneShot", "DurationMin": "0.96172917", "DurationMax": "0.96172917", "MediaRefs": [{"Id": "567515101"}]}, {"Id": "1586972862", "Name": "Play_sfx_cutScene_ele_paimon_sleeping", "DurationType": "OneShot", "DurationMin": "3.684729", "DurationMax": "3.684729", "MediaRefs": [{"Id": "774460902"}]}, {"Id": "3189503697", "Name": "Play_sfx_cutScene_ele_waterturtle_escape", "DurationType": "OneShot", "DurationMin": "2.8055418", "DurationMax": "2.8055418", "MediaRefs": [{"Id": "1060725018"}]}, {"Id": "3096156506", "Name": "Play_sfx_cutScene_fire_big_burn", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "1059442337"}]}, {"Id": "3014099844", "Name": "Play_sfx_cutScene_fire_extinguish", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.35", "DurationMax": "3.35", "MediaRefs": [{"Id": "389786303"}]}, {"Id": "3799875123", "Name": "Play_sfx_cutScene_fire_small_burn", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "33596105"}]}, {"Id": "643936715", "Name": "Play_sfx_cutScene_fire_spread_burn", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "7.65", "DurationMax": "7.65", "MediaRefs": [{"Id": "********"}]}, {"Id": "461959316", "Name": "Play_sfx_cutScene_porcupine_angry", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.4833333", "DurationMax": "1.4833333", "MediaRefs": [{"Id": "998492363"}]}, {"Id": "1772965563", "Name": "Play_sfx_cutScene_push_stone", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3", "DurationMax": "3", "MediaRefs": [{"Id": "325962657"}]}, {"Id": "635067553", "Name": "Play_sfx_cutScene_snake_drifting", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.7660832", "DurationMax": "2.7660832", "MediaRefs": [{"Id": "867993667"}]}, {"Id": "3410526253", "Name": "Play_sfx_cutScene_snake_hiss", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "759720742"}]}, {"Id": "1277798695", "Name": "Play_sfx_cutScene_spider_web_stretch", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.9136667", "DurationMax": "2.9136667", "MediaRefs": [{"Id": "845003763"}]}, {"Id": "972227087", "Name": "Play_sfx_cutScene_stone_drop_in_lava", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3583333", "DurationMax": "1.3583333", "MediaRefs": [{"Id": "318329169"}]}, {"Id": "2055622770", "Name": "Play_sfx_cutScene_tree_spirit_jump", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3583333", "DurationMax": "1.3583333", "MediaRefs": [{"Id": "102837247"}, {"Id": "360941261"}, {"Id": "374378373"}, {"Id": "942930113"}]}, {"Id": "865440022", "Name": "Play_sfx_cutScene_troop_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.0471458", "DurationMax": "1.0471458", "MediaRefs": [{"Id": "235978478"}]}, {"Id": "2336910820", "Name": "Play_sfx_cutScene_volcano_eruption", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "314056286"}]}, {"Id": "648715895", "Name": "Play_sfx_hero_annie_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1", "DurationMax": "1.4666667", "MediaRefs": [{"Id": "29833038"}, {"Id": "823004320"}, {"Id": "865101134"}]}, {"Id": "345723630", "Name": "Play_sfx_hero_annie_attack_bombbeep", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "4.508333", "DurationMax": "4.508333", "MediaRefs": [{"Id": "791141614"}]}, {"Id": "4242420989", "Name": "Play_sfx_hero_annie_attack_bombexplode", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.8966668", "DurationMax": "2.8966668", "MediaRefs": [{"Id": "81238408"}]}, {"Id": "3458216758", "Name": "Play_sfx_hero_annie_attack_bombset", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.68333334", "DurationMax": "0.68333334", "MediaRefs": [{"Id": "103152751"}]}, {"Id": "2587809346", "Name": "Play_sfx_hero_annie_attack_dogbark", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.85", "DurationMax": "0.85", "MediaRefs": [{"Id": "636235407"}]}, {"Id": "3328981157", "Name": "Play_sfx_hero_annie_attack_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.2", "DurationMax": "0.304", "MediaRefs": [{"Id": "141779687"}, {"Id": "455155676"}, {"Id": "630020883"}]}, {"Id": "1301735799", "Name": "Play_sfx_hero_annie_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.0666667", "DurationMax": "1.0666667", "MediaRefs": [{"Id": "750169564"}]}, {"Id": "1019083844", "Name": "Play_sfx_hero_annie_skill_summon", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.9166667", "DurationMax": "2.9166667", "MediaRefs": [{"Id": "405084286"}]}, {"Id": "2635494254", "Name": "Play_sfx_hero_dante_attack_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.48", "DurationMax": "1.5166667", "MediaRefs": [{"Id": "171862592"}, {"Id": "552950655"}, {"Id": "939358324"}]}, {"Id": "2635494253", "Name": "Play_sfx_hero_dante_attack_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.9833333", "DurationMax": "3.65", "MediaRefs": [{"Id": "126656759"}, {"Id": "136448081"}, {"Id": "957414585"}]}, {"Id": "2559318464", "Name": "Play_sfx_hero_dante_attack_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.15", "DurationMax": "0.18333334", "MediaRefs": [{"Id": "481169991"}, {"Id": "604867087"}, {"Id": "904786911"}]}, {"Id": "428710692", "Name": "Play_sfx_hero_dante_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.4015", "DurationMax": "0.4015", "MediaRefs": [{"Id": "665143121"}]}, {"Id": "1071492341", "Name": "Play_sfx_hero_dante_skill2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.1676042", "DurationMax": "2.1676042", "MediaRefs": [{"Id": "522465123"}]}, {"Id": "569815929", "Name": "Play_sfx_hero_dante_skill_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1603333", "DurationMax": "1.1603333", "MediaRefs": [{"Id": "574485244"}]}, {"Id": "569815930", "Name": "Play_sfx_hero_dante_skill_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0246665", "DurationMax": "2.0246665", "MediaRefs": [{"Id": "481465004"}]}, {"Id": "569815931", "Name": "Play_sfx_hero_dante_skill_3", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.5924791", "DurationMax": "1.5924791", "MediaRefs": [{"Id": "778413402"}]}, {"Id": "347659393", "Name": "Play_sfx_hero_isabe<PERSON>_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1", "DurationMax": "1.1833333", "MediaRefs": [{"Id": "162975851"}, {"Id": "502420198"}, {"Id": "662045469"}]}, {"Id": "796373059", "Name": "Play_sfx_hero_isabe<PERSON>_attack_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.21666667", "DurationMax": "0.35", "MediaRefs": [{"Id": "56135970"}, {"Id": "82143151"}, {"Id": "701033755"}]}, {"Id": "3799425445", "Name": "Play_sfx_hero_isabella_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.47491667", "DurationMax": "0.47491667", "MediaRefs": [{"Id": "917792699"}]}, {"Id": "651830458", "Name": "Play_sfx_hero_isabella_skill2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.4833333", "DurationMax": "1.4833333", "MediaRefs": [{"Id": "237541992"}]}, {"Id": "2974817407", "Name": "Play_sfx_hero_isabella_skill_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.5666666", "DurationMax": "2.5666666", "MediaRefs": [{"Id": "75375983"}]}, {"Id": "492851485", "Name": "Play_sfx_hero_isabella_skill_attack_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.31666666", "DurationMax": "0.31666666", "MediaRefs": [{"Id": "642608268"}]}, {"Id": "1138433182", "Name": "Play_sfx_hero_isabella_skill_end", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3166667", "DurationMax": "1.3166667", "MediaRefs": [{"Id": "281681615"}]}, {"Id": "892587381", "Name": "Play_sfx_hero_isabella_skill_start", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "4.766667", "DurationMax": "4.766667", "MediaRefs": [{"Id": "556446041"}]}, {"Id": "3925112644", "Name": "Play_sfx_hero_lilith_attack_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.96", "DurationMax": "1.04", "MediaRefs": [{"Id": "449236370"}, {"Id": "586476684"}, {"Id": "822144417"}]}, {"Id": "3925112647", "Name": "Play_sfx_hero_lilith_attack_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.96", "DurationMax": "1.04", "MediaRefs": [{"Id": "449236370"}, {"Id": "586476684"}, {"Id": "822144417"}]}, {"Id": "3272713626", "Name": "Play_sfx_hero_lilith_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.779", "DurationMax": "0.779", "MediaRefs": [{"Id": "722188082"}]}, {"Id": "3841679355", "Name": "Play_sfx_hero_lilith_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.4848332", "DurationMax": "2.4848332", "MediaRefs": [{"Id": "353340842"}]}, {"Id": "2552274355", "Name": "Play_sfx_hero_lilith_skill2_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.9775208", "DurationMax": "1.9775208", "MediaRefs": [{"Id": "973613541"}]}, {"Id": "2552274352", "Name": "Play_sfx_hero_lilith_skill2_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.967", "DurationMax": "2.967", "MediaRefs": [{"Id": "136167884"}]}, {"Id": "2552274353", "Name": "Play_sfx_hero_lilith_skill2_3", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.0096042", "DurationMax": "3.0096042", "MediaRefs": [{"Id": "521174348"}]}, {"Id": "2865063258", "Name": "Play_sfx_hero_lina_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.52902085", "DurationMax": "0.703", "MediaRefs": [{"Id": "52282061"}, {"Id": "239523656"}, {"Id": "926714882"}]}, {"Id": "2364410288", "Name": "Play_sfx_hero_lina_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.6225", "DurationMax": "1.6225", "MediaRefs": [{"Id": "676954604"}]}, {"Id": "2615376001", "Name": "Play_sfx_hero_lina_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.17414583", "DurationMax": "0.32470834", "MediaRefs": [{"Id": "44283607"}, {"Id": "96894957"}, {"Id": "492981583"}]}, {"Id": "3006614673", "Name": "Play_sfx_hero_lina_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.6673334", "DurationMax": "2.6673334", "MediaRefs": [{"Id": "33249628"}]}, {"Id": "224468787", "Name": "Play_sfx_hero_lina_skill2_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "5.1339583", "DurationMax": "5.1339583", "MediaRefs": [{"Id": "835295918"}]}, {"Id": "2183819027", "Name": "Play_sfx_hero_lina_skill_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.1446874", "DurationMax": "2.1446874", "MediaRefs": [{"Id": "986648724"}]}, {"Id": "2401911465", "Name": "Play_sfx_hero_rowen_attack_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.5", "DurationMax": "0.6", "MediaRefs": [{"Id": "9574061"}, {"Id": "********"}, {"Id": "397250989"}]}, {"Id": "2401911466", "Name": "Play_sfx_hero_rowen_attack_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.5", "DurationMax": "0.6", "MediaRefs": [{"Id": "9574061"}, {"Id": "********"}, {"Id": "397250989"}]}, {"Id": "37306737", "Name": "Play_sfx_hero_rowen_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.679", "DurationMax": "1.679", "MediaRefs": [{"Id": "886113566"}]}, {"Id": "3419161052", "Name": "Play_sfx_hero_rowen_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.5648125", "DurationMax": "0.5648125", "MediaRefs": [{"Id": "323837174"}]}, {"Id": "2928389478", "Name": "Play_sfx_hero_rowen_skill2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.273729", "DurationMax": "2.273729", "MediaRefs": [{"Id": "425800599"}]}, {"Id": "31517034", "Name": "Play_sfx_hero_si<PERSON>ra_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.749", "DurationMax": "0.749", "MediaRefs": [{"Id": "667945588"}]}, {"Id": "1976265644", "Name": "Play_sfx_hero_<PERSON><PERSON>ra_attack_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.56666666", "DurationMax": "0.56666666", "MediaRefs": [{"Id": "52040472"}, {"Id": "413081790"}, {"Id": "566913848"}]}, {"Id": "2123706688", "Name": "Play_sfx_hero_si<PERSON>ra_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.938875", "DurationMax": "1.938875", "MediaRefs": [{"Id": "928171570"}]}, {"Id": "2188258529", "Name": "Play_sfx_hero_sierra_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3043958", "DurationMax": "1.3043958", "MediaRefs": [{"Id": "1004677208"}]}, {"Id": "879797761", "Name": "Play_sfx_hero_sierra_skill2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.7920417", "DurationMax": "1.7920417", "MediaRefs": [{"Id": "908609484"}]}, {"Id": "658674115", "Name": "Play_sfx_hero_sierra_skill2_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.1427917", "DurationMax": "3.1427917", "MediaRefs": [{"Id": "968385269"}]}, {"Id": "2825430499", "Name": "Play_sfx_hero_sierra_skill_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.070021", "DurationMax": "3.070021", "MediaRefs": [{"Id": "924289523"}]}, {"Id": "2461663100", "Name": "Play_sfx_hero_vivi_attack_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.2", "DurationMax": "1.35", "MediaRefs": [{"Id": "102780360"}, {"Id": "434179014"}, {"Id": "584186618"}]}, {"Id": "2461663103", "Name": "Play_sfx_hero_vivi_attack_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.05", "DurationMax": "1.3666667", "MediaRefs": [{"Id": "54921207"}, {"Id": "663533879"}, {"Id": "760043406"}]}, {"Id": "818795474", "Name": "Play_sfx_hero_vivi_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3333334", "DurationMax": "1.3333334", "MediaRefs": [{"Id": "320111903"}]}, {"Id": "2925852099", "Name": "Play_sfx_hero_vivi_skill_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.7666667", "DurationMax": "1.7666667", "MediaRefs": [{"Id": "302271482"}]}, {"Id": "2925852096", "Name": "Play_sfx_hero_vivi_skill_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0333333", "DurationMax": "2.0333333", "MediaRefs": [{"Id": "928078625"}]}, {"Id": "2925852103", "Name": "Play_sfx_hero_vivi_skill_5", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.85", "DurationMax": "2.85", "MediaRefs": [{"Id": "1043368750"}]}, {"Id": "4174357088", "Name": "Play_sfx_hero_zoe_attack_1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.16902083", "DurationMax": "0.19339584", "MediaRefs": [{"Id": "382013667"}, {"Id": "752670150"}, {"Id": "851863018"}]}, {"Id": "4174357091", "Name": "Play_sfx_hero_zoe_attack_2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.16902083", "DurationMax": "0.19339584", "MediaRefs": [{"Id": "382013667"}, {"Id": "752670150"}, {"Id": "851863018"}]}, {"Id": "4174357090", "Name": "Play_sfx_hero_zoe_attack_3", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.1413125", "DurationMax": "0.1425", "MediaRefs": [{"Id": "278319306"}, {"Id": "389514283"}, {"Id": "1017476771"}]}, {"Id": "852804878", "Name": "Play_sfx_hero_zoe_death", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.6137917", "DurationMax": "1.6137917", "MediaRefs": [{"Id": "928296001"}]}, {"Id": "331227991", "Name": "Play_sfx_hero_zoe_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.1559374", "DurationMax": "2.1559374", "MediaRefs": [{"Id": "773801030"}]}, {"Id": "3918146245", "Name": "Play_sfx_hero_zoe_skill_hit", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.8131875", "DurationMax": "1.8131875", "MediaRefs": [{"Id": "660259301"}]}, {"Id": "323790142", "Name": "Play_sfx_monster_bat_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "5", "DurationMax": "5", "MediaRefs": [{"Id": "64961667"}]}, {"Id": "2067836137", "Name": "Play_sfx_monster_bat_skill_fail", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.627875", "DurationMax": "0.627875", "MediaRefs": [{"Id": "467723753"}]}, {"Id": "1271676950", "Name": "Play_sfx_monster_bat_skillstart", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.8833333", "DurationMax": "0.8833333", "MediaRefs": [{"Id": "1051034241"}]}, {"Id": "3684128364", "Name": "Play_sfx_monster_bear_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.66902083", "DurationMax": "0.66902083", "MediaRefs": [{"Id": "193839726"}]}, {"Id": "3074848660", "Name": "Play_sfx_monster_bear_skill1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "7.878", "DurationMax": "7.878", "MediaRefs": [{"Id": "390802556"}]}, {"Id": "3586209575", "Name": "Play_sfx_monster_bear_skill_break", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.984", "DurationMax": "1.984", "MediaRefs": [{"Id": "599058672"}]}, {"Id": "2210945902", "Name": "Play_sfx_monster_dog_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.7936042", "DurationMax": "0.7936042", "MediaRefs": [{"Id": "857822037"}]}, {"Id": "3345497978", "Name": "Play_sfx_monster_gorilla_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.6333333", "DurationMax": "0.6333333", "MediaRefs": [{"Id": "120352083"}]}, {"Id": "3918488947", "Name": "Play_sfx_monster_gorilla_attacksuccess", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.55789584", "DurationMax": "0.55789584", "MediaRefs": [{"Id": "257672328"}]}, {"Id": "3294060073", "Name": "Play_sfx_monster_gorilla_rage", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.9166666", "DurationMax": "1.9166666", "MediaRefs": [{"Id": "260546809"}]}, {"Id": "1299979368", "Name": "Play_sfx_monster_gorilla_rage_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.9166667", "DurationMax": "0.9166667", "MediaRefs": [{"Id": "487354823"}]}, {"Id": "2820624265", "Name": "Play_sfx_monster_gorilla_rage_attacksuccess", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1666666", "DurationMax": "1.1666666", "MediaRefs": [{"Id": "997781802"}]}, {"Id": "2873862731", "Name": "Play_sfx_monster_gorilla_rage_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.530125", "DurationMax": "3.530125", "MediaRefs": [{"Id": "533044395"}]}, {"Id": "1065036971", "Name": "Play_sfx_monster_gorilla_ranged", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.5855833", "DurationMax": "0.5855833", "MediaRefs": [{"Id": "284611539"}]}, {"Id": "2901661569", "Name": "Play_sfx_monster_gorilla_skill_break", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0137084", "DurationMax": "2.0137084", "MediaRefs": [{"Id": "594121427"}]}, {"Id": "1475352083", "Name": "Play_sfx_monster_monkey_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.46666667", "DurationMax": "0.46666667", "MediaRefs": [{"Id": "742220051"}]}, {"Id": "1613343252", "Name": "Play_sfx_monster_monkey_attacksuccess", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.28333333", "DurationMax": "0.28333333", "MediaRefs": [{"Id": "457821505"}]}, {"Id": "328779268", "Name": "Play_sfx_monster_snake_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.23225", "DurationMax": "1.23225", "MediaRefs": [{"Id": "706532936"}]}, {"Id": "1044660294", "Name": "Play_sfx_monster_snakeking_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.146", "DurationMax": "2.146", "MediaRefs": [{"Id": "55433167"}]}, {"Id": "3993828355", "Name": "Play_sfx_monster_spider_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.46729165", "DurationMax": "0.46729165", "MediaRefs": [{"Id": "219512163"}]}, {"Id": "1837870348", "Name": "Play_sfx_monster_spiderelite_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3134792", "DurationMax": "1.3134792", "MediaRefs": [{"Id": "998435409"}]}, {"Id": "631651847", "Name": "Play_sfx_monster_spiderelite_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.781", "DurationMax": "1.781", "MediaRefs": [{"Id": "736925174"}]}, {"Id": "2822285434", "Name": "Play_sfx_monster_spiderelite_skill_fail", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.5032917", "DurationMax": "1.5032917", "MediaRefs": [{"Id": "653497506"}]}, {"Id": "3335443923", "Name": "Play_sfx_monster_spiderelite_skill_success", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0156667", "DurationMax": "2.0156667", "MediaRefs": [{"Id": "351812292"}]}, {"Id": "3540891845", "Name": "Play_sfx_monster_spiderelite_skillstart", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.7299166", "DurationMax": "3.7299166", "MediaRefs": [{"Id": "132961897"}]}, {"Id": "27292657", "Name": "Play_sfx_monster_vulture_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.6333333", "DurationMax": "0.6333333", "MediaRefs": [{"Id": "769759363"}]}, {"Id": "1355046664", "Name": "Play_sfx_monster_vulture_skill", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.15", "DurationMax": "1.15", "MediaRefs": [{"Id": "47347581"}]}, {"Id": "767203753", "Name": "Play_sfx_monster_vulture_skillsuccess", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.46666667", "DurationMax": "0.46666667", "MediaRefs": [{"Id": "435542606"}]}, {"Id": "3221251678", "Name": "Play_sfx_monster_wolf_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.54272914", "DurationMax": "1.1018585", "MediaRefs": [{"Id": "115438071"}, {"Id": "765096628"}, {"Id": "984088565"}]}, {"Id": "1593455665", "Name": "Play_sfx_monster_wolfking_attack", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.2027084", "DurationMax": "1.3333125", "MediaRefs": [{"Id": "284047166"}, {"Id": "632651498"}, {"Id": "673124368"}]}, {"Id": "2682789737", "Name": "Play_sfx_monster_wolfking_skill1", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.3381042", "DurationMax": "2.3381042", "MediaRefs": [{"Id": "358900373"}]}, {"Id": "2682789738", "Name": "Play_sfx_monster_wolfking_skill2", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.8293333", "DurationMax": "3.8293333", "MediaRefs": [{"Id": "508755055"}]}, {"Id": "4011877523", "Name": "Play_sfx_notice_collect_iron_ore_complete", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3833333", "DurationMax": "1.3833333", "MediaRefs": [{"Id": "493093430"}]}, {"Id": "1736869689", "Name": "Play_sfx_notice_common_battle_lose", "DurationType": "OneShot", "DurationMin": "2.1609793", "DurationMax": "2.1609793", "MediaRefs": [{"Id": "178917646"}], "ActionSetState": [{"GroupId": "1926883983", "Id": "221232726", "Name": "Lose"}]}, {"Id": "3635450884", "Name": "Play_sfx_notice_common_battle_win", "DurationType": "OneShot", "DurationMin": "3.4260833", "DurationMax": "3.4260833", "MediaRefs": [{"Id": "181648324"}], "ActionSetState": [{"GroupId": "1926883983", "Id": "979765101", "Name": "Win"}]}, {"Id": "1559194859", "Name": "Play_sfx_notice_confirm", "DurationType": "OneShot", "DurationMin": "0.43333334", "DurationMax": "0.43333334", "MediaRefs": [{"Id": "630200909"}]}, {"Id": "2651644742", "Name": "Play_sfx_notice_levelUp", "DurationType": "OneShot", "DurationMin": "1.1414059", "DurationMax": "1.1414059", "MediaRefs": [{"Id": "943654622"}]}, {"Id": "3298357776", "Name": "Play_sfx_notice_negative", "DurationType": "OneShot", "DurationMin": "0.56666666", "DurationMax": "0.56666666", "MediaRefs": [{"Id": "553602955"}]}, {"Id": "3093195746", "Name": "Play_sfx_notice_neutral", "DurationType": "OneShot", "DurationMin": "0.778375", "DurationMax": "0.778375", "MediaRefs": [{"Id": "838746950"}]}, {"Id": "14361946", "Name": "Play_sfx_notice_pay_succeed_token", "DurationType": "OneShot", "DurationMin": "0.72245836", "DurationMax": "0.72245836", "MediaRefs": [{"Id": "81450364"}]}, {"Id": "3202305464", "Name": "Play_sfx_notice_positive", "DurationType": "OneShot", "DurationMin": "1.1307708", "DurationMax": "1.1307708", "MediaRefs": [{"Id": "100042163"}]}, {"Id": "1021916689", "Name": "Play_sfx_notice_pve_missionAccomplished", "DurationType": "OneShot", "DurationMin": "2.8825417", "DurationMax": "2.8825417", "MediaRefs": [{"Id": "203736238"}]}, {"Id": "1192581685", "Name": "Play_sfx_notice_pve_missionStart", "DurationType": "OneShot", "DurationMin": "2.7786875", "DurationMax": "2.7786875", "MediaRefs": [{"Id": "484719282"}]}, {"Id": "2595619343", "Name": "Play_sfx_notice_pve_skillUseable", "DurationType": "OneShot", "DurationMin": "1.4333333", "DurationMax": "1.4333333", "MediaRefs": [{"Id": "609615974"}]}, {"Id": "128816036", "Name": "Play_sfx_notice_rpg_getCrystalShards", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.7995", "DurationMax": "0.7995", "MediaRefs": [{"Id": "157206279"}, {"Id": "225689011"}, {"Id": "949553456"}]}, {"Id": "3123162642", "Name": "Play_sfx_notice_rpg_getPet", "DurationType": "OneShot", "DurationMin": "1.3743541", "DurationMax": "1.3743541", "MediaRefs": [{"Id": "332039537"}]}, {"Id": "756656290", "Name": "Play_sfx_notice_rpg_mission_accomplished", "DurationType": "OneShot", "DurationMin": "2.2545834", "DurationMax": "2.2545834", "MediaRefs": [{"Id": "726133731"}]}, {"Id": "1641692609", "Name": "Play_sfx_notice_se_addHero", "DurationType": "OneShot", "DurationMin": "1.8666667", "DurationMax": "1.8666667", "MediaRefs": [{"Id": "160779747"}]}, {"Id": "939287814", "Name": "Play_sfx_notice_se_startGame", "DurationType": "OneShot", "DurationMin": "2.9", "DurationMax": "2.9", "MediaRefs": [{"Id": "387319348"}]}, {"Id": "1213940368", "Name": "Play_sfx_notice_treasureBox_open", "DurationType": "OneShot", "DurationMin": "1.9452292", "DurationMax": "1.9452292", "MediaRefs": [{"Id": "307271184"}]}, {"Id": "1171632403", "Name": "Play_sfx_rpg_bear_roar", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.4275209", "DurationMax": "1.4275209", "MediaRefs": [{"Id": "210469521"}]}, {"Id": "2891751095", "Name": "Play_sfx_rpg_bee_scare", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.3333334", "DurationMax": "1.3333334", "MediaRefs": [{"Id": "201196212"}]}, {"Id": "1605984297", "Name": "Play_sfx_rpg_boat_puffUp", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.479", "DurationMax": "1.479", "MediaRefs": [{"Id": "544145033"}]}, {"Id": "807194329", "Name": "Play_sfx_rpg_boat_sailing_loop", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "19.926708", "DurationMax": "19.926708", "MediaRefs": [{"Id": "127217317"}]}, {"Id": "4069512484", "Name": "Play_sfx_rpg_break_airplane", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0166667", "DurationMax": "2.0166667", "MediaRefs": [{"Id": "144733970"}]}, {"Id": "900866832", "Name": "Play_sfx_rpg_gorilla_eat_crystal", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.0166667", "DurationMax": "3.0166667", "MediaRefs": [{"Id": "592222445"}]}, {"Id": "480580936", "Name": "Play_sfx_rpg_gorilla_sleep", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.0333333", "DurationMax": "3.0333333", "MediaRefs": [{"Id": "890169225"}]}, {"Id": "2165797404", "Name": "Play_sfx_rpg_hyena_bark", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.8748958", "DurationMax": "0.8748958", "MediaRefs": [{"Id": "638372973"}]}, {"Id": "654169029", "Name": "Play_sfx_rpg_man_clilmbing_ladder", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "5.117625", "DurationMax": "5.117625", "MediaRefs": [{"Id": "6103003"}]}, {"Id": "2309282293", "Name": "Play_sfx_rpg_man_clilmbing_vine", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "4.187667", "DurationMax": "4.187667", "MediaRefs": [{"Id": "163887531"}]}, {"Id": "2482091568", "Name": "Play_sfx_rpg_man_collect_berries", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.65", "DurationMax": "0.6613333", "MediaRefs": [{"Id": "249875023"}, {"Id": "621988094"}, {"Id": "896808524"}]}, {"Id": "417841057", "Name": "Play_sfx_rpg_man_collect_conch", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.52597916", "DurationMax": "0.52597916", "MediaRefs": [{"Id": "155182812"}]}, {"Id": "4009575298", "Name": "Play_sfx_rpg_man_collect_mushroom", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.4", "DurationMax": "0.5", "MediaRefs": [{"Id": "69039270"}, {"Id": "169066047"}, {"Id": "911739863"}]}, {"Id": "65210556", "Name": "Play_sfx_rpg_man_collect_ore", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.42704168", "DurationMax": "0.42704168", "MediaRefs": [{"Id": "787740001"}]}, {"Id": "2025481786", "Name": "Play_sfx_rpg_man_collect_ore_complete", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.1169375", "DurationMax": "1.1169375", "MediaRefs": [{"Id": "230320031"}]}, {"Id": "2951621980", "Name": "Play_sfx_rpg_man_collect_vine", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.946125", "DurationMax": "0.946125", "MediaRefs": [{"Id": "866356243"}]}, {"Id": "1004365631", "Name": "Play_sfx_rpg_man_construct_building", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.5486667", "DurationMax": "3.5486667", "MediaRefs": [{"Id": "112896267"}]}, {"Id": "644046392", "Name": "Play_sfx_rpg_man_cut_thorns", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.7436042", "DurationMax": "0.7436042", "MediaRefs": [{"Id": "1057282303"}]}, {"Id": "325276991", "Name": "Play_sfx_rpg_man_cutTree", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.55097914", "DurationMax": "0.6340208", "MediaRefs": [{"Id": "141796949"}, {"Id": "593955212"}, {"Id": "1026991295"}]}, {"Id": "3205641902", "Name": "Play_sfx_rpg_man_footsteps", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.27283335", "DurationMax": "0.49995834", "SwitchContainers": [{"SwitchValue": {"GroupType": "Switch", "GroupId": "3865314626", "Id": "2654748154"}, "MediaRefs": [{"Id": "7275739"}, {"Id": "71166277"}, {"Id": "188205898"}]}, {"SwitchValue": {"GroupType": "Switch", "GroupId": "3865314626", "Id": "4248645337"}, "MediaRefs": [{"Id": "38087952"}, {"Id": "574380731"}, {"Id": "791468363"}]}, {"SwitchValue": {"GroupType": "Switch", "GroupId": "3865314626", "Id": "803837735"}, "MediaRefs": [{"Id": "488961693"}, {"Id": "892317253"}, {"Id": "1067128043"}]}, {"SwitchValue": {"GroupType": "Switch", "GroupId": "3865314626", "Id": "1280754535", "Default": "true"}, "MediaRefs": [{"Id": "769978538"}, {"Id": "1057537891"}, {"Id": "1067908762"}]}, {"SwitchValue": {"GroupType": "Switch", "GroupId": "3865314626", "Id": "114672787"}, "MediaRefs": [{"Id": "312218708"}, {"Id": "478002122"}, {"Id": "814508586"}]}]}, {"Id": "3043540489", "Name": "Play_sfx_rpg_man_footsteps_grass", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.38139585", "DurationMax": "0.421875", "MediaRefs": [{"Id": "38087952"}, {"Id": "574380731"}, {"Id": "791468363"}]}, {"Id": "3627163331", "Name": "Play_sfx_rpg_man_footsteps_plank", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.37775", "DurationMax": "0.49995834", "MediaRefs": [{"Id": "312218708"}, {"Id": "478002122"}, {"Id": "814508586"}]}, {"Id": "1012198071", "Name": "Play_sfx_rpg_man_footsteps_stones", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.28908333", "DurationMax": "0.34252083", "MediaRefs": [{"Id": "769978538"}, {"Id": "1057537891"}, {"Id": "1067908762"}]}, {"Id": "834857450", "Name": "Play_sfx_rpg_man_footsteps_water", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.31383333", "DurationMax": "0.32870832", "MediaRefs": [{"Id": "7275739"}, {"Id": "71166277"}, {"Id": "188205898"}]}, {"Id": "4151046001", "Name": "Play_sfx_rpg_man_open_bigchest", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.0831041", "DurationMax": "3.0831041", "MediaRefs": [{"Id": "75547551"}]}, {"Id": "2810257095", "Name": "Play_sfx_rpg_man_open_bigchest_Instant", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.8976876", "DurationMax": "1.8976876", "MediaRefs": [{"Id": "895320919"}]}, {"Id": "860902794", "Name": "Play_sfx_rpg_man_open_luxurychest", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.1552083", "DurationMax": "3.1552083", "MediaRefs": [{"Id": "80439985"}]}, {"Id": "147275328", "Name": "Play_sfx_rpg_man_open_luxurychest_Instant", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.1362083", "DurationMax": "2.1362083", "MediaRefs": [{"Id": "263012307"}]}, {"Id": "27147776", "Name": "Play_sfx_rpg_man_openChest", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.4407291", "DurationMax": "3.4407291", "MediaRefs": [{"Id": "766902835"}]}, {"Id": "758311902", "Name": "Play_sfx_rpg_man_openChest_Instant", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.0526042", "DurationMax": "2.0526042", "MediaRefs": [{"Id": "977361644"}]}, {"Id": "3887699966", "Name": "Play_sfx_rpg_man_push_turtle_accelerate", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.534125", "DurationMax": "3.534125", "MediaRefs": [{"Id": "704048172"}]}, {"Id": "2389979583", "Name": "Play_sfx_rpg_man_push_turtle_decelerate", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.810604", "DurationMax": "3.810604", "MediaRefs": [{"Id": "1030124390"}]}, {"Id": "448747835", "Name": "Play_sfx_rpg_man_push_turtle_loop", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "89726666"}]}, {"Id": "4183857004", "Name": "Play_sfx_rpg_monkey_call", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.2666667", "DurationMax": "3.2666667", "MediaRefs": [{"Id": "727547958"}]}, {"Id": "456630077", "Name": "Play_sfx_rpg_mush_born", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.4333333", "DurationMax": "1.4333333", "MediaRefs": [{"Id": "1048602592"}]}, {"Id": "2921939919", "Name": "Play_sfx_rpg_pet_appear", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.2666667", "DurationMax": "1.2666667", "MediaRefs": [{"Id": "760215675"}]}, {"Id": "3128850129", "Name": "Play_sfx_rpg_pet_blow_loop", "MaxAttenuation": "140", "DurationType": "Infinite", "MediaRefs": [{"Id": "29505024"}, {"Id": "632483673"}]}, {"Id": "2687130213", "Name": "Play_sfx_rpg_pet_squirtWater", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.316", "DurationMax": "2.316", "MediaRefs": [{"Id": "142707063"}]}, {"Id": "392689063", "Name": "Play_sfx_rpg_spider_silk", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.9166667", "DurationMax": "2.9166667", "MediaRefs": [{"Id": "845836426"}]}, {"Id": "131963970", "Name": "Play_sfx_rpg_vulture_angry_call", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1.35", "DurationMax": "1.35", "MediaRefs": [{"Id": "218059986"}]}, {"Id": "819302248", "Name": "Play_sfx_rpg_vulture_normal_call", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.43333334", "DurationMax": "0.43333334", "MediaRefs": [{"Id": "383342016"}]}, {"Id": "2489020703", "Name": "Play_sfx_rpg_vulture_scare_call", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.9", "DurationMax": "0.9", "MediaRefs": [{"Id": "963824576"}]}, {"Id": "3221460433", "Name": "Play_sfx_rpg_wolf_jump", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "0.317875", "DurationMax": "0.317875", "MediaRefs": [{"Id": "920736361"}]}, {"Id": "2038675217", "Name": "Play_sfx_rpg_wolf_roar", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.95", "DurationMax": "3.95", "MediaRefs": [{"Id": "194350357"}]}, {"Id": "2660315067", "Name": "Play_sfx_scene_bonfire_loop", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "3.9352918", "DurationMax": "3.9352918", "MediaRefs": [{"Id": "826289354"}]}, {"Id": "1921308421", "Name": "Play_sfx_scene_build_bridge", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.7546458", "DurationMax": "2.7546458", "MediaRefs": [{"Id": "562810313"}]}, {"Id": "620519943", "Name": "Play_sfx_scene_rpg_plantGrowing", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "2.8529375", "DurationMax": "2.8529375", "MediaRefs": [{"Id": "287995186"}]}, {"Id": "2372109405", "Name": "Play_sfx_scene_rpg_waterfall", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "10.268167", "DurationMax": "10.268167", "MediaRefs": [{"Id": "983502686"}]}, {"Id": "3717890612", "Name": "Play_sfx_scene_unfold_ladder", "MaxAttenuation": "140", "DurationType": "OneShot", "DurationMin": "1", "DurationMax": "1", "MediaRefs": [{"Id": "918477639"}]}, {"Id": "1581643532", "Name": "Play_sfx_ui_animation_rpg_items_fly", "DurationType": "OneShot", "DurationMin": "0.6298125", "DurationMax": "0.6298125", "MediaRefs": [{"Id": "152830642"}]}, {"Id": "3550193417", "Name": "Play_sfx_ui_building_common", "DurationType": "OneShot", "DurationMin": "0.84", "DurationMax": "0.84", "MediaRefs": [{"Id": "960238724"}]}, {"Id": "2925662583", "Name": "Play_SFX_UI_button", "DurationType": "OneShot", "DurationMin": "0.18333334", "DurationMax": "0.18333334", "MediaRefs": [{"Id": "372647210"}]}, {"Id": "142950399", "Name": "Play_sfx_ui_button_claimReward", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1620389061", "Name": "Play_sfx_ui_button_common", "DurationType": "OneShot", "DurationMin": "0.18333334", "DurationMax": "0.18333334", "MediaRefs": [{"Id": "372647210"}]}, {"Id": "1246747528", "Name": "Play_sfx_ui_button_pve_hero_add", "DurationType": "OneShot", "DurationMin": "0.9", "DurationMax": "0.9", "MediaRefs": [{"Id": "383910358"}]}, {"Id": "4018085867", "Name": "Play_sfx_ui_button_pve_hero_remove", "DurationType": "OneShot", "DurationMin": "0.492125", "DurationMax": "0.492125", "MediaRefs": [{"Id": "435433257"}]}, {"Id": "3162325059", "Name": "Play_sfx_ui_button_pve_skill", "DurationType": "OneShot", "DurationMin": "1.5666667", "DurationMax": "1.5666667", "MediaRefs": [{"Id": "210851714"}]}, {"Id": "2678915623", "Name": "Play_sfx_ui_button_rpg_click_bubble", "DurationType": "OneShot", "DurationMin": "0.107854165", "DurationMax": "0.107854165", "MediaRefs": [{"Id": "456848626"}]}, {"Id": "1411738129", "Name": "Play_sfx_ui_button_se_startGame", "DurationType": "OneShot", "DurationMin": "1.579125", "DurationMax": "1.579125", "MediaRefs": [{"Id": "521260712"}]}, {"Id": "4128709912", "Name": "Play_sfx_ui_character_common", "DurationType": "OneShot", "DurationMin": "0.000430839", "DurationMax": "0.000430839", "MediaRefs": [{"Id": "607100907"}]}, {"Id": "806078765", "Name": "Play_sfx_ui_fecruit_box", "DurationType": "OneShot", "DurationMin": "2.289", "DurationMax": "2.289", "MediaRefs": [{"Id": "474527836"}]}, {"Id": "2070217658", "Name": "Play_sfx_ui_fecruit_fire", "DurationType": "OneShot", "DurationMin": "2.0833333", "DurationMax": "2.0833333", "MediaRefs": [{"Id": "313318960"}]}, {"Id": "2675319719", "Name": "Play_sfx_ui_fecruit_landing", "DurationType": "OneShot", "DurationMin": "1.3833333", "DurationMax": "1.3833333", "MediaRefs": [{"Id": "797261951"}]}, {"Id": "3589160100", "Name": "Play_sfx_ui_formation_common", "DurationType": "OneShot", "DurationMin": "0.000430839", "DurationMax": "0.000430839", "MediaRefs": [{"Id": "688481423"}]}, {"Id": "1683695080", "Name": "Play_sfx_ui_panel_common_close", "DurationType": "OneShot", "DurationMin": "0.48333332", "DurationMax": "0.48333332", "MediaRefs": [{"Id": "399993586"}]}, {"Id": "3628131276", "Name": "Play_sfx_ui_panel_common_open", "DurationType": "OneShot", "DurationMin": "0.51666665", "DurationMax": "0.51666665", "MediaRefs": [{"Id": "386869349"}]}, {"Id": "387962279", "Name": "Play_sfx_ui_panel_dialog_close", "DurationType": "OneShot", "DurationMin": "0.21696146", "DurationMax": "0.21696146", "MediaRefs": [{"Id": "652545392"}]}, {"Id": "1058859754", "Name": "Play_sfx_ui_panel_dialog_next", "DurationType": "OneShot", "DurationMin": "0.204625", "DurationMax": "0.204625", "MediaRefs": [{"Id": "218610223"}]}, {"Id": "3148206161", "Name": "Play_sfx_ui_panel_dialog_open", "DurationType": "OneShot", "DurationMin": "0.13750567", "DurationMax": "0.13750567", "MediaRefs": [{"Id": "695815029"}]}, {"Id": "4161818250", "Name": "Play_sfx_ui_panel_getReward_close", "DurationType": "OneShot", "DurationMin": "1.35", "DurationMax": "1.35", "MediaRefs": [{"Id": "312913805"}]}, {"Id": "748980654", "Name": "Play_sfx_ui_panel_getReward_open", "DurationType": "OneShot", "DurationMin": "2.8", "DurationMax": "2.8", "MediaRefs": [{"Id": "245856841"}]}, {"Id": "1134900532", "Name": "Play_sfx_ui_panel_labelSwitch", "DurationType": "OneShot", "DurationMin": "0.21666667", "DurationMax": "0.21666667", "MediaRefs": [{"Id": "83847796"}]}, {"Id": "4263229530", "Name": "Play_sfx_ui_panel_small_close", "DurationType": "OneShot", "DurationMin": "0.35", "DurationMax": "0.35", "MediaRefs": [{"Id": "182384876"}]}, {"Id": "2593537886", "Name": "Play_sfx_ui_panel_small_open", "DurationType": "OneShot", "DurationMin": "0.41666666", "DurationMax": "0.41666666", "MediaRefs": [{"Id": "1013798872"}]}, {"Id": "4216952712", "Name": "Play_sfx_ui_tick", "DurationType": "OneShot", "DurationMin": "0.102", "DurationMax": "0.102", "MediaRefs": [{"Id": "1025983463"}]}, {"Id": "2807572219", "Name": "Play_Silence", "DurationType": "OneShot", "DurationMin": "1", "DurationMax": "1", "PluginRefs": {"Custom": [{"Id": "347332099"}]}}, {"Id": "920653311", "Name": "Play_voice_Gender_test", "DurationType": "OneShot", "DurationMin": "1.6826875", "DurationMax": "2.2063959", "SwitchContainers": [{"SwitchValue": {"GroupType": "Switch", "GroupId": "1776943274", "Id": "2707640201"}, "MediaRefs": [{"Id": "672229353"}]}, {"SwitchValue": {"GroupType": "Switch", "GroupId": "1776943274", "Id": "3111576190"}, "MediaRefs": [{"Id": "616916900"}]}]}, {"Id": "2726492490", "Name": "Set_Material_Grass", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "3865314626", "Id": "4248645337", "Name": "Grass"}]}, {"Id": "1224834524", "Name": "Set_Material_Plank", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "3865314626", "Id": "114672787", "Name": "Plank"}]}, {"Id": "1784795218", "Name": "Set_Material_Sand", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "3865314626", "Id": "803837735", "Name": "Sand"}]}, {"Id": "1676363650", "Name": "Set_Material_Stones", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "3865314626", "Id": "1280754535", "Name": "Stones"}]}, {"Id": "842875169", "Name": "Set_Material_Water", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "3865314626", "Id": "2654748154", "Name": "Water"}]}, {"Id": "3013207241", "Name": "Set_State_City", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "3888786832", "Name": "City"}]}, {"Id": "1834131217", "Name": "Set_State_RPG", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1176052424", "Id": "796049858", "Name": "RPG"}]}, {"Id": "2655749499", "Name": "Set_State_RPG_Cave", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "3137033164", "Name": "RPG_Cove"}]}, {"Id": "519591791", "Name": "Set_State_RPG_Forest", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "1808625698", "Name": "RPG_Forest"}]}, {"Id": "189911082", "Name": "Set_State_RPG_Volcano", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "1976664129", "Name": "RPG_Volcano"}]}, {"Id": "2793429712", "Name": "Set_State_SE_Cave", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "204962941", "Name": "SE_Cove"}]}, {"Id": "1346983544", "Name": "Set_State_SE_Forest", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "888797855", "Name": "SE_Forest"}]}, {"Id": "2691379495", "Name": "Set_State_SE_Volcano", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetState": [{"GroupId": "1926883983", "Id": "3953081786", "Name": "SE_Volcano"}]}, {"Id": "2296949677", "Name": "Set_Switch_Famale", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "1776943274", "Id": "2707640201", "Name": "<PERSON><PERSON><PERSON>"}]}, {"Id": "626775482", "Name": "Set_Switch_Male", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0", "ActionSetSwitch": [{"GroupId": "1776943274", "Id": "3111576190", "Name": "Male"}]}, {"Id": "1751939562", "Name": "Stop_sfx_animation_dog_bark", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3373688846", "Name": "Stop_sfx_animation_dog_happy", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1708165125", "Name": "Stop_sfx_animation_lava_solidifying", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3620917626", "Name": "Stop_sfx_animation_pet_breathing_fire", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "532725435", "Name": "Stop_sfx_animation_spider_web_burn", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1604007001", "Name": "Stop_sfx_animation_tree_spirit_walk", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3492331893", "Name": "Stop_sfx_cutScene_bee_flying", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "68072351", "Name": "Stop_sfx_cutScene_crystal_charging", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3437762896", "Name": "Stop_sfx_cutScene_dog_eat", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3859957511", "Name": "Stop_sfx_cutScene_dragon_breathing_fireball", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3591595953", "Name": "Stop_sfx_cutScene_dragon_shadow_flying", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1835523044", "Name": "Stop_sfx_cutScene_fire_big_burn", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3687811529", "Name": "Stop_sfx_cutScene_fire_small_burn", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3399616111", "Name": "Stop_sfx_cutScene_snake_hiss", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3956378666", "Name": "Stop_sfx_cutScene_volcano_eruption", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1670224303", "Name": "Stop_sfx_rpg_boat_sailing_loop", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2808665554", "Name": "Stop_sfx_rpg_gorilla_eat_crystal", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "448473430", "Name": "Stop_sfx_rpg_gorilla_sleep", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "621647494", "Name": "Stop_sfx_rpg_hyena_bark", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3600675083", "Name": "Stop_sfx_rpg_man_clilmbing_ladder", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1066251575", "Name": "Stop_sfx_rpg_man_clilmbing_vine", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3200764357", "Name": "Stop_sfx_rpg_man_push_turtle_decelerate", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "477708058", "Name": "Stop_sfx_rpg_monkey_call", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3904757375", "Name": "Stop_sfx_rpg_pet_blow_loop", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "2557971181", "Name": "Stop_sfx_rpg_spider_silk", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1509715728", "Name": "Stop_sfx_rpg_vulture_angry_call", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "1853601882", "Name": "Stop_sfx_rpg_vulture_normal_call", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}, {"Id": "3633479885", "Name": "Stop_sfx_rpg_vulture_scare_call", "MaxAttenuation": "0", "DurationType": "OneShot", "DurationMin": "0", "DurationMax": "0"}], "StateGroups": [{"Id": "1176052424", "Name": "Location", "States": [{"Id": "748895195", "Name": "None"}, {"Id": "796049858", "Name": "RPG"}, {"Id": "3888786832", "Name": "City"}]}, {"Id": "1926883983", "Name": "Scene", "States": [{"Id": "204962941", "Name": "SE_Cove"}, {"Id": "221232726", "Name": "Lose"}, {"Id": "748895195", "Name": "None"}, {"Id": "888797855", "Name": "SE_Forest"}, {"Id": "979765101", "Name": "Win"}, {"Id": "1808625698", "Name": "RPG_Forest"}, {"Id": "1976664129", "Name": "RPG_Volcano"}, {"Id": "3137033164", "Name": "RPG_Cove"}, {"Id": "3888786832", "Name": "City"}, {"Id": "3953081786", "Name": "SE_Volcano"}]}], "SwitchGroups": [{"Id": "1776943274", "Name": "Gender", "Switches": [{"Id": "2707640201", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "3111576190", "Name": "Male"}]}, {"Id": "3865314626", "Name": "Material", "Switches": [{"Id": "114672787", "Name": "Plank"}, {"Id": "803837735", "Name": "Sand"}, {"Id": "1280754535", "Name": "Stones"}, {"Id": "2654748154", "Name": "Water"}, {"Id": "4248645337", "Name": "Grass"}]}]}, {"Id": "1355168291", "Type": "User", "Language": "SFX", "Hash": "{79ABBB65-F7CA-EC89-05E4-CBAC68CC4C2D}", "ShortName": "Init", "Path": "Init.bnk", "Plugins": {"Custom": [{"Id": "902607071", "Name": "Brick_Wall_Minus_1dB_Peak_Fast_Release (Custom)", "LibName": "Wwise Peak Limiter", "LibId": "7208963"}], "AudioDevices": [{"Id": "2317455096", "Name": "No_Output", "LibName": "No Output", "LibId": "11862023"}, {"Id": "3859886410", "Name": "System", "LibName": "System", "LibId": "11403271"}]}, "Busses": [{"Id": "393239870", "Name": "SFX"}, {"Id": "511093792", "Name": "3D"}, {"Id": "527871411", "Name": "2D"}, {"Id": "1182958561", "Name": "CutScene"}, {"Id": "1551306167", "Name": "UI"}, {"Id": "2981522829", "Name": "Notice"}, {"Id": "3170124113", "Name": "Voice"}, {"Id": "3803692087", "Name": "Master Audio Bus", "PluginRefs": {"Custom": [{"Id": "902607071"}]}}, {"Id": "3991942870", "Name": "Music"}, {"Id": "3994535597", "Name": "Animation"}], "GameParameters": [{"Id": "921301768", "Name": "Timeline_SFX"}, {"Id": "1006694123", "Name": "Music_Volume"}, {"Id": "1564184899", "Name": "SFX_Volume"}], "StateGroups": [{"Id": "1176052424", "Name": "Location", "States": [{"Id": "748895195", "Name": "None"}, {"Id": "796049858", "Name": "RPG"}, {"Id": "3888786832", "Name": "City"}]}, {"Id": "1926883983", "Name": "Scene", "States": [{"Id": "204962941", "Name": "SE_Cove"}, {"Id": "221232726", "Name": "Lose"}, {"Id": "748895195", "Name": "None"}, {"Id": "888797855", "Name": "SE_Forest"}, {"Id": "979765101", "Name": "Win"}, {"Id": "1808625698", "Name": "RPG_Forest"}, {"Id": "1976664129", "Name": "RPG_Volcano"}, {"Id": "3137033164", "Name": "RPG_Cove"}, {"Id": "3888786832", "Name": "City"}, {"Id": "3953081786", "Name": "SE_Volcano"}]}], "SwitchGroups": [{"Id": "1776943274", "Name": "Gender", "Switches": [{"Id": "2707640201", "Name": "<PERSON><PERSON><PERSON>"}, {"Id": "3111576190", "Name": "Male"}]}, {"Id": "3865314626", "Name": "Material", "Switches": [{"Id": "114672787", "Name": "Plank"}, {"Id": "803837735", "Name": "Sand"}, {"Id": "1280754535", "Name": "Stones"}, {"Id": "2654748154", "Name": "Water"}, {"Id": "4248645337", "Name": "Grass"}]}]}], "FileHash": "{729389EF-8841-31C9-6D7D-71FE2135FCB1}"}}