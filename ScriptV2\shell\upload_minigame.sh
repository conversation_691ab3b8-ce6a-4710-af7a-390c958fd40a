#!/bin/bash

# 检查是否提供了三个参数
if [ "$#" -ne 3 ]; then
  echo "Usage: $0 <weixinOutputDirectory> <version> <desc>"
  exit 1
fi


# 捕获参数
weixinOutputDirectory=$1
version=$2
desc=$3



echo $weixinOutputDirectory
/Applications/wechatwebdevtools.app/Contents/MacOS/cli upload --project "${weixinOutputDirectory}" --desc "${desc}" --version "${version}"
# 检查命令执行结果
if [ $? -eq 0 ]; then
  echo "upload minigame successfully."
else
  echo "upload minigame failed."
fi

