<?xml version="1.0" encoding="utf-8"?>
<WwiseDocument Type="WorkUnit" ID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}" SchemaVersion="119">
	<Busses>
		<WorkUnit Name="Default Work Unit" ID="{98BE4928-7DA5-4B2F-8127-4B7CC8324CA4}" PersistMode="Standalone">
			<ChildrenList>
				<Bus Name="Master Audio Bus" ID="{1514A4D8-1DA6-412A-A17E-75CA0C2149F3}">
					<ReferenceList>
						<Reference Name="AudioDevice" PluginName="System" CompanyID="0" PluginID="174" PluginType="7">
							<ObjectRef Name="System" ID="{BED5F077-5003-42B2-90F1-F1E1899FDA56}" WorkUnitID="{9C6CDE90-2CFC-44F6-98CD-D3684FD7F9F7}"/>
						</Reference>
					</ReferenceList>
					<ChildrenList>
						<Bus Name="Music" ID="{2A8972CA-8B50-4283-BFA6-9D9588EB440B}">
							<ObjectLists>
								<ObjectList Name="RTPC">
									<Reference>
										<Local>
											<RTPC Name="" ID="{61A612A8-865E-479E-9BBF-1E7946BC3A71}" ShortID="*********">
												<PropertyList>
													<Property Name="PropertyName" Type="string" Value="BusVolume"/>
												</PropertyList>
												<ReferenceList>
													<Reference Name="ControlInput">
														<ObjectRef Name="Music_Volume" ID="{D803C343-070F-41B1-84D0-16E1EA18A1E5}" WorkUnitID="{9C8A24B5-1AAF-42E4-8E90-B771023E852C}"/>
													</Reference>
													<Reference Name="Curve">
														<Custom>
															<Curve Name="" ID="{E798D746-9796-45AA-90D7-7E4599828B40}">
																<PropertyList>
																	<Property Name="Flags" Type="int32" Value="3"/>
																</PropertyList>
																<PointList>
																	<Point>
																		<XPos>0</XPos>
																		<YPos>-200</YPos>
																		<Flags>5</Flags>
																	</Point>
																	<Point>
																		<XPos>100</XPos>
																		<YPos>0</YPos>
																		<Flags>37</Flags>
																	</Point>
																</PointList>
															</Curve>
														</Custom>
													</Reference>
												</ReferenceList>
											</RTPC>
										</Local>
									</Reference>
								</ObjectList>
							</ObjectLists>
						</Bus>
						<Bus Name="SFX" ID="{9FCDAD4F-D523-4289-86D7-3D70D747BAB7}">
							<ChildrenList>
								<Bus Name="2D" ID="{D28D3B44-0A66-484C-B0D5-AE5ED8C289A6}">
									<ChildrenList>
										<Bus Name="Animation" ID="{9B108616-795C-44CA-B008-A3FCE840D89A}"/>
										<Bus Name="CutScene" ID="{61ACF335-C23C-4DD2-8187-6572C573B1E4}"/>
										<Bus Name="UI" ID="{330AF869-10C5-4E08-B999-18CF0551233A}"/>
										<Bus Name="Notice" ID="{87BE5C03-7FCE-42DF-BFD2-368B6FFC6B23}"/>
									</ChildrenList>
								</Bus>
								<Bus Name="3D" ID="{67F9359A-A826-4E7B-9BC8-448B210B2559}"/>
							</ChildrenList>
							<ObjectLists>
								<ObjectList Name="RTPC">
									<Reference>
										<Local>
											<RTPC Name="" ID="{08E34D94-9964-4548-918A-7EC297D6E72B}" ShortID="773953123">
												<PropertyList>
													<Property Name="PropertyName" Type="string" Value="BusVolume"/>
												</PropertyList>
												<ReferenceList>
													<Reference Name="ControlInput">
														<ObjectRef Name="SFX_Volume" ID="{54B4F574-EB2C-4BC0-96D2-581928704B3A}" WorkUnitID="{9C8A24B5-1AAF-42E4-8E90-B771023E852C}"/>
													</Reference>
													<Reference Name="Curve">
														<Custom>
															<Curve Name="" ID="{64243043-4FB7-4F1A-B461-766DAF7226B0}">
																<PropertyList>
																	<Property Name="Flags" Type="int32" Value="3"/>
																</PropertyList>
																<PointList>
																	<Point>
																		<XPos>0</XPos>
																		<YPos>-200</YPos>
																		<Flags>5</Flags>
																	</Point>
																	<Point>
																		<XPos>100</XPos>
																		<YPos>0</YPos>
																		<Flags>37</Flags>
																	</Point>
																</PointList>
															</Curve>
														</Custom>
													</Reference>
												</ReferenceList>
											</RTPC>
										</Local>
									</Reference>
								</ObjectList>
							</ObjectLists>
						</Bus>
						<Bus Name="Voice" ID="{F2F49575-70ED-41CB-AE0B-F252C0FD46FC}"/>
					</ChildrenList>
					<ObjectLists>
						<ObjectList Name="Effects">
							<Reference>
								<Local>
									<EffectSlot Name="" ID="{C898A674-E08E-48B3-9FEE-7E15D8036BAC}" ShortID="*********">
										<ReferenceList>
											<Reference Name="Effect">
												<Custom CreatedFrom="{2144284E-7ADD-4341-86A4-4E468BEC9A2D}">
													<Effect Name="Brick_Wall_Minus_1dB_Peak_Fast_Release (Custom)" ID="{939A6949-88FA-40F8-8D8C-028B921E0D58}" ShortID="*********" PluginName="Wwise Peak Limiter" CompanyID="0" PluginID="110" PluginType="3">
														<PropertyList>
															<Property Name="Ratio" Type="Real32" Value="50"/>
															<Property Name="ReleaseTime" Type="Real32" Value="0.02"/>
															<Property Name="Threshold" Type="Real32" Value="-1"/>
														</PropertyList>
													</Effect>
												</Custom>
											</Reference>
										</ReferenceList>
									</EffectSlot>
								</Local>
							</Reference>
						</ObjectList>
					</ObjectLists>
				</Bus>
			</ChildrenList>
		</WorkUnit>
	</Busses>
</WwiseDocument>
